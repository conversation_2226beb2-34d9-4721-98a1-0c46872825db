# **Project Plan: Interactive Portfolio for <PERSON><PERSON><PERSON>**

## **1.0 Executive Overview & Technical Strategy**

### **1.1 Project Mission**

The primary objective of this project is to construct a personal portfolio website for <PERSON><PERSON><PERSON>. This website will not only serve as a repository of his professional and academic achievements but will also function as a direct demonstration of his technical capabilities as a modern software developer. The execution must prioritize a clean, professional aesthetic, a fully responsive layout across all device viewports, and a rich, interactive user experience powered by fluid, meaningful animations. The final product must be a high-performance, production-grade web application.

### **1.2 Core Philosophy: Animation as Information Architecture**

The mandate for pervasive animation is interpreted not as a decorative requirement, but as a core architectural principle. Motion design, when executed with purpose, transcends aesthetics to become a fundamental tool for enhancing usability and narrative flow. A static webpage presents all information with a flat, uniform visual priority, leaving the user to parse the hierarchy. This project will leverage animation to actively guide the user's attention, provide meaningful feedback for interactions, and create a deliberate, story-driven journey through the content.

This philosophy will be implemented through specific techniques:

* **Sequential Revelation:** On-load animations, particularly in the Hero section, will use staggered delays. This technique reveals elements in a controlled sequence (e.g., Name, then <PERSON><PERSON>, then Calls-to-Action), establishing an immediate information hierarchy and creating a more engaging introduction than a page that simply appears.  
* **Focused Transitions:** On-scroll animations will trigger as new content sections enter the viewport. This focuses the user's attention on the newly presented information, preventing cognitive overload and creating a sense of discovery as they navigate down the page.  
* **Interaction Feedback:** Hover and click effects provide immediate, tactile confirmation of user actions. This makes the interface feel more responsive and intuitive, transforming the user from a passive reader into an active participant.

Therefore, animation will not be an afterthought applied during a final styling phase. Instead, it will be treated as an integral aspect of each component's design and structure from its inception.

### **1.3 Recommended Technology Stack**

The technology stack is selected to provide a modern, performant, and highly scalable foundation that aligns with current industry best practices and is perfectly suited to the project's specific requirements.

* **Framework: Next.js (v14+ with App Router)**  
  * **Rationale:** Next.js is chosen for its robust, production-ready features. The App Router provides a modern, intuitive foundation for component-based architecture. Its support for both Server-Side Rendering (SSR) and Static Site Generation (SSG) allows for optimal performance strategies: delivering a fast initial page load, which is critical for user retention and SEO, while maintaining the dynamic capabilities of a React application.  
* **UI Library: React (v18+)**  
  * **Rationale:** As the industry standard for building interactive user interfaces, React's component model is ideal for deconstructing the portfolio into discrete, manageable, and reusable pieces. This modularity will ensure a clean, maintainable codebase that the executing agent can assemble logically.  
* **Styling: Tailwind CSS (v3+)**  
  * **Rationale:** Tailwind CSS is a utility-first CSS framework that enables rapid development and ensures stylistic consistency. By applying utility classes directly within the JSX, it eliminates the need for context-switching between markup and separate stylesheet files. This approach keeps styling logic co-located with its corresponding component, resulting in a more efficient and readable development process. It also prevents CSS bloat by generating only the styles that are actually used.  
* **Animation: Framer Motion (v10+)**  
  * **Rationale:** To fulfill the core requirement of fluid and complex animations, Framer Motion is the ideal choice. It is a production-ready motion library for React with a declarative and intuitive API that integrates seamlessly with React's component lifecycle. Its powerful, high-level components like AnimatePresence (for animating components as they enter and exit the DOM) and its simple yet powerful props like whileInView, whileHover, and whileTap make the implementation of sophisticated scroll, hover, and click animations straightforward and maintainable.  
* **Icons: React-Icons**  
  * **Rationale:** This library provides a vast collection of high-quality SVG icons from popular icon sets. Its use abstracts away the manual process of sourcing, optimizing, and managing individual SVG assets, allowing for the easy inclusion of icons for social media links and other UI elements.

## **2.0 Phase 1: Environment Setup & Project Initialization**

This phase outlines the foundational steps for creating a clean, correctly configured development environment. Adherence to these steps is critical to prevent downstream integration issues and ensure a stable project base.

### **2.1 Initialize Next.js Project**

1. Open a terminal in the current directory.  
2. Execute the create-next-app command:  
   npx create-next-app@latest janmejay-tiwari-portfolio  
3. When prompted by the interactive setup wizard, select the following options:  
   * "Would you like to use TypeScript?" \-\> **Yes**  
   * "Would you like to use ESLint?" \-\> **Yes**  
   * "Would you like to use Tailwind CSS?" \-\> **Yes**  
   * "Would you like to use src/ directory?" \-\> **No** (for a flatter root structure)  
   * "Would you like to use App Router?" \-\> **Yes**  
   * "Would you like to customize the default import alias?" \-\> **No** (accept default @/\*)

### **2.2 Install Core Dependencies**

1. Navigate into the newly created project directory: cd janmejay-tiwari-portfolio  
2. Install the primary animation library, Framer Motion:  
   npm install framer-motion  
3. Install the icon library, React-Icons:  
   npm install react-icons

### **2.3 Configure Project Structure**

To ensure a logical and scalable architecture, create the following directories at the root of the project:

1. /components: This directory will house all React components.  
   * Create a subdirectory: /components/ui. This will contain generic, application-agnostic components that can be reused anywhere (e.g., Button.jsx).  
2. /public: This directory is for static assets that are served directly.  
   * Create a subdirectory: /public/assets.  
   * Create a subdirectory: /public/assets/images. This will store all static images, such as the personal photo and company logos.  
   * Create a subdirectory: /public/assets/docs. This will store documents like the resume PDF.  
3. /lib: This directory will contain helper functions, utility scripts, and centralized data definitions.
### **2.4 Configure Tailwind CSS**

Centralizing design tokens is a critical architectural step that establishes a consistent and maintainable design system from the outset. This practice prevents the use of arbitrary values and makes global style modifications efficient.

1. Open the tailwind.config.ts file located at the project root.  
2. Modify the theme.extend object to include a custom color palette and any other design tokens. This creates a semantic and centralized theme for the entire application.  
   **Example Configuration:**  
   TypeScript  
   // tailwind.config.ts  
   const config: Config \= {  
     content: \[  
       './pages/\*\*/\*.{js,ts,jsx,tsx,mdx}',  
       './components/\*\*/\*.{js,ts,jsx,tsx,mdx}',  
       './app/\*\*/\*.{js,ts,jsx,tsx,mdx}',  
     \],  
     theme: {  
       extend: {  
         colors: {  
           background: '\#0a192f', // A dark navy blue  
           textPrimary: '\#ccd6f6', // A light, muted blue/grey  
           textSecondary: '\#8892b0', // A darker grey for secondary text  
           accent: '\#64ffda', // A bright teal/mint for highlights and CTAs  
           card: '\#112240', // A slightly lighter navy for card backgrounds  
         },  
         fontFamily: {  
           // Define custom font families if needed  
         },  
       },  
     },  
     plugins:,  
   };  
   export default config;


## **3.0 Phase 2: Global Layout & Core UI Components**

This phase focuses on creating the application's foundational shell and the reusable UI elements that will ensure consistency across all sections.

### **3.1 Root Layout Configuration**

The root layout defines the global styles and structure for every page in the application.

1. Navigate to and open the app/layout.tsx file.  
2. Import a professional, modern font from next/font/google. The 'Inter' font is recommended for its excellent readability.  
3. Instantiate the font and apply its class name to the \<body\> tag.  
4. Apply the base background and text colors defined in the Tailwind config to the \<body\> tag. This ensures all pages inherit the correct default styling.

### **3.2 Data & Asset Manifest Implementation**

Decoupling content from presentation is a core software engineering principle that enhances maintainability. A centralized data file will act as a mock API for the application.

1. Create a new file: /lib/data.ts.  
2. Transcribe the entire **Appendix A: Data & Asset Manifest** into exported JavaScript constants (objects and arrays) within this file.  
3. Each component will import its required data directly from this file, referencing the unique ItemID. This makes content updates trivial, as they only need to be made in this single location. The data is sourced from the provided resume and subsequent analysis.

### **3.3 Reusable Button Component**

Creating a generic, reusable Button component is fundamental to component-driven development. It guarantees visual and behavioral consistency for all interactive buttons and centralizes animation logic.

1. Create the component file: /components/ui/Button.tsx.  
2. **Component Signature:** The component should accept props for different visual styles (variant: 'primary' | 'outline'), an onClick handler, a potential href for linking, and children for the button's text content.  
3. **Component Structure:** The component will be a motion.button element from Framer Motion to enable built-in animation props.  
4. **Styling:** Use Tailwind CSS utility classes to define the base styles (padding, font size, border radius, transition properties) and the variant-specific styles (colors, borders).  
5. **Animation Implementation:**  
   * **On-Hover:** Implement a subtle lift-and-glow effect using the whileHover prop. Example: whileHover={{ scale: 1.05, boxShadow: '0 0 8px rgba(100, 255, 218, 0.5)' }}.  
   * **On-Click:** Implement immediate tactile feedback using the whileTap prop to slightly shrink the button. Example: whileTap={{ scale: 0.95 }}.

## **4.0 Phase 3: Hero Section Development**

This section is the user's first impression of the site and must be visually impactful, clear, and engaging.

### **4.1 Component Creation & Data Sourcing**

1. Create the component file: /components/Hero.tsx.  
2. Within the component, import the necessary data objects from /lib/data.ts, referencing the ItemIDs for the name, bio, social links, and resume link (e.g., HERO-01 through HERO-09).

### **4.2 JSX Structure**

1. Use a flexbox or grid layout for the main section container to position the content.  
2. Create two main child containers: one for the textual content (Name, Bio, Buttons, Icons) and one for the personal image.  
3. Render the name (\<h1\>), bio (\<p\>), and other text elements.  
4. Use the Next.js \<Image\> component to render the personal photo (HERO-03) for optimized loading and performance.  
5. Render two instances of the reusable \<Button\> component for 'View Work' and 'View Resume'.  
6. Map over the social links data to render the social media and contact icons. Each icon should be from the react-icons library and wrapped in an \<a\> tag with the appropriate href.

### **4.3 Styling**

1. Apply Tailwind CSS classes to create a strong visual hierarchy. Use a large, bold font for the name, a smaller, secondary-colored font for the bio, and ensure generous spacing between elements.  
2. Implement responsive design using Tailwind's breakpoint prefixes (e.g., md:flex-row) to transition the layout from a side-by-side view on desktop to a stacked, vertical view on mobile devices.

### **4.4 Animation & Interactivity Implementation**

1. **On-Load Staggered Animation:** This is a critical sequence for guiding the user's eye.  
   * The main section container will be a motion.section.  
   * Define animation variants for the container and its children.  
   * The container variant will include a transition property with a staggerChildren value (e.g., 0.1).  
   * Each child element (name, bio, buttons, icon group) will be a motion element (e.g., motion.h1) and will have a common animation variant, such as a fade-in and slide-up effect: initial={{ opacity: 0, y: 20 }} and animate={{ opacity: 1, y: 0 }}.  
   * Applying these variants to the container will orchestrate a fluid, cascading reveal of the content.  
2. **Button Interactivity:** The 'View Work' and 'View Resume' buttons will automatically inherit the hover and click animations from the reusable Button component.  
3. **Icon Interactivity:** The social media icons will have their own distinct whileHover animation, such as scaling up and changing color to the defined accent color: whileHover={{ scale: 1.2, color: 'var(--color-accent)' }}.  
4. **Scroll-to Functionality:**  
   * The 'View Work' button's onClick handler will execute a JavaScript function that finds the work experience section by its ID and calls element.scrollIntoView({ behavior: 'smooth' }).  
   * The contact icon will use the same smooth scroll mechanism to navigate to the contact section.  
   * The 'View Resume' button will be an \<a\> tag pointing to the resume PDF path (HERO-06) with target="\_blank" to open it in a new browser tab.1

## **5.0 Phase 4: Work Experience Section**

This section details professional history. The design and animation should convey organization, clarity, and substance.

### **5.1 Component Creation & Data Sourcing**

1. Create a parent component: /components/WorkExperience.tsx.  
2. Create a child component: /components/ExperienceCard.tsx.  
3. The parent WorkExperience.tsx component will import the work experience data array from /lib/data.ts.1  
4. It will then map over this array, rendering an ExperienceCard for each job and passing the corresponding job data object as props.

### **5.2 ExperienceCard.tsx Structure & Styling**

1. The card's root element will be a motion.div.  
2. **Structure:** The card will be structured with a clear visual flow:  
   * Top: Company Logo and Company Name.  
   * Middle: Role and Timeline.  
   * Body: An unordered list (\<ul\>) of key responsibilities/achievements.  
   * Bottom: A 'View Certificate' button, which will be an instance of the reusable \<Button\> component.  
3. **Assets:** Use the Next.js \<Image\> component for company logos, sourcing the paths from the props (e.g., EXP-02, EXP-08). The logos themselves are to be sourced based on web research.2  
4. **Styling:** Use Tailwind CSS to create a clean, well-padded card with a distinct background color (bg-card). Use flexbox or grid to align elements properly.

### **5.3 Animation & Interactivity Implementation**

1. **On-Scroll Section Reveal:** The parent WorkExperience.tsx container will be a motion.section. The section title (\<h2\>) will have its own motion animation using the whileInView prop to fade in as it becomes visible. The viewport={{ once: true, amount: 0.2 }} configuration ensures the animation triggers once when 20% of the element is visible.  
2. **On-Scroll Individual Card Reveal:** Within the parent's .map() function, each ExperienceCard component will have its own whileInView animation. For example: initial={{ opacity: 0, x: \-50 }}, whileInView={{ opacity: 1, x: 0 }}, transition={{ duration: 0.5 }}. This approach makes each card animate into view individually as the user scrolls, creating a more dynamic and engaging effect than revealing the entire list at once.  
3. **On-Hover Card Effect:** The root motion.div of the ExperienceCard will have a whileHover prop to create a subtle lift effect, inviting interaction. Example: whileHover={{ y: \-5, boxShadow: '0px 10px 30px \-15px rgba(2,12,27,0.7)' }}.  
4. **Button Interactivity:** The 'View Certificate' button will inherit its animations from the base Button component and will link to a placeholder URL as specified in the data manifest.

## **6.0 Phase 5: Project Work Section**

This section is a critical showcase of practical skills and tangible accomplishments. A grid layout is effective for displaying multiple projects concisely.

### **6.1 Component Creation & Data Sourcing**

1. Create a parent component: /components/Projects.tsx.  
2. Create a child component: /components/ProjectCard.tsx.  
3. The Projects.tsx component will import the project data array from /lib/data.ts.1  
4. It will map over this data, rendering a ProjectCard for each project and passing the data object as props.

### **6.2 ProjectCard.tsx Structure & Styling**

1. The card's root element will be a motion.div.  
2. **Structure:** The card will display:  
   * Project Name (\<h3\>).  
   * A brief description covering the project's purpose and use cases (\<p\>).  
   * A list or flexbox container of "pills" for the Tech Stack. Each pill will be a small, styled \<span\> or \<div\>.  
   * A section titled "What I Learned" with a short paragraph.  
   * An icon link (e.g., GitHub icon) to the project repository.  
3. **Styling:** Use Tailwind CSS to create a modern, clean card layout. The tech stack pills are a key visual element and should be styled distinctly with a different background color and small padding to make them stand out.

### **6.3 Animation & Interactivity Implementation**

1. **On-Scroll Section Reveal:** As with previous sections, the main Projects.tsx container will animate its title (\<h2\>) into view using the whileInView prop.  
2. **Staggered Grid Animation:** Animating a grid of items requires a more sophisticated approach than a simple list to achieve an elegant effect.  
   * The grid container in Projects.tsx (e.g., \<motion.div className="grid..."\>) will be configured with animation variants.  
   * The container's variant will define a transition property with staggerChildren, for example: staggerChildren: 0.1.  
   * The ProjectCard component will be a motion.div with a corresponding child variant, such as a scale-up and fade-in effect: initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}.  
   * By setting initial="hidden" and whileInView="visible" on the grid container, Framer Motion will automatically orchestrate the staggered animation, causing the cards to appear in a cascading sequence when the grid scrolls into view.  
3. **On-Hover Card Effect:** The ProjectCard will have a whileHover prop with a more pronounced lift effect (e.g., y: \-8) to signify it as a primary portfolio piece. The GitHub icon within the card should also have its own distinct hover effect for enhanced interactivity.

## **7.0 Phase 6: Dual Education & Certificates Section**

This section's unique tab-based functionality provides an excellent opportunity to demonstrate proficiency with React state management and complex UI animations.

### **7.1 Component Creation & State Management**

1. Create a main stateful component: /components/EducationAndCerts.tsx.  
2. Create two stateless child view components: /components/CertificatesView.tsx and /components/EducationView.tsx.  
3. In the parent EducationAndCerts.tsx component, implement state to manage the active view using the useState hook: const \= useState('certificates');.

### **7.2 JSX Structure & Styling**

1. The parent component will contain two \<button\> elements, 'Certificates' and 'Education'. Their onClick handlers will update the activeTab state.  
2. The styling of these buttons must be dynamic, changing based on the activeTab state to visually indicate which tab is currently selected (e.g., by changing the text color or adding a border-bottom).  
3. Below the buttons, implement the conditional rendering logic. This will be wrapped in Framer Motion's \<AnimatePresence\> component.  
4. CertificatesView.tsx will receive the certificate data from /lib/data.ts 1 and map over it, displaying each certificate's name, issuing company (name and logo), and platform. Logos for issuers like IBM, AWS, Google, and Deeplearning.AI will be sourced from web research.4  
5. EducationView.tsx will receive and display the static education data from /lib/data.ts.1

### **7.3 Animation & Interactivity Implementation**

1. **On-Click Tab Transition:** The smooth transition between the two views is the primary technical challenge and a key feature of this section.  
   * Wrap the conditional rendering block in the \<AnimatePresence mode='wait'\> component. The mode='wait' prop is crucial; it ensures that the outgoing component completes its exit animation *before* the incoming component begins its enter animation, preventing a visual clash and ensuring a clean transition.  
   * Both CertificatesView.tsx and EducationView.tsx must have a root element of motion.div.  
   * Define initial, animate, and exit animation props for both components. A horizontal slide-and-fade effect is recommended. For example:  
     * initial={{ opacity: 0, x: 20 }}  
     * animate={{ opacity: 1, x: 0 }}  
     * exit={{ opacity: 0, x: \-20 }}  
   * When the activeTab state changes, AnimatePresence will detect the component swap. It will apply the exit animation to the component being removed from the DOM and the initial/animate sequence to the new component being added, creating a seamless and professional transition.  
2. **On-Scroll Section Reveal:** The entire EducationAndCerts.tsx parent component will have a standard whileInView fade-in animation applied to its root element.

## **8.0 Phase 7: Contact & Footer Sections**

These final sections provide essential information and a professional conclusion to the user's journey.

### **8.1 Component Creation & Structure**

1. Create the component files: /components/Contact.tsx and /components/Footer.tsx.  
2. **Contact Section:**  
   * Structure: A heading (e.g., "Get In Touch"), a short call-to-action paragraph, the email address, phone number, and the social media icons rendered again for user convenience.  
   * The email address should be wrapped in a mailto: anchor tag: \<a href="mailto:<EMAIL>"\>.  
   * Data will be sourced from the manifest.1  
3. **Footer Section:**  
   * A simple component containing two lines of text as specified in the user query and data manifest (FOOTER-01, FOOTER-02).

### **8.2 Styling**

1. Use Tailwind CSS to style the Contact section with centered text and ample vertical padding to create a distinct section.  
2. The Footer should be styled to be simple and unobtrusive, with a smaller font size and a subtle text color (text-textSecondary).

### **8.3 Animation & Interactivity**

1. **On-Scroll Reveal:** Both the Contact and Footer sections will have a simple whileInView fade-in animation applied to their root containers.  
2. **Icon Interactivity:** The social media icons in the Contact section must reuse the exact same whileHover animation effects defined for the Hero section to maintain design consistency throughout the application.

## **9.0 Phase 8: Final Assembly & Deployment Readiness**

This final phase integrates all components and prepares the application for production deployment.

### **9.1 Page Assembly**

1. Navigate to and open the main page file: app/page.tsx.  
2. Import all the main section components created in the previous phases (Hero, WorkExperience, Projects, EducationAndCerts, Contact, Footer).  
3. Render these components in the correct sequential order within the page's return statement.  
4. Assign a unique id attribute to the root container of each major section (e.g., \<section id="work-experience"\>). These IDs will serve as the targets for the smooth-scroll navigation links initiated from the Hero section.

### **9.2 Responsiveness & Performance Audit**

1. Conduct a comprehensive review of the entire application across a range of simulated device breakpoints: mobile (e.g., 375px), tablet (e.g., 768px), desktop (e.g., 1024px), and large desktop (e.g., 1440px).  
2. Ensure all layouts reflow correctly and that no elements are broken or overlapping.  
3. Verify that all animations are fluid and performant on each breakpoint.  
4. Use browser developer tools to analyze performance. Confirm that animations are primarily manipulating the transform and opacity CSS properties, as these are typically hardware-accelerated and less likely to cause performance issues than animating properties that trigger layout reflows (e.g., width, height, margin).

### **9.3 Accessibility (a11y) Audit**

1. **Keyboard Navigation:** Ensure all interactive elements (buttons, links, icons, tabs) are focusable and operable using only the keyboard. The focus order must be logical.  
2. **Semantic HTML:** Verify that semantic HTML5 tags (\<main\>, \<section\>, \<nav\>, \<h1\>, etc.) are used correctly to define the page structure.  
3. **ARIA Attributes:** Add appropriate ARIA (Accessible Rich Internet Applications) attributes where necessary, especially for dynamic components like the tabbed interface, to communicate state changes to assistive technologies.  
4. **Color Contrast:** Use a contrast checker tool to ensure that all text has sufficient color contrast against its background, meeting WCAG AA standards.  
5. **Image Alt Text:** Ensure all meaningful images rendered with the Next.js \<Image\> component have descriptive alt text.

### **9.4 Deployment**

1. Initialize a Git repository in the project folder and commit all files.  
2. Create a new repository on a Git provider (e.g., GitHub).  
3. Push the local repository to the remote provider.  
4. Sign up for a hosting platform optimized for Next.js, such as Vercel or Netlify.  
5. Connect the hosting platform to the GitHub repository. The platform will typically auto-detect the Next.js framework and configure the build and deployment settings automatically, enabling continuous deployment on every push to the main branch.

## **Appendix A: Data & Asset Manifest**

This manifest serves as the single source of truth for all content and static assets required for the project. It decouples data from the UI components, which is a crucial best practice for creating maintainable and scalable applications. The executing agent must use this manifest to populate all content fields accurately.

**Table 1: Data & Asset Manifest**

| ItemID | Section | ElementType | Content/Placeholder | Source |
| :---- | :---- | :---- | :---- | :---- |
| **Hero Section** |  |  |  |  |
| HERO-01 | Hero | Name | Janmejay Tiwari | 1 |
| HERO-02 | Hero | Bio | A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies. | 1 |
| HERO-03 | Hero | Image | /assets/images/janmejay\_tiwari\_profile.jpg | User Provided |
| HERO-04 | Hero | Button 1 Text | View Work | User Query |
| HERO-05 | Hero | Button 2 Text | View Resume | User Query |
| HERO-06 | Hero | Resume Link | /assets/docs/Resume\_Janmejay\_Tiwari.pdf | 1 |
| HERO-07 | Hero | Social Link: GitHub | https://github.com/Janmejay3108 | 1 |
| HERO-08 | Hero | Social Link: LinkedIn | https://linkedin.com/in/janmejay-tiwari | 1 |
| HERO-09 | Hero | Social Link: Twitter | https://twitter.com/placeholder\_handle | User Query |
| **Work Experience** |  |  |  |  |
| EXP-01 | Work Experience | Job 1: Company Name | Celebal Technologies | 1 |
| EXP-02 | Work Experience | Job 1: Company Logo | /assets/images/logo\_celebal.svg | 2 |
| EXP-03 | Work Experience | Job 1: Role | React Developer Intern | 1 |
| EXP-04 | Work Experience | Job 1: Timeline | May 2025 \- July 2025 | 1 |
| EXP-05 | Work Experience | Job 1: Details | • Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement. • Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS. | 1 |
| EXP-06 | Work Experience | Job 1: Certificate Link | \# | 1 |
| EXP-07 | Work Experience | Job 2: Company Name | 1stop.ai | 1 |
| EXP-08 | Work Experience | Job 2: Company Logo | /assets/images/logo\_1stopai.svg | 3 |
| EXP-09 | Work Experience | Job 2: Role | Frontend developer Intern | 1 |
| EXP-10 | Work Experience | Job 2: Details | • Collaborated with a team to develop responsive web application using MERN stack. • Implemented frontend components using React.js. • Assisted in database design and implementation with Firebase. • Integrated AWS S3 for media storage and content delivery. | 1 |
| EXP-11 | Work Experience | Job 2: Certificate Link | \# | 1 |
| **Project Work** |  |  |  |  |
| PROJ-01 | Project Work | Project 1: Name | Web accessibility analyser | 1 |
| PROJ-02 | Project Work | Project 1: Purpose | A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions. | 1 |
| PROJ-03 | Project Work | Project 1: Tech Stack | \`\` | 1 |
| PROJ-04 | Project Work | Project 1: Learnings | Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers. | Inferred |
| PROJ-05 | Project Work | Project 1: Link | https://github.com/Janmejay3108 | 1 |
| PROJ-06 | Project Work | Project 2: Name | Secure File Sharing Platform | 1 |
| PROJ-07 | Project Work | Project 2: Purpose | A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI. | 1 |
| PROJ-08 | Project Work | Project 2: Tech Stack | \`\` | 1 |
| PROJ-09 | Project Work | Project 2: Learnings | Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker. | Inferred |
| PROJ-10 | Project Work | Project 2: Link | https://github.com/Janmejay3108 | 1 |
| **Certificates** |  |  |  |  |
| CERT-01 | Certificates | Cert 1: Name | AWS Cloud Solutions Architect | 1 |
| CERT-02 | Certificates | Cert 1: Issuer Name | Amazon Web Services | 1 |
| CERT-03 | Certificates | Cert 1: Issuer Logo | /assets/images/logo\_aws.svg | 13 |
| CERT-04 | Certificates | Cert 2: Name | Full Stack Developer | 1 |
| CERT-05 | Certificates | Cert 2: Issuer Name | IBM | 1 |
| CERT-06 | Certificates | Cert 2: Issuer Logo | /assets/images/logo\_ibm.svg | 4 |
| CERT-07 | Certificates | Cert 3: Name | Database and SQL for data science with Python | 1 |
| CERT-08 | Certificates | Cert 3: Issuer Name | IBM | 1 |
| CERT-09 | Certificates | Cert 3: Issuer Logo | /assets/images/logo\_ibm.svg | 4 |
| CERT-10 | Certificates | Cert 4: Name | RAG and Agentic AI | 1 |
| CERT-11 | Certificates | Cert 4: Issuer Name | IBM | 1 |
| CERT-12 | Certificates | Cert 4: Issuer Logo | /assets/images/logo\_ibm.svg | 4 |
| CERT-13 | Certificates | Cert 5: Name | Artificial Intelligence Essentials | 1 |
| CERT-14 | Certificates | Cert 5: Issuer Name | Google | 1 |
| CERT-15 | Certificates | Cert 5: Issuer Logo | /assets/images/logo\_google.svg | 5 |
| CERT-16 | Certificates | Cert 6: Name | Convolutional Neural Networks | 1 |
| CERT-17 | Certificates | Cert 6: Issuer Name | Deeplearning.AI | 1 |
| CERT-18 | Certificates | Cert 6: Issuer Logo | /assets/images/logo\_deeplearning\_ai.svg | 7 |
| **Education** |  |  |  |  |
| EDU-01 | Education | Degree | BTech in Electronics and Communication Engineering | 1 |
| EDU-02 | Education | Institution | Institute of Engineering and Management, Kolkata | 1 |
| EDU-03 | Education | Timeline | 2022-2026 | 1 |
| EDU-04 | Education | Detail | CGPA: 9.38 | 1 |
| **Contact & Footer** |  |  |  |  |
| CONTACT-01 | Contact | Email | <EMAIL> | 1 |
| CONTACT-02 | Contact | Phone | \+91 9163083482 | 1 |
| FOOTER-01 | Footer | Line 1 | Janmejay Tiwari | User Query |
| FOOTER-02 | Footer | Line 2 | @copyright 2025\. All Rights Reserved. | User Query |

#### 