"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ExperienceCard.tsx":
/*!***************************************!*\
  !*** ./components/ExperienceCard.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExperienceCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Animation variants for the card - Following step 5.3 specification\nconst cardVariants = {\n    hidden: {\n        opacity: 0,\n        x: -50\n    },\n    visible: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.5,\n            ease: 'easeOut'\n        }\n    }\n};\n// Animation variants for the details list\nconst listVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst listItemVariants = {\n    hidden: {\n        opacity: 0,\n        x: -20\n    },\n    visible: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.4,\n            ease: 'easeOut'\n        }\n    }\n};\nfunction ExperienceCard(param) {\n    let { experience, index } = param;\n    const { companyName, companyLogo, role, timeline, details, certificateLink } = experience;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        variants: cardVariants,\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true,\n            amount: 0.3\n        },\n        className: \"bg-card border border-border rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300\",\n        whileHover: {\n            y: -5,\n            transition: {\n                duration: 0.2\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 relative bg-white rounded-lg p-2 shadow-sm flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: companyLogo,\n                            alt: \"\".concat(companyName, \" logo\"),\n                            fill: true,\n                            className: \"object-contain\",\n                            sizes: \"64px\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl md:text-2xl font-bold text-textPrimary\",\n                        children: companyName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg md:text-xl font-semibold text-accent\",\n                        children: role\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-textSecondary font-medium\",\n                        children: timeline\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.ul, {\n                variants: listVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    amount: 0.5\n                },\n                className: \"space-y-3 mb-6\",\n                children: details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.li, {\n                        variants: listItemVariants,\n                        className: \"flex items-start gap-3 text-textSecondary leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: detail\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, detailIndex, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            certificateLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    delay: 0.4,\n                    duration: 0.3\n                },\n                className: \"flex justify-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    variant: \"outline\",\n                    href: certificateLink,\n                    className: \"text-sm\",\n                    children: \"View Certificate\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_c = ExperienceCard;\nvar _c;\n$RefreshReg$(_c, \"ExperienceCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ExperienceCard.tsx\n"));

/***/ })

});