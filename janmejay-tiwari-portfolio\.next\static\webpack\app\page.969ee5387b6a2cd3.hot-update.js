"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/WorkExperience.tsx":
/*!***************************************!*\
  !*** ./components/WorkExperience.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkExperience)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./lib/data.ts\");\n/* harmony import */ var _ExperienceCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ExperienceCard */ \"(app-pages-browser)/./components/ExperienceCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Animation variants for the section\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2,\n            delayChildren: 0.1\n        }\n    }\n};\nconst titleVariants = {\n    hidden: {\n        opacity: 0,\n        y: 30\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: 'easeOut'\n        }\n    }\n};\nfunction WorkExperience() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.section, {\n        id: \"work-experience\",\n        className: \"py-20 bg-background\",\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true,\n            amount: 0.2\n        },\n        variants: containerVariants,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                        className: \"text-4xl md:text-5xl font-bold text-textPrimary mb-4 text-center\",\n                        variants: titleVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true,\n                            amount: 0.2\n                        },\n                        children: \"Work Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                        className: \"text-lg text-textSecondary max-w-2xl mx-auto text-center mb-16\",\n                        variants: titleVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true,\n                            amount: 0.2\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        children: \"Professional journey and key contributions in software development\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.workExperience.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExperienceCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                experience: experience,\n                                index: index\n                            }, experience.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = WorkExperience;\nvar _c;\n$RefreshReg$(_c, \"WorkExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/WorkExperience.tsx\n"));

/***/ })

});