'use client';

import { heroData, workExperience, education, projects, certificates } from "@/lib/data";
import Button from "@/components/ui/Button";
import Hero from "@/components/Hero";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero />

      {/* Temporary sections for testing scroll functionality */}
      <section id="work-experience" className="min-h-screen bg-card/50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-textPrimary mb-4">Work Experience</h2>
          <p className="text-textSecondary">This section will be implemented in Phase 4</p>
        </div>
      </section>

      <section id="contact" className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-textPrimary mb-4">Contact</h2>
          <p className="text-textSecondary">This section will be implemented in a later phase</p>
        </div>
      </section>

      {/* Development Testing Section */}
      <div className="p-8">
        <main className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-4 text-accent">Development Testing</h1>

          {/* Button Component Test */}
          <section className="bg-card p-6 rounded-lg mb-8">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Button Component Test</h2>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" onClick={() => alert('Primary button clicked!')}>
                Primary Button
              </Button>
              <Button variant="outline" onClick={() => alert('Outline button clicked!')}>
                Outline Button
              </Button>
              <Button variant="primary" href="#work-experience">
                Link Button
              </Button>
              <Button variant="outline" disabled>
                Disabled Button
              </Button>
            </div>
          </section>

        <div className="space-y-8">
          {/* Hero Data Test */}
          <section className="bg-card p-6 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Hero Data</h2>
            <p className="text-textSecondary mb-2"><strong>Name:</strong> {heroData.name}</p>
            <p className="text-textSecondary mb-2"><strong>Bio:</strong> {heroData.bio}</p>
            <p className="text-textSecondary mb-2"><strong>GitHub:</strong> {heroData.socialLinks.github}</p>
          </section>

          {/* Work Experience Test */}
          <section className="bg-card p-6 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Work Experience</h2>
            {workExperience.map((job) => (
              <div key={job.id} className="mb-4 p-4 bg-background rounded">
                <h3 className="text-lg font-medium text-accent">{job.companyName}</h3>
                <p className="text-textSecondary">{job.role} | {job.timeline}</p>
              </div>
            ))}
          </section>

          {/* Projects Test */}
          <section className="bg-card p-6 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Projects ({projects.length})</h2>
            {projects.map((project) => (
              <div key={project.id} className="mb-4 p-4 bg-background rounded">
                <h3 className="text-lg font-medium text-accent">{project.name}</h3>
                <p className="text-textSecondary mb-2">{project.purpose}</p>
                <p className="text-textSecondary mb-2"><strong>Learnings:</strong> {project.learnings}</p>
                <p className="text-textSecondary"><strong>GitHub:</strong> {project.githubLink}</p>
              </div>
            ))}
          </section>

          {/* Certificates Test */}
          <section className="bg-card p-6 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Certificates ({certificates.length})</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {certificates.map((cert) => (
                <div key={cert.id} className="p-4 bg-background rounded">
                  <h3 className="text-lg font-medium text-accent">{cert.name}</h3>
                  <p className="text-textSecondary">Issued by: {cert.issuerName}</p>
                </div>
              ))}
            </div>
          </section>

          {/* Education Test */}
          <section className="bg-card p-6 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4 text-textPrimary">Education</h2>
            <p className="text-textSecondary mb-2"><strong>Degree:</strong> {education.degree}</p>
            <p className="text-textSecondary mb-2"><strong>Institution:</strong> {education.institution}</p>
            <p className="text-textSecondary mb-2"><strong>Timeline:</strong> {education.timeline}</p>
            <p className="text-textSecondary"><strong>CGPA:</strong> {education.detail}</p>
          </section>
        </div>
        </main>
      </div>
    </div>
  );
}
