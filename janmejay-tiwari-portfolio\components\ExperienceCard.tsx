'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaExternalLinkAlt } from 'react-icons/fa';
import { WorkExperience } from '@/lib/data';

interface ExperienceCardProps {
  experience: WorkExperience;
  index: number;
}

// Animation variants for the card
const cardVariants = {
  hidden: { 
    opacity: 0, 
    y: 50,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

// Animation variants for the details list
const listVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const listItemVariants = {
  hidden: { 
    opacity: 0, 
    x: -20 
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};

export default function ExperienceCard({ experience, index }: ExperienceCardProps) {
  const { companyName, companyLogo, role, timeline, details, certificateLink } = experience;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      className="bg-card border border-border rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
      whileHover={{ 
        y: -5,
        transition: { duration: 0.2 }
      }}
    >
      <div className="flex flex-col md:flex-row gap-6">
        {/* Company Logo */}
        <div className="flex-shrink-0">
          <div className="w-16 h-16 md:w-20 md:h-20 relative bg-white rounded-lg p-2 shadow-sm">
            <Image
              src={companyLogo}
              alt={`${companyName} logo`}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 64px, 80px"
            />
          </div>
        </div>

        {/* Experience Content */}
        <div className="flex-1 space-y-4">
          {/* Header: Role and Company */}
          <div className="space-y-2">
            <h3 className="text-xl md:text-2xl font-bold text-textPrimary">
              {role}
            </h3>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <p className="text-lg font-medium text-accent">
                {companyName}
              </p>
              <p className="text-sm text-textSecondary font-medium">
                {timeline}
              </p>
            </div>
          </div>

          {/* Experience Details */}
          <motion.ul
            variants={listVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.5 }}
            className="space-y-3"
          >
            {details.map((detail, detailIndex) => (
              <motion.li
                key={detailIndex}
                variants={listItemVariants}
                className="flex items-start gap-3 text-textSecondary leading-relaxed"
              >
                <span className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <span>{detail}</span>
              </motion.li>
            ))}
          </motion.ul>

          {/* Certificate Link */}
          {certificateLink && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.3 }}
              className="pt-4"
            >
              <motion.a
                href={certificateLink}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-accent hover:text-accent/80 font-medium text-sm transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaExternalLinkAlt size={12} />
                View Certificate
              </motion.a>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
