'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { WorkExperience } from '@/lib/data';
import Button from '@/components/ui/Button';

interface ExperienceCardProps {
  experience: WorkExperience;
  index: number;
}

// Animation variants for the card - Following step 5.3 specification
const cardVariants = {
  hidden: {
    opacity: 0,
    x: -50
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

// Animation variants for the details list
const listVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const listItemVariants = {
  hidden: { 
    opacity: 0, 
    x: -20 
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};

export default function ExperienceCard({ experience, index }: ExperienceCardProps) {
  const { companyName, companyLogo, role, timeline, details, certificateLink } = experience;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      className="bg-card border border-border rounded-xl p-8 shadow-lg"
      whileHover={{
        y: -5,
        boxShadow: '0px 10px 30px -15px rgba(2,12,27,0.7)',
        transition: { duration: 0.2 }
      }}
    >
      {/* TOP: Company Logo and Company Name */}
      <div className="flex items-center gap-4 mb-6">
        <div className="w-16 h-16 relative bg-white rounded-lg p-2 shadow-sm flex-shrink-0">
          <Image
            src={companyLogo}
            alt={`${companyName} logo`}
            fill
            className="object-contain"
            sizes="64px"
          />
        </div>
        <h3 className="text-xl md:text-2xl font-bold text-textPrimary">
          {companyName}
        </h3>
      </div>

      {/* MIDDLE: Role and Timeline */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6">
        <h4 className="text-lg md:text-xl font-semibold text-accent">
          {role}
        </h4>
        <p className="text-sm text-textSecondary font-medium">
          {timeline}
        </p>
      </div>

      {/* BODY: Unordered list of key responsibilities/achievements */}
      <motion.ul
        variants={listVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.5 }}
        className="space-y-3 mb-6"
      >
        {details.map((detail, detailIndex) => (
          <motion.li
            key={detailIndex}
            variants={listItemVariants}
            className="flex items-start gap-3 text-textSecondary leading-relaxed"
          >
            <span className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></span>
            <span>{detail}</span>
          </motion.li>
        ))}
      </motion.ul>

      {/* BOTTOM: View Certificate Button */}
      {certificateLink && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.3 }}
          className="flex justify-start"
        >
          <Button
            variant="outline"
            href={certificateLink}
            className="text-sm"
          >
            View Certificate
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}
