'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaGithub, FaLinkedin, FaTwitter } from 'react-icons/fa';
import Button from '@/components/ui/Button';
import { heroData } from '@/lib/data';

// Animation variants for staggered reveal
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { 
    opacity: 0, 
    y: 20 
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

// Social media icon mapping
const socialIcons = {
  github: FaGithub,
  linkedin: FaLinkedin,
  twitter: FaTwitter,
};

export default function Hero() {
  // Smooth scroll to work experience section
  const scrollToWork = () => {
    const workSection = document.getElementById('work-experience');
    if (workSection) {
      workSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Smooth scroll to contact section
  const scrollToContact = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <motion.section
      className="min-h-screen flex items-center justify-center px-6 py-12"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-6xl mx-auto w-full">
        {/* Main content container with responsive layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          
          {/* Text content container */}
          <div className="space-y-8 order-2 md:order-1">
            {/* Name */}
            <motion.h1 
              className="text-4xl md:text-6xl font-bold text-textPrimary leading-tight"
              variants={itemVariants}
            >
              {heroData.name}
            </motion.h1>

            {/* Bio */}
            <motion.p 
              className="text-lg md:text-xl text-textSecondary leading-relaxed max-w-lg"
              variants={itemVariants}
            >
              {heroData.bio}
            </motion.p>

            {/* Action buttons */}
            <motion.div 
              className="flex flex-col sm:flex-row gap-4"
              variants={itemVariants}
            >
              <Button 
                variant="primary" 
                onClick={scrollToWork}
                className="w-full sm:w-auto"
              >
                {heroData.buttons.viewWork}
              </Button>
              
              <Button 
                variant="outline"
                href={heroData.resumeLink}
                className="w-full sm:w-auto"
              >
                {heroData.buttons.viewResume}
              </Button>
            </motion.div>

            {/* Social media icons */}
            <motion.div 
              className="flex gap-6"
              variants={itemVariants}
            >
              {Object.entries(heroData.socialLinks).map(([platform, url]) => {
                const IconComponent = socialIcons[platform as keyof typeof socialIcons];
                
                return (
                  <motion.a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-textSecondary hover:text-accent transition-colors duration-200"
                    whileHover={{ 
                      scale: 1.2, 
                      color: 'var(--accent)' 
                    }}
                    whileTap={{ scale: 0.9 }}
                    transition={{
                      type: 'spring',
                      stiffness: 400,
                      damping: 17,
                    }}
                  >
                    <IconComponent size={28} />
                    <span className="sr-only">{platform}</span>
                  </motion.a>
                );
              })}
              
              {/* Contact icon with scroll functionality */}
              <motion.button
                onClick={scrollToContact}
                className="text-textSecondary hover:text-accent transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background rounded"
                whileHover={{ 
                  scale: 1.2, 
                  color: 'var(--accent)' 
                }}
                whileTap={{ scale: 0.9 }}
                transition={{
                  type: 'spring',
                  stiffness: 400,
                  damping: 17,
                }}
                aria-label="Scroll to contact section"
              >
                <svg 
                  width="28" 
                  height="28" 
                  viewBox="0 0 24 24" 
                  fill="currentColor"
                  className="inline-block"
                >
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
                <span className="sr-only">Contact</span>
              </motion.button>
            </motion.div>
          </div>

          {/* Profile image container */}
          <motion.div 
            className="flex justify-center order-1 md:order-2"
            variants={itemVariants}
          >
            <div className="relative">
              {/* Decorative background element */}
              <div className="absolute inset-0 bg-accent/10 rounded-full blur-3xl scale-110 -z-10"></div>
              
              {/* Profile image */}
              <div className="relative w-80 h-80 md:w-96 md:h-96 rounded-full overflow-hidden border-4 border-accent/20 shadow-2xl">
                <Image
                  src={heroData.profileImage}
                  alt={`${heroData.name} - Profile Photo`}
                  fill
                  className="object-cover"
                  priority
                  sizes="(max-width: 768px) 320px, 384px"
                />
              </div>
              
              {/* Animated accent ring */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-accent/30"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.3, 0.6, 0.3],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
