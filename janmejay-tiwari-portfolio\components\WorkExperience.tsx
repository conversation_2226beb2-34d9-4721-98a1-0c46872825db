'use client';

import { motion } from 'framer-motion';
import { workExperience } from '@/lib/data';
import ExperienceCard from './ExperienceCard';

// Animation variants for the section
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const titleVariants = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

export default function WorkExperience() {
  return (
    <motion.section
      id="work-experience"
      className="py-20 bg-background"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={containerVariants}
    >
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          {/* Section Title */}
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-textPrimary mb-4 text-center"
            variants={titleVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
          >
            Work Experience
          </motion.h2>

          <motion.p
            className="text-lg text-textSecondary max-w-2xl mx-auto text-center mb-16"
            variants={titleVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            transition={{ delay: 0.2 }}
          >
            Professional journey and key contributions in software development
          </motion.p>

          {/* Experience Cards */}
          <div className="space-y-8">
            {workExperience.map((experience, index) => (
              <ExperienceCard
                key={experience.id}
                experience={experience}
                index={index}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
}
