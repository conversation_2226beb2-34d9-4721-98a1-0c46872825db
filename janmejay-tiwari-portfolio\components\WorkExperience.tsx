'use client';

import { motion } from 'framer-motion';
import { workExperience } from '@/lib/data';
import ExperienceCard from './ExperienceCard';

// Animation variants for the section
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const titleVariants = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

export default function WorkExperience() {
  return (
    <section id="work-experience" className="py-20 bg-background">
      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Section Title */}
          <motion.div 
            variants={titleVariants}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-textPrimary mb-4">
              Work Experience
            </h2>
            <p className="text-lg text-textSecondary max-w-2xl mx-auto">
              Professional journey and key contributions in software development
            </p>
          </motion.div>

          {/* Experience Cards */}
          <div className="space-y-8">
            {workExperience.map((experience, index) => (
              <ExperienceCard 
                key={experience.id}
                experience={experience}
                index={index}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
