// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const LuAArrowDown: IconType;
export declare const LuAArrowUp: IconType;
export declare const LuALargeSmall: IconType;
export declare const LuAccessibility: IconType;
export declare const LuActivity: IconType;
export declare const LuAirVent: IconType;
export declare const LuAirplay: IconType;
export declare const LuAlarmClockCheck: IconType;
export declare const LuAlarmClockMinus: IconType;
export declare const LuAlarmClockOff: IconType;
export declare const LuAlarmClockPlus: IconType;
export declare const LuAlarmClock: IconType;
export declare const LuAlarmSmoke: IconType;
export declare const LuAlbum: IconType;
export declare const LuAlignCenterHorizontal: IconType;
export declare const LuAlignCenterVertical: IconType;
export declare const LuAlignCenter: IconType;
export declare const LuAlignEndHorizontal: IconType;
export declare const LuAlignEndVertical: IconType;
export declare const LuAlignHorizontalDistributeCenter: IconType;
export declare const LuAlignHorizontalDistributeEnd: IconType;
export declare const LuAlignHorizontalDistributeStart: IconType;
export declare const LuAlignHorizontalJustifyCenter: IconType;
export declare const LuAlignHorizontalJustifyEnd: IconType;
export declare const LuAlignHorizontalJustifyStart: IconType;
export declare const LuAlignHorizontalSpaceAround: IconType;
export declare const LuAlignHorizontalSpaceBetween: IconType;
export declare const LuAlignJustify: IconType;
export declare const LuAlignLeft: IconType;
export declare const LuAlignRight: IconType;
export declare const LuAlignStartHorizontal: IconType;
export declare const LuAlignStartVertical: IconType;
export declare const LuAlignVerticalDistributeCenter: IconType;
export declare const LuAlignVerticalDistributeEnd: IconType;
export declare const LuAlignVerticalDistributeStart: IconType;
export declare const LuAlignVerticalJustifyCenter: IconType;
export declare const LuAlignVerticalJustifyEnd: IconType;
export declare const LuAlignVerticalJustifyStart: IconType;
export declare const LuAlignVerticalSpaceAround: IconType;
export declare const LuAlignVerticalSpaceBetween: IconType;
export declare const LuAmbulance: IconType;
export declare const LuAmpersand: IconType;
export declare const LuAmpersands: IconType;
export declare const LuAmphora: IconType;
export declare const LuAnchor: IconType;
export declare const LuAngry: IconType;
export declare const LuAnnoyed: IconType;
export declare const LuAntenna: IconType;
export declare const LuAnvil: IconType;
export declare const LuAperture: IconType;
export declare const LuAppWindowMac: IconType;
export declare const LuAppWindow: IconType;
export declare const LuApple: IconType;
export declare const LuArchiveRestore: IconType;
export declare const LuArchiveX: IconType;
export declare const LuArchive: IconType;
export declare const LuArmchair: IconType;
export declare const LuArrowBigDownDash: IconType;
export declare const LuArrowBigDown: IconType;
export declare const LuArrowBigLeftDash: IconType;
export declare const LuArrowBigLeft: IconType;
export declare const LuArrowBigRightDash: IconType;
export declare const LuArrowBigRight: IconType;
export declare const LuArrowBigUpDash: IconType;
export declare const LuArrowBigUp: IconType;
export declare const LuArrowDown01: IconType;
export declare const LuArrowDown10: IconType;
export declare const LuArrowDownAZ: IconType;
export declare const LuArrowDownFromLine: IconType;
export declare const LuArrowDownLeft: IconType;
export declare const LuArrowDownNarrowWide: IconType;
export declare const LuArrowDownRight: IconType;
export declare const LuArrowDownToDot: IconType;
export declare const LuArrowDownToLine: IconType;
export declare const LuArrowDownUp: IconType;
export declare const LuArrowDownWideNarrow: IconType;
export declare const LuArrowDownZA: IconType;
export declare const LuArrowDown: IconType;
export declare const LuArrowLeftFromLine: IconType;
export declare const LuArrowLeftRight: IconType;
export declare const LuArrowLeftToLine: IconType;
export declare const LuArrowLeft: IconType;
export declare const LuArrowRightFromLine: IconType;
export declare const LuArrowRightLeft: IconType;
export declare const LuArrowRightToLine: IconType;
export declare const LuArrowRight: IconType;
export declare const LuArrowUp01: IconType;
export declare const LuArrowUp10: IconType;
export declare const LuArrowUpAZ: IconType;
export declare const LuArrowUpDown: IconType;
export declare const LuArrowUpFromDot: IconType;
export declare const LuArrowUpFromLine: IconType;
export declare const LuArrowUpLeft: IconType;
export declare const LuArrowUpNarrowWide: IconType;
export declare const LuArrowUpRight: IconType;
export declare const LuArrowUpToLine: IconType;
export declare const LuArrowUpWideNarrow: IconType;
export declare const LuArrowUpZA: IconType;
export declare const LuArrowUp: IconType;
export declare const LuArrowsUpFromLine: IconType;
export declare const LuAsterisk: IconType;
export declare const LuAtSign: IconType;
export declare const LuAtom: IconType;
export declare const LuAudioLines: IconType;
export declare const LuAudioWaveform: IconType;
export declare const LuAward: IconType;
export declare const LuAxe: IconType;
export declare const LuAxis3D: IconType;
export declare const LuBaby: IconType;
export declare const LuBackpack: IconType;
export declare const LuBadgeAlert: IconType;
export declare const LuBadgeCent: IconType;
export declare const LuBadgeCheck: IconType;
export declare const LuBadgeDollarSign: IconType;
export declare const LuBadgeEuro: IconType;
export declare const LuBadgeHelp: IconType;
export declare const LuBadgeIndianRupee: IconType;
export declare const LuBadgeInfo: IconType;
export declare const LuBadgeJapaneseYen: IconType;
export declare const LuBadgeMinus: IconType;
export declare const LuBadgePercent: IconType;
export declare const LuBadgePlus: IconType;
export declare const LuBadgePoundSterling: IconType;
export declare const LuBadgeRussianRuble: IconType;
export declare const LuBadgeSwissFranc: IconType;
export declare const LuBadgeX: IconType;
export declare const LuBadge: IconType;
export declare const LuBaggageClaim: IconType;
export declare const LuBan: IconType;
export declare const LuBanana: IconType;
export declare const LuBandage: IconType;
export declare const LuBanknote: IconType;
export declare const LuBarcode: IconType;
export declare const LuBaseline: IconType;
export declare const LuBath: IconType;
export declare const LuBatteryCharging: IconType;
export declare const LuBatteryFull: IconType;
export declare const LuBatteryLow: IconType;
export declare const LuBatteryMedium: IconType;
export declare const LuBatteryWarning: IconType;
export declare const LuBattery: IconType;
export declare const LuBeaker: IconType;
export declare const LuBeanOff: IconType;
export declare const LuBean: IconType;
export declare const LuBedDouble: IconType;
export declare const LuBedSingle: IconType;
export declare const LuBed: IconType;
export declare const LuBeef: IconType;
export declare const LuBeerOff: IconType;
export declare const LuBeer: IconType;
export declare const LuBellDot: IconType;
export declare const LuBellElectric: IconType;
export declare const LuBellMinus: IconType;
export declare const LuBellOff: IconType;
export declare const LuBellPlus: IconType;
export declare const LuBellRing: IconType;
export declare const LuBell: IconType;
export declare const LuBetweenHorizontalEnd: IconType;
export declare const LuBetweenHorizontalStart: IconType;
export declare const LuBetweenVerticalEnd: IconType;
export declare const LuBetweenVerticalStart: IconType;
export declare const LuBicepsFlexed: IconType;
export declare const LuBike: IconType;
export declare const LuBinary: IconType;
export declare const LuBinoculars: IconType;
export declare const LuBiohazard: IconType;
export declare const LuBird: IconType;
export declare const LuBitcoin: IconType;
export declare const LuBlend: IconType;
export declare const LuBlinds: IconType;
export declare const LuBlocks: IconType;
export declare const LuBluetoothConnected: IconType;
export declare const LuBluetoothOff: IconType;
export declare const LuBluetoothSearching: IconType;
export declare const LuBluetooth: IconType;
export declare const LuBold: IconType;
export declare const LuBolt: IconType;
export declare const LuBomb: IconType;
export declare const LuBone: IconType;
export declare const LuBookA: IconType;
export declare const LuBookAudio: IconType;
export declare const LuBookCheck: IconType;
export declare const LuBookCopy: IconType;
export declare const LuBookDashed: IconType;
export declare const LuBookDown: IconType;
export declare const LuBookHeadphones: IconType;
export declare const LuBookHeart: IconType;
export declare const LuBookImage: IconType;
export declare const LuBookKey: IconType;
export declare const LuBookLock: IconType;
export declare const LuBookMarked: IconType;
export declare const LuBookMinus: IconType;
export declare const LuBookOpenCheck: IconType;
export declare const LuBookOpenText: IconType;
export declare const LuBookOpen: IconType;
export declare const LuBookPlus: IconType;
export declare const LuBookText: IconType;
export declare const LuBookType: IconType;
export declare const LuBookUp2: IconType;
export declare const LuBookUp: IconType;
export declare const LuBookUser: IconType;
export declare const LuBookX: IconType;
export declare const LuBook: IconType;
export declare const LuBookmarkCheck: IconType;
export declare const LuBookmarkMinus: IconType;
export declare const LuBookmarkPlus: IconType;
export declare const LuBookmarkX: IconType;
export declare const LuBookmark: IconType;
export declare const LuBoomBox: IconType;
export declare const LuBotMessageSquare: IconType;
export declare const LuBotOff: IconType;
export declare const LuBot: IconType;
export declare const LuBox: IconType;
export declare const LuBoxes: IconType;
export declare const LuBraces: IconType;
export declare const LuBrackets: IconType;
export declare const LuBrainCircuit: IconType;
export declare const LuBrainCog: IconType;
export declare const LuBrain: IconType;
export declare const LuBrickWall: IconType;
export declare const LuBriefcaseBusiness: IconType;
export declare const LuBriefcaseConveyorBelt: IconType;
export declare const LuBriefcaseMedical: IconType;
export declare const LuBriefcase: IconType;
export declare const LuBringToFront: IconType;
export declare const LuBrush: IconType;
export declare const LuBugOff: IconType;
export declare const LuBugPlay: IconType;
export declare const LuBug: IconType;
export declare const LuBuilding2: IconType;
export declare const LuBuilding: IconType;
export declare const LuBusFront: IconType;
export declare const LuBus: IconType;
export declare const LuCableCar: IconType;
export declare const LuCable: IconType;
export declare const LuCakeSlice: IconType;
export declare const LuCake: IconType;
export declare const LuCalculator: IconType;
export declare const LuCalendar1: IconType;
export declare const LuCalendarArrowDown: IconType;
export declare const LuCalendarArrowUp: IconType;
export declare const LuCalendarCheck2: IconType;
export declare const LuCalendarCheck: IconType;
export declare const LuCalendarClock: IconType;
export declare const LuCalendarCog: IconType;
export declare const LuCalendarDays: IconType;
export declare const LuCalendarFold: IconType;
export declare const LuCalendarHeart: IconType;
export declare const LuCalendarMinus2: IconType;
export declare const LuCalendarMinus: IconType;
export declare const LuCalendarOff: IconType;
export declare const LuCalendarPlus2: IconType;
export declare const LuCalendarPlus: IconType;
export declare const LuCalendarRange: IconType;
export declare const LuCalendarSearch: IconType;
export declare const LuCalendarSync: IconType;
export declare const LuCalendarX2: IconType;
export declare const LuCalendarX: IconType;
export declare const LuCalendar: IconType;
export declare const LuCameraOff: IconType;
export declare const LuCamera: IconType;
export declare const LuCandyCane: IconType;
export declare const LuCandyOff: IconType;
export declare const LuCandy: IconType;
export declare const LuCannabis: IconType;
export declare const LuCaptionsOff: IconType;
export declare const LuCaptions: IconType;
export declare const LuCarFront: IconType;
export declare const LuCarTaxiFront: IconType;
export declare const LuCar: IconType;
export declare const LuCaravan: IconType;
export declare const LuCarrot: IconType;
export declare const LuCaseLower: IconType;
export declare const LuCaseSensitive: IconType;
export declare const LuCaseUpper: IconType;
export declare const LuCassetteTape: IconType;
export declare const LuCast: IconType;
export declare const LuCastle: IconType;
export declare const LuCat: IconType;
export declare const LuCctv: IconType;
export declare const LuChartArea: IconType;
export declare const LuChartBarBig: IconType;
export declare const LuChartBarDecreasing: IconType;
export declare const LuChartBarIncreasing: IconType;
export declare const LuChartBarStacked: IconType;
export declare const LuChartBar: IconType;
export declare const LuChartCandlestick: IconType;
export declare const LuChartColumnBig: IconType;
export declare const LuChartColumnDecreasing: IconType;
export declare const LuChartColumnIncreasing: IconType;
export declare const LuChartColumnStacked: IconType;
export declare const LuChartColumn: IconType;
export declare const LuChartGantt: IconType;
export declare const LuChartLine: IconType;
export declare const LuChartNetwork: IconType;
export declare const LuChartNoAxesColumnDecreasing: IconType;
export declare const LuChartNoAxesColumnIncreasing: IconType;
export declare const LuChartNoAxesColumn: IconType;
export declare const LuChartNoAxesCombined: IconType;
export declare const LuChartNoAxesGantt: IconType;
export declare const LuChartPie: IconType;
export declare const LuChartScatter: IconType;
export declare const LuChartSpline: IconType;
export declare const LuCheckCheck: IconType;
export declare const LuCheck: IconType;
export declare const LuChefHat: IconType;
export declare const LuCherry: IconType;
export declare const LuChevronDown: IconType;
export declare const LuChevronFirst: IconType;
export declare const LuChevronLast: IconType;
export declare const LuChevronLeft: IconType;
export declare const LuChevronRight: IconType;
export declare const LuChevronUp: IconType;
export declare const LuChevronsDownUp: IconType;
export declare const LuChevronsDown: IconType;
export declare const LuChevronsLeftRightEllipsis: IconType;
export declare const LuChevronsLeftRight: IconType;
export declare const LuChevronsLeft: IconType;
export declare const LuChevronsRightLeft: IconType;
export declare const LuChevronsRight: IconType;
export declare const LuChevronsUpDown: IconType;
export declare const LuChevronsUp: IconType;
export declare const LuChrome: IconType;
export declare const LuChurch: IconType;
export declare const LuCigaretteOff: IconType;
export declare const LuCigarette: IconType;
export declare const LuCircleAlert: IconType;
export declare const LuCircleArrowDown: IconType;
export declare const LuCircleArrowLeft: IconType;
export declare const LuCircleArrowOutDownLeft: IconType;
export declare const LuCircleArrowOutDownRight: IconType;
export declare const LuCircleArrowOutUpLeft: IconType;
export declare const LuCircleArrowOutUpRight: IconType;
export declare const LuCircleArrowRight: IconType;
export declare const LuCircleArrowUp: IconType;
export declare const LuCircleCheckBig: IconType;
export declare const LuCircleCheck: IconType;
export declare const LuCircleChevronDown: IconType;
export declare const LuCircleChevronLeft: IconType;
export declare const LuCircleChevronRight: IconType;
export declare const LuCircleChevronUp: IconType;
export declare const LuCircleDashed: IconType;
export declare const LuCircleDivide: IconType;
export declare const LuCircleDollarSign: IconType;
export declare const LuCircleDotDashed: IconType;
export declare const LuCircleDot: IconType;
export declare const LuCircleEllipsis: IconType;
export declare const LuCircleEqual: IconType;
export declare const LuCircleFadingArrowUp: IconType;
export declare const LuCircleFadingPlus: IconType;
export declare const LuCircleGauge: IconType;
export declare const LuCircleHelp: IconType;
export declare const LuCircleMinus: IconType;
export declare const LuCircleOff: IconType;
export declare const LuCircleParkingOff: IconType;
export declare const LuCircleParking: IconType;
export declare const LuCirclePause: IconType;
export declare const LuCirclePercent: IconType;
export declare const LuCirclePlay: IconType;
export declare const LuCirclePlus: IconType;
export declare const LuCirclePower: IconType;
export declare const LuCircleSlash2: IconType;
export declare const LuCircleSlash: IconType;
export declare const LuCircleStop: IconType;
export declare const LuCircleUserRound: IconType;
export declare const LuCircleUser: IconType;
export declare const LuCircleX: IconType;
export declare const LuCircle: IconType;
export declare const LuCircuitBoard: IconType;
export declare const LuCitrus: IconType;
export declare const LuClapperboard: IconType;
export declare const LuClipboardCheck: IconType;
export declare const LuClipboardCopy: IconType;
export declare const LuClipboardList: IconType;
export declare const LuClipboardMinus: IconType;
export declare const LuClipboardPaste: IconType;
export declare const LuClipboardPenLine: IconType;
export declare const LuClipboardPen: IconType;
export declare const LuClipboardPlus: IconType;
export declare const LuClipboardType: IconType;
export declare const LuClipboardX: IconType;
export declare const LuClipboard: IconType;
export declare const LuClock1: IconType;
export declare const LuClock10: IconType;
export declare const LuClock11: IconType;
export declare const LuClock12: IconType;
export declare const LuClock2: IconType;
export declare const LuClock3: IconType;
export declare const LuClock4: IconType;
export declare const LuClock5: IconType;
export declare const LuClock6: IconType;
export declare const LuClock7: IconType;
export declare const LuClock8: IconType;
export declare const LuClock9: IconType;
export declare const LuClockAlert: IconType;
export declare const LuClockArrowDown: IconType;
export declare const LuClockArrowUp: IconType;
export declare const LuClock: IconType;
export declare const LuCloudAlert: IconType;
export declare const LuCloudCog: IconType;
export declare const LuCloudDownload: IconType;
export declare const LuCloudDrizzle: IconType;
export declare const LuCloudFog: IconType;
export declare const LuCloudHail: IconType;
export declare const LuCloudLightning: IconType;
export declare const LuCloudMoonRain: IconType;
export declare const LuCloudMoon: IconType;
export declare const LuCloudOff: IconType;
export declare const LuCloudRainWind: IconType;
export declare const LuCloudRain: IconType;
export declare const LuCloudSnow: IconType;
export declare const LuCloudSunRain: IconType;
export declare const LuCloudSun: IconType;
export declare const LuCloudUpload: IconType;
export declare const LuCloud: IconType;
export declare const LuCloudy: IconType;
export declare const LuClover: IconType;
export declare const LuClub: IconType;
export declare const LuCodeXml: IconType;
export declare const LuCode: IconType;
export declare const LuCodepen: IconType;
export declare const LuCodesandbox: IconType;
export declare const LuCoffee: IconType;
export declare const LuCog: IconType;
export declare const LuCoins: IconType;
export declare const LuColumns2: IconType;
export declare const LuColumns3: IconType;
export declare const LuColumns4: IconType;
export declare const LuCombine: IconType;
export declare const LuCommand: IconType;
export declare const LuCompass: IconType;
export declare const LuComponent: IconType;
export declare const LuComputer: IconType;
export declare const LuConciergeBell: IconType;
export declare const LuCone: IconType;
export declare const LuConstruction: IconType;
export declare const LuContactRound: IconType;
export declare const LuContact: IconType;
export declare const LuContainer: IconType;
export declare const LuContrast: IconType;
export declare const LuCookie: IconType;
export declare const LuCookingPot: IconType;
export declare const LuCopyCheck: IconType;
export declare const LuCopyMinus: IconType;
export declare const LuCopyPlus: IconType;
export declare const LuCopySlash: IconType;
export declare const LuCopyX: IconType;
export declare const LuCopy: IconType;
export declare const LuCopyleft: IconType;
export declare const LuCopyright: IconType;
export declare const LuCornerDownLeft: IconType;
export declare const LuCornerDownRight: IconType;
export declare const LuCornerLeftDown: IconType;
export declare const LuCornerLeftUp: IconType;
export declare const LuCornerRightDown: IconType;
export declare const LuCornerRightUp: IconType;
export declare const LuCornerUpLeft: IconType;
export declare const LuCornerUpRight: IconType;
export declare const LuCpu: IconType;
export declare const LuCreativeCommons: IconType;
export declare const LuCreditCard: IconType;
export declare const LuCroissant: IconType;
export declare const LuCrop: IconType;
export declare const LuCross: IconType;
export declare const LuCrosshair: IconType;
export declare const LuCrown: IconType;
export declare const LuCuboid: IconType;
export declare const LuCupSoda: IconType;
export declare const LuCurrency: IconType;
export declare const LuCylinder: IconType;
export declare const LuDam: IconType;
export declare const LuDatabaseBackup: IconType;
export declare const LuDatabaseZap: IconType;
export declare const LuDatabase: IconType;
export declare const LuDelete: IconType;
export declare const LuDessert: IconType;
export declare const LuDiameter: IconType;
export declare const LuDiamondMinus: IconType;
export declare const LuDiamondPercent: IconType;
export declare const LuDiamondPlus: IconType;
export declare const LuDiamond: IconType;
export declare const LuDice1: IconType;
export declare const LuDice2: IconType;
export declare const LuDice3: IconType;
export declare const LuDice4: IconType;
export declare const LuDice5: IconType;
export declare const LuDice6: IconType;
export declare const LuDices: IconType;
export declare const LuDiff: IconType;
export declare const LuDisc2: IconType;
export declare const LuDisc3: IconType;
export declare const LuDiscAlbum: IconType;
export declare const LuDisc: IconType;
export declare const LuDivide: IconType;
export declare const LuDnaOff: IconType;
export declare const LuDna: IconType;
export declare const LuDock: IconType;
export declare const LuDog: IconType;
export declare const LuDollarSign: IconType;
export declare const LuDonut: IconType;
export declare const LuDoorClosed: IconType;
export declare const LuDoorOpen: IconType;
export declare const LuDot: IconType;
export declare const LuDownload: IconType;
export declare const LuDraftingCompass: IconType;
export declare const LuDrama: IconType;
export declare const LuDribbble: IconType;
export declare const LuDrill: IconType;
export declare const LuDroplet: IconType;
export declare const LuDroplets: IconType;
export declare const LuDrum: IconType;
export declare const LuDrumstick: IconType;
export declare const LuDumbbell: IconType;
export declare const LuEarOff: IconType;
export declare const LuEar: IconType;
export declare const LuEarthLock: IconType;
export declare const LuEarth: IconType;
export declare const LuEclipse: IconType;
export declare const LuEggFried: IconType;
export declare const LuEggOff: IconType;
export declare const LuEgg: IconType;
export declare const LuEllipsisVertical: IconType;
export declare const LuEllipsis: IconType;
export declare const LuEqualApproximately: IconType;
export declare const LuEqualNot: IconType;
export declare const LuEqual: IconType;
export declare const LuEraser: IconType;
export declare const LuEthernetPort: IconType;
export declare const LuEuro: IconType;
export declare const LuExpand: IconType;
export declare const LuExternalLink: IconType;
export declare const LuEyeClosed: IconType;
export declare const LuEyeOff: IconType;
export declare const LuEye: IconType;
export declare const LuFacebook: IconType;
export declare const LuFactory: IconType;
export declare const LuFan: IconType;
export declare const LuFastForward: IconType;
export declare const LuFeather: IconType;
export declare const LuFence: IconType;
export declare const LuFerrisWheel: IconType;
export declare const LuFigma: IconType;
export declare const LuFileArchive: IconType;
export declare const LuFileAudio2: IconType;
export declare const LuFileAudio: IconType;
export declare const LuFileAxis3D: IconType;
export declare const LuFileBadge2: IconType;
export declare const LuFileBadge: IconType;
export declare const LuFileBox: IconType;
export declare const LuFileChartColumnIncreasing: IconType;
export declare const LuFileChartColumn: IconType;
export declare const LuFileChartLine: IconType;
export declare const LuFileChartPie: IconType;
export declare const LuFileCheck2: IconType;
export declare const LuFileCheck: IconType;
export declare const LuFileClock: IconType;
export declare const LuFileCode2: IconType;
export declare const LuFileCode: IconType;
export declare const LuFileCog: IconType;
export declare const LuFileDiff: IconType;
export declare const LuFileDigit: IconType;
export declare const LuFileDown: IconType;
export declare const LuFileHeart: IconType;
export declare const LuFileImage: IconType;
export declare const LuFileInput: IconType;
export declare const LuFileJson2: IconType;
export declare const LuFileJson: IconType;
export declare const LuFileKey2: IconType;
export declare const LuFileKey: IconType;
export declare const LuFileLock2: IconType;
export declare const LuFileLock: IconType;
export declare const LuFileMinus2: IconType;
export declare const LuFileMinus: IconType;
export declare const LuFileMusic: IconType;
export declare const LuFileOutput: IconType;
export declare const LuFilePenLine: IconType;
export declare const LuFilePen: IconType;
export declare const LuFilePlus2: IconType;
export declare const LuFilePlus: IconType;
export declare const LuFileQuestion: IconType;
export declare const LuFileScan: IconType;
export declare const LuFileSearch2: IconType;
export declare const LuFileSearch: IconType;
export declare const LuFileSliders: IconType;
export declare const LuFileSpreadsheet: IconType;
export declare const LuFileStack: IconType;
export declare const LuFileSymlink: IconType;
export declare const LuFileTerminal: IconType;
export declare const LuFileText: IconType;
export declare const LuFileType2: IconType;
export declare const LuFileType: IconType;
export declare const LuFileUp: IconType;
export declare const LuFileUser: IconType;
export declare const LuFileVideo2: IconType;
export declare const LuFileVideo: IconType;
export declare const LuFileVolume2: IconType;
export declare const LuFileVolume: IconType;
export declare const LuFileWarning: IconType;
export declare const LuFileX2: IconType;
export declare const LuFileX: IconType;
export declare const LuFile: IconType;
export declare const LuFiles: IconType;
export declare const LuFilm: IconType;
export declare const LuFilterX: IconType;
export declare const LuFilter: IconType;
export declare const LuFingerprint: IconType;
export declare const LuFireExtinguisher: IconType;
export declare const LuFishOff: IconType;
export declare const LuFishSymbol: IconType;
export declare const LuFish: IconType;
export declare const LuFlagOff: IconType;
export declare const LuFlagTriangleLeft: IconType;
export declare const LuFlagTriangleRight: IconType;
export declare const LuFlag: IconType;
export declare const LuFlameKindling: IconType;
export declare const LuFlame: IconType;
export declare const LuFlashlightOff: IconType;
export declare const LuFlashlight: IconType;
export declare const LuFlaskConicalOff: IconType;
export declare const LuFlaskConical: IconType;
export declare const LuFlaskRound: IconType;
export declare const LuFlipHorizontal2: IconType;
export declare const LuFlipHorizontal: IconType;
export declare const LuFlipVertical2: IconType;
export declare const LuFlipVertical: IconType;
export declare const LuFlower2: IconType;
export declare const LuFlower: IconType;
export declare const LuFocus: IconType;
export declare const LuFoldHorizontal: IconType;
export declare const LuFoldVertical: IconType;
export declare const LuFolderArchive: IconType;
export declare const LuFolderCheck: IconType;
export declare const LuFolderClock: IconType;
export declare const LuFolderClosed: IconType;
export declare const LuFolderCode: IconType;
export declare const LuFolderCog: IconType;
export declare const LuFolderDot: IconType;
export declare const LuFolderDown: IconType;
export declare const LuFolderGit2: IconType;
export declare const LuFolderGit: IconType;
export declare const LuFolderHeart: IconType;
export declare const LuFolderInput: IconType;
export declare const LuFolderKanban: IconType;
export declare const LuFolderKey: IconType;
export declare const LuFolderLock: IconType;
export declare const LuFolderMinus: IconType;
export declare const LuFolderOpenDot: IconType;
export declare const LuFolderOpen: IconType;
export declare const LuFolderOutput: IconType;
export declare const LuFolderPen: IconType;
export declare const LuFolderPlus: IconType;
export declare const LuFolderRoot: IconType;
export declare const LuFolderSearch2: IconType;
export declare const LuFolderSearch: IconType;
export declare const LuFolderSymlink: IconType;
export declare const LuFolderSync: IconType;
export declare const LuFolderTree: IconType;
export declare const LuFolderUp: IconType;
export declare const LuFolderX: IconType;
export declare const LuFolder: IconType;
export declare const LuFolders: IconType;
export declare const LuFootprints: IconType;
export declare const LuForklift: IconType;
export declare const LuForward: IconType;
export declare const LuFrame: IconType;
export declare const LuFramer: IconType;
export declare const LuFrown: IconType;
export declare const LuFuel: IconType;
export declare const LuFullscreen: IconType;
export declare const LuGalleryHorizontalEnd: IconType;
export declare const LuGalleryHorizontal: IconType;
export declare const LuGalleryThumbnails: IconType;
export declare const LuGalleryVerticalEnd: IconType;
export declare const LuGalleryVertical: IconType;
export declare const LuGamepad2: IconType;
export declare const LuGamepad: IconType;
export declare const LuGauge: IconType;
export declare const LuGavel: IconType;
export declare const LuGem: IconType;
export declare const LuGhost: IconType;
export declare const LuGift: IconType;
export declare const LuGitBranchPlus: IconType;
export declare const LuGitBranch: IconType;
export declare const LuGitCommitHorizontal: IconType;
export declare const LuGitCommitVertical: IconType;
export declare const LuGitCompareArrows: IconType;
export declare const LuGitCompare: IconType;
export declare const LuGitFork: IconType;
export declare const LuGitGraph: IconType;
export declare const LuGitMerge: IconType;
export declare const LuGitPullRequestArrow: IconType;
export declare const LuGitPullRequestClosed: IconType;
export declare const LuGitPullRequestCreateArrow: IconType;
export declare const LuGitPullRequestCreate: IconType;
export declare const LuGitPullRequestDraft: IconType;
export declare const LuGitPullRequest: IconType;
export declare const LuGithub: IconType;
export declare const LuGitlab: IconType;
export declare const LuGlassWater: IconType;
export declare const LuGlasses: IconType;
export declare const LuGlobeLock: IconType;
export declare const LuGlobe: IconType;
export declare const LuGoal: IconType;
export declare const LuGrab: IconType;
export declare const LuGraduationCap: IconType;
export declare const LuGrape: IconType;
export declare const LuGrid2X2Check: IconType;
export declare const LuGrid2X2Plus: IconType;
export declare const LuGrid2X2X: IconType;
export declare const LuGrid2X2: IconType;
export declare const LuGrid3X3: IconType;
export declare const LuGripHorizontal: IconType;
export declare const LuGripVertical: IconType;
export declare const LuGrip: IconType;
export declare const LuGroup: IconType;
export declare const LuGuitar: IconType;
export declare const LuHam: IconType;
export declare const LuHammer: IconType;
export declare const LuHandCoins: IconType;
export declare const LuHandHeart: IconType;
export declare const LuHandHelping: IconType;
export declare const LuHandMetal: IconType;
export declare const LuHandPlatter: IconType;
export declare const LuHand: IconType;
export declare const LuHandshake: IconType;
export declare const LuHardDriveDownload: IconType;
export declare const LuHardDriveUpload: IconType;
export declare const LuHardDrive: IconType;
export declare const LuHardHat: IconType;
export declare const LuHash: IconType;
export declare const LuHaze: IconType;
export declare const LuHdmiPort: IconType;
export declare const LuHeading1: IconType;
export declare const LuHeading2: IconType;
export declare const LuHeading3: IconType;
export declare const LuHeading4: IconType;
export declare const LuHeading5: IconType;
export declare const LuHeading6: IconType;
export declare const LuHeading: IconType;
export declare const LuHeadphoneOff: IconType;
export declare const LuHeadphones: IconType;
export declare const LuHeadset: IconType;
export declare const LuHeartCrack: IconType;
export declare const LuHeartHandshake: IconType;
export declare const LuHeartOff: IconType;
export declare const LuHeartPulse: IconType;
export declare const LuHeart: IconType;
export declare const LuHeater: IconType;
export declare const LuHexagon: IconType;
export declare const LuHighlighter: IconType;
export declare const LuHistory: IconType;
export declare const LuHopOff: IconType;
export declare const LuHop: IconType;
export declare const LuHospital: IconType;
export declare const LuHotel: IconType;
export declare const LuHourglass: IconType;
export declare const LuHousePlug: IconType;
export declare const LuHousePlus: IconType;
export declare const LuHouse: IconType;
export declare const LuIceCreamBowl: IconType;
export declare const LuIceCreamCone: IconType;
export declare const LuIdCard: IconType;
export declare const LuImageDown: IconType;
export declare const LuImageMinus: IconType;
export declare const LuImageOff: IconType;
export declare const LuImagePlay: IconType;
export declare const LuImagePlus: IconType;
export declare const LuImageUp: IconType;
export declare const LuImageUpscale: IconType;
export declare const LuImage: IconType;
export declare const LuImages: IconType;
export declare const LuImport: IconType;
export declare const LuInbox: IconType;
export declare const LuIndentDecrease: IconType;
export declare const LuIndentIncrease: IconType;
export declare const LuIndianRupee: IconType;
export declare const LuInfinity: IconType;
export declare const LuInfo: IconType;
export declare const LuInspectionPanel: IconType;
export declare const LuInstagram: IconType;
export declare const LuItalic: IconType;
export declare const LuIterationCcw: IconType;
export declare const LuIterationCw: IconType;
export declare const LuJapaneseYen: IconType;
export declare const LuJoystick: IconType;
export declare const LuKanban: IconType;
export declare const LuKeyRound: IconType;
export declare const LuKeySquare: IconType;
export declare const LuKey: IconType;
export declare const LuKeyboardMusic: IconType;
export declare const LuKeyboardOff: IconType;
export declare const LuKeyboard: IconType;
export declare const LuLampCeiling: IconType;
export declare const LuLampDesk: IconType;
export declare const LuLampFloor: IconType;
export declare const LuLampWallDown: IconType;
export declare const LuLampWallUp: IconType;
export declare const LuLamp: IconType;
export declare const LuLandPlot: IconType;
export declare const LuLandmark: IconType;
export declare const LuLanguages: IconType;
export declare const LuLaptopMinimalCheck: IconType;
export declare const LuLaptopMinimal: IconType;
export declare const LuLaptop: IconType;
export declare const LuLassoSelect: IconType;
export declare const LuLasso: IconType;
export declare const LuLaugh: IconType;
export declare const LuLayers2: IconType;
export declare const LuLayers3: IconType;
export declare const LuLayers: IconType;
export declare const LuLayoutDashboard: IconType;
export declare const LuLayoutGrid: IconType;
export declare const LuLayoutList: IconType;
export declare const LuLayoutPanelLeft: IconType;
export declare const LuLayoutPanelTop: IconType;
export declare const LuLayoutTemplate: IconType;
export declare const LuLeaf: IconType;
export declare const LuLeafyGreen: IconType;
export declare const LuLectern: IconType;
export declare const LuLetterText: IconType;
export declare const LuLibraryBig: IconType;
export declare const LuLibrary: IconType;
export declare const LuLifeBuoy: IconType;
export declare const LuLigature: IconType;
export declare const LuLightbulbOff: IconType;
export declare const LuLightbulb: IconType;
export declare const LuLink2Off: IconType;
export declare const LuLink2: IconType;
export declare const LuLink: IconType;
export declare const LuLinkedin: IconType;
export declare const LuListCheck: IconType;
export declare const LuListChecks: IconType;
export declare const LuListCollapse: IconType;
export declare const LuListEnd: IconType;
export declare const LuListFilter: IconType;
export declare const LuListMinus: IconType;
export declare const LuListMusic: IconType;
export declare const LuListOrdered: IconType;
export declare const LuListPlus: IconType;
export declare const LuListRestart: IconType;
export declare const LuListStart: IconType;
export declare const LuListTodo: IconType;
export declare const LuListTree: IconType;
export declare const LuListVideo: IconType;
export declare const LuListX: IconType;
export declare const LuList: IconType;
export declare const LuLoaderCircle: IconType;
export declare const LuLoaderPinwheel: IconType;
export declare const LuLoader: IconType;
export declare const LuLocateFixed: IconType;
export declare const LuLocateOff: IconType;
export declare const LuLocate: IconType;
export declare const LuLockKeyholeOpen: IconType;
export declare const LuLockKeyhole: IconType;
export declare const LuLockOpen: IconType;
export declare const LuLock: IconType;
export declare const LuLogIn: IconType;
export declare const LuLogOut: IconType;
export declare const LuLogs: IconType;
export declare const LuLollipop: IconType;
export declare const LuLuggage: IconType;
export declare const LuMagnet: IconType;
export declare const LuMailCheck: IconType;
export declare const LuMailMinus: IconType;
export declare const LuMailOpen: IconType;
export declare const LuMailPlus: IconType;
export declare const LuMailQuestion: IconType;
export declare const LuMailSearch: IconType;
export declare const LuMailWarning: IconType;
export declare const LuMailX: IconType;
export declare const LuMail: IconType;
export declare const LuMailbox: IconType;
export declare const LuMails: IconType;
export declare const LuMapPinCheckInside: IconType;
export declare const LuMapPinCheck: IconType;
export declare const LuMapPinHouse: IconType;
export declare const LuMapPinMinusInside: IconType;
export declare const LuMapPinMinus: IconType;
export declare const LuMapPinOff: IconType;
export declare const LuMapPinPlusInside: IconType;
export declare const LuMapPinPlus: IconType;
export declare const LuMapPinXInside: IconType;
export declare const LuMapPinX: IconType;
export declare const LuMapPin: IconType;
export declare const LuMapPinned: IconType;
export declare const LuMap: IconType;
export declare const LuMartini: IconType;
export declare const LuMaximize2: IconType;
export declare const LuMaximize: IconType;
export declare const LuMedal: IconType;
export declare const LuMegaphoneOff: IconType;
export declare const LuMegaphone: IconType;
export declare const LuMeh: IconType;
export declare const LuMemoryStick: IconType;
export declare const LuMenu: IconType;
export declare const LuMerge: IconType;
export declare const LuMessageCircleCode: IconType;
export declare const LuMessageCircleDashed: IconType;
export declare const LuMessageCircleHeart: IconType;
export declare const LuMessageCircleMore: IconType;
export declare const LuMessageCircleOff: IconType;
export declare const LuMessageCirclePlus: IconType;
export declare const LuMessageCircleQuestion: IconType;
export declare const LuMessageCircleReply: IconType;
export declare const LuMessageCircleWarning: IconType;
export declare const LuMessageCircleX: IconType;
export declare const LuMessageCircle: IconType;
export declare const LuMessageSquareCode: IconType;
export declare const LuMessageSquareDashed: IconType;
export declare const LuMessageSquareDiff: IconType;
export declare const LuMessageSquareDot: IconType;
export declare const LuMessageSquareHeart: IconType;
export declare const LuMessageSquareLock: IconType;
export declare const LuMessageSquareMore: IconType;
export declare const LuMessageSquareOff: IconType;
export declare const LuMessageSquarePlus: IconType;
export declare const LuMessageSquareQuote: IconType;
export declare const LuMessageSquareReply: IconType;
export declare const LuMessageSquareShare: IconType;
export declare const LuMessageSquareText: IconType;
export declare const LuMessageSquareWarning: IconType;
export declare const LuMessageSquareX: IconType;
export declare const LuMessageSquare: IconType;
export declare const LuMessagesSquare: IconType;
export declare const LuMicOff: IconType;
export declare const LuMicVocal: IconType;
export declare const LuMic: IconType;
export declare const LuMicrochip: IconType;
export declare const LuMicroscope: IconType;
export declare const LuMicrowave: IconType;
export declare const LuMilestone: IconType;
export declare const LuMilkOff: IconType;
export declare const LuMilk: IconType;
export declare const LuMinimize2: IconType;
export declare const LuMinimize: IconType;
export declare const LuMinus: IconType;
export declare const LuMonitorCheck: IconType;
export declare const LuMonitorCog: IconType;
export declare const LuMonitorDot: IconType;
export declare const LuMonitorDown: IconType;
export declare const LuMonitorOff: IconType;
export declare const LuMonitorPause: IconType;
export declare const LuMonitorPlay: IconType;
export declare const LuMonitorSmartphone: IconType;
export declare const LuMonitorSpeaker: IconType;
export declare const LuMonitorStop: IconType;
export declare const LuMonitorUp: IconType;
export declare const LuMonitorX: IconType;
export declare const LuMonitor: IconType;
export declare const LuMoonStar: IconType;
export declare const LuMoon: IconType;
export declare const LuMountainSnow: IconType;
export declare const LuMountain: IconType;
export declare const LuMouseOff: IconType;
export declare const LuMousePointer2: IconType;
export declare const LuMousePointerBan: IconType;
export declare const LuMousePointerClick: IconType;
export declare const LuMousePointer: IconType;
export declare const LuMouse: IconType;
export declare const LuMove3D: IconType;
export declare const LuMoveDiagonal2: IconType;
export declare const LuMoveDiagonal: IconType;
export declare const LuMoveDownLeft: IconType;
export declare const LuMoveDownRight: IconType;
export declare const LuMoveDown: IconType;
export declare const LuMoveHorizontal: IconType;
export declare const LuMoveLeft: IconType;
export declare const LuMoveRight: IconType;
export declare const LuMoveUpLeft: IconType;
export declare const LuMoveUpRight: IconType;
export declare const LuMoveUp: IconType;
export declare const LuMoveVertical: IconType;
export declare const LuMove: IconType;
export declare const LuMusic2: IconType;
export declare const LuMusic3: IconType;
export declare const LuMusic4: IconType;
export declare const LuMusic: IconType;
export declare const LuNavigation2Off: IconType;
export declare const LuNavigation2: IconType;
export declare const LuNavigationOff: IconType;
export declare const LuNavigation: IconType;
export declare const LuNetwork: IconType;
export declare const LuNewspaper: IconType;
export declare const LuNfc: IconType;
export declare const LuNotebookPen: IconType;
export declare const LuNotebookTabs: IconType;
export declare const LuNotebookText: IconType;
export declare const LuNotebook: IconType;
export declare const LuNotepadTextDashed: IconType;
export declare const LuNotepadText: IconType;
export declare const LuNutOff: IconType;
export declare const LuNut: IconType;
export declare const LuOctagonAlert: IconType;
export declare const LuOctagonMinus: IconType;
export declare const LuOctagonPause: IconType;
export declare const LuOctagonX: IconType;
export declare const LuOctagon: IconType;
export declare const LuOmega: IconType;
export declare const LuOption: IconType;
export declare const LuOrbit: IconType;
export declare const LuOrigami: IconType;
export declare const LuPackage2: IconType;
export declare const LuPackageCheck: IconType;
export declare const LuPackageMinus: IconType;
export declare const LuPackageOpen: IconType;
export declare const LuPackagePlus: IconType;
export declare const LuPackageSearch: IconType;
export declare const LuPackageX: IconType;
export declare const LuPackage: IconType;
export declare const LuPaintBucket: IconType;
export declare const LuPaintRoller: IconType;
export declare const LuPaintbrushVertical: IconType;
export declare const LuPaintbrush: IconType;
export declare const LuPalette: IconType;
export declare const LuPanelBottomClose: IconType;
export declare const LuPanelBottomDashed: IconType;
export declare const LuPanelBottomOpen: IconType;
export declare const LuPanelBottom: IconType;
export declare const LuPanelLeftClose: IconType;
export declare const LuPanelLeftDashed: IconType;
export declare const LuPanelLeftOpen: IconType;
export declare const LuPanelLeft: IconType;
export declare const LuPanelRightClose: IconType;
export declare const LuPanelRightDashed: IconType;
export declare const LuPanelRightOpen: IconType;
export declare const LuPanelRight: IconType;
export declare const LuPanelTopClose: IconType;
export declare const LuPanelTopDashed: IconType;
export declare const LuPanelTopOpen: IconType;
export declare const LuPanelTop: IconType;
export declare const LuPanelsLeftBottom: IconType;
export declare const LuPanelsRightBottom: IconType;
export declare const LuPanelsTopLeft: IconType;
export declare const LuPaperclip: IconType;
export declare const LuParentheses: IconType;
export declare const LuParkingMeter: IconType;
export declare const LuPartyPopper: IconType;
export declare const LuPause: IconType;
export declare const LuPawPrint: IconType;
export declare const LuPcCase: IconType;
export declare const LuPenLine: IconType;
export declare const LuPenOff: IconType;
export declare const LuPenTool: IconType;
export declare const LuPen: IconType;
export declare const LuPencilLine: IconType;
export declare const LuPencilOff: IconType;
export declare const LuPencilRuler: IconType;
export declare const LuPencil: IconType;
export declare const LuPentagon: IconType;
export declare const LuPercent: IconType;
export declare const LuPersonStanding: IconType;
export declare const LuPhilippinePeso: IconType;
export declare const LuPhoneCall: IconType;
export declare const LuPhoneForwarded: IconType;
export declare const LuPhoneIncoming: IconType;
export declare const LuPhoneMissed: IconType;
export declare const LuPhoneOff: IconType;
export declare const LuPhoneOutgoing: IconType;
export declare const LuPhone: IconType;
export declare const LuPi: IconType;
export declare const LuPiano: IconType;
export declare const LuPickaxe: IconType;
export declare const LuPictureInPicture2: IconType;
export declare const LuPictureInPicture: IconType;
export declare const LuPiggyBank: IconType;
export declare const LuPilcrowLeft: IconType;
export declare const LuPilcrowRight: IconType;
export declare const LuPilcrow: IconType;
export declare const LuPillBottle: IconType;
export declare const LuPill: IconType;
export declare const LuPinOff: IconType;
export declare const LuPin: IconType;
export declare const LuPipette: IconType;
export declare const LuPizza: IconType;
export declare const LuPlaneLanding: IconType;
export declare const LuPlaneTakeoff: IconType;
export declare const LuPlane: IconType;
export declare const LuPlay: IconType;
export declare const LuPlug2: IconType;
export declare const LuPlugZap: IconType;
export declare const LuPlug: IconType;
export declare const LuPlus: IconType;
export declare const LuPocketKnife: IconType;
export declare const LuPocket: IconType;
export declare const LuPodcast: IconType;
export declare const LuPointerOff: IconType;
export declare const LuPointer: IconType;
export declare const LuPopcorn: IconType;
export declare const LuPopsicle: IconType;
export declare const LuPoundSterling: IconType;
export declare const LuPowerOff: IconType;
export declare const LuPower: IconType;
export declare const LuPresentation: IconType;
export declare const LuPrinterCheck: IconType;
export declare const LuPrinter: IconType;
export declare const LuProjector: IconType;
export declare const LuProportions: IconType;
export declare const LuPuzzle: IconType;
export declare const LuPyramid: IconType;
export declare const LuQrCode: IconType;
export declare const LuQuote: IconType;
export declare const LuRabbit: IconType;
export declare const LuRadar: IconType;
export declare const LuRadiation: IconType;
export declare const LuRadical: IconType;
export declare const LuRadioReceiver: IconType;
export declare const LuRadioTower: IconType;
export declare const LuRadio: IconType;
export declare const LuRadius: IconType;
export declare const LuRailSymbol: IconType;
export declare const LuRainbow: IconType;
export declare const LuRat: IconType;
export declare const LuRatio: IconType;
export declare const LuReceiptCent: IconType;
export declare const LuReceiptEuro: IconType;
export declare const LuReceiptIndianRupee: IconType;
export declare const LuReceiptJapaneseYen: IconType;
export declare const LuReceiptPoundSterling: IconType;
export declare const LuReceiptRussianRuble: IconType;
export declare const LuReceiptSwissFranc: IconType;
export declare const LuReceiptText: IconType;
export declare const LuReceipt: IconType;
export declare const LuRectangleEllipsis: IconType;
export declare const LuRectangleHorizontal: IconType;
export declare const LuRectangleVertical: IconType;
export declare const LuRecycle: IconType;
export declare const LuRedo2: IconType;
export declare const LuRedoDot: IconType;
export declare const LuRedo: IconType;
export declare const LuRefreshCcwDot: IconType;
export declare const LuRefreshCcw: IconType;
export declare const LuRefreshCwOff: IconType;
export declare const LuRefreshCw: IconType;
export declare const LuRefrigerator: IconType;
export declare const LuRegex: IconType;
export declare const LuRemoveFormatting: IconType;
export declare const LuRepeat1: IconType;
export declare const LuRepeat2: IconType;
export declare const LuRepeat: IconType;
export declare const LuReplaceAll: IconType;
export declare const LuReplace: IconType;
export declare const LuReplyAll: IconType;
export declare const LuReply: IconType;
export declare const LuRewind: IconType;
export declare const LuRibbon: IconType;
export declare const LuRocket: IconType;
export declare const LuRockingChair: IconType;
export declare const LuRollerCoaster: IconType;
export declare const LuRotate3D: IconType;
export declare const LuRotateCcwSquare: IconType;
export declare const LuRotateCcw: IconType;
export declare const LuRotateCwSquare: IconType;
export declare const LuRotateCw: IconType;
export declare const LuRouteOff: IconType;
export declare const LuRoute: IconType;
export declare const LuRouter: IconType;
export declare const LuRows2: IconType;
export declare const LuRows3: IconType;
export declare const LuRows4: IconType;
export declare const LuRss: IconType;
export declare const LuRuler: IconType;
export declare const LuRussianRuble: IconType;
export declare const LuSailboat: IconType;
export declare const LuSalad: IconType;
export declare const LuSandwich: IconType;
export declare const LuSatelliteDish: IconType;
export declare const LuSatellite: IconType;
export declare const LuSaveAll: IconType;
export declare const LuSaveOff: IconType;
export declare const LuSave: IconType;
export declare const LuScale3D: IconType;
export declare const LuScale: IconType;
export declare const LuScaling: IconType;
export declare const LuScanBarcode: IconType;
export declare const LuScanEye: IconType;
export declare const LuScanFace: IconType;
export declare const LuScanLine: IconType;
export declare const LuScanQrCode: IconType;
export declare const LuScanSearch: IconType;
export declare const LuScanText: IconType;
export declare const LuScan: IconType;
export declare const LuSchool: IconType;
export declare const LuScissorsLineDashed: IconType;
export declare const LuScissors: IconType;
export declare const LuScreenShareOff: IconType;
export declare const LuScreenShare: IconType;
export declare const LuScrollText: IconType;
export declare const LuScroll: IconType;
export declare const LuSearchCheck: IconType;
export declare const LuSearchCode: IconType;
export declare const LuSearchSlash: IconType;
export declare const LuSearchX: IconType;
export declare const LuSearch: IconType;
export declare const LuSection: IconType;
export declare const LuSendHorizontal: IconType;
export declare const LuSendToBack: IconType;
export declare const LuSend: IconType;
export declare const LuSeparatorHorizontal: IconType;
export declare const LuSeparatorVertical: IconType;
export declare const LuServerCog: IconType;
export declare const LuServerCrash: IconType;
export declare const LuServerOff: IconType;
export declare const LuServer: IconType;
export declare const LuSettings2: IconType;
export declare const LuSettings: IconType;
export declare const LuShapes: IconType;
export declare const LuShare2: IconType;
export declare const LuShare: IconType;
export declare const LuSheet: IconType;
export declare const LuShell: IconType;
export declare const LuShieldAlert: IconType;
export declare const LuShieldBan: IconType;
export declare const LuShieldCheck: IconType;
export declare const LuShieldEllipsis: IconType;
export declare const LuShieldHalf: IconType;
export declare const LuShieldMinus: IconType;
export declare const LuShieldOff: IconType;
export declare const LuShieldPlus: IconType;
export declare const LuShieldQuestion: IconType;
export declare const LuShieldX: IconType;
export declare const LuShield: IconType;
export declare const LuShipWheel: IconType;
export declare const LuShip: IconType;
export declare const LuShirt: IconType;
export declare const LuShoppingBag: IconType;
export declare const LuShoppingBasket: IconType;
export declare const LuShoppingCart: IconType;
export declare const LuShovel: IconType;
export declare const LuShowerHead: IconType;
export declare const LuShrink: IconType;
export declare const LuShrub: IconType;
export declare const LuShuffle: IconType;
export declare const LuSigma: IconType;
export declare const LuSignalHigh: IconType;
export declare const LuSignalLow: IconType;
export declare const LuSignalMedium: IconType;
export declare const LuSignalZero: IconType;
export declare const LuSignal: IconType;
export declare const LuSignature: IconType;
export declare const LuSignpostBig: IconType;
export declare const LuSignpost: IconType;
export declare const LuSiren: IconType;
export declare const LuSkipBack: IconType;
export declare const LuSkipForward: IconType;
export declare const LuSkull: IconType;
export declare const LuSlack: IconType;
export declare const LuSlash: IconType;
export declare const LuSlice: IconType;
export declare const LuSlidersHorizontal: IconType;
export declare const LuSlidersVertical: IconType;
export declare const LuSmartphoneCharging: IconType;
export declare const LuSmartphoneNfc: IconType;
export declare const LuSmartphone: IconType;
export declare const LuSmilePlus: IconType;
export declare const LuSmile: IconType;
export declare const LuSnail: IconType;
export declare const LuSnowflake: IconType;
export declare const LuSofa: IconType;
export declare const LuSoup: IconType;
export declare const LuSpace: IconType;
export declare const LuSpade: IconType;
export declare const LuSparkle: IconType;
export declare const LuSparkles: IconType;
export declare const LuSpeaker: IconType;
export declare const LuSpeech: IconType;
export declare const LuSpellCheck2: IconType;
export declare const LuSpellCheck: IconType;
export declare const LuSpline: IconType;
export declare const LuSplit: IconType;
export declare const LuSprayCan: IconType;
export declare const LuSprout: IconType;
export declare const LuSquareActivity: IconType;
export declare const LuSquareArrowDownLeft: IconType;
export declare const LuSquareArrowDownRight: IconType;
export declare const LuSquareArrowDown: IconType;
export declare const LuSquareArrowLeft: IconType;
export declare const LuSquareArrowOutDownLeft: IconType;
export declare const LuSquareArrowOutDownRight: IconType;
export declare const LuSquareArrowOutUpLeft: IconType;
export declare const LuSquareArrowOutUpRight: IconType;
export declare const LuSquareArrowRight: IconType;
export declare const LuSquareArrowUpLeft: IconType;
export declare const LuSquareArrowUpRight: IconType;
export declare const LuSquareArrowUp: IconType;
export declare const LuSquareAsterisk: IconType;
export declare const LuSquareBottomDashedScissors: IconType;
export declare const LuSquareChartGantt: IconType;
export declare const LuSquareCheckBig: IconType;
export declare const LuSquareCheck: IconType;
export declare const LuSquareChevronDown: IconType;
export declare const LuSquareChevronLeft: IconType;
export declare const LuSquareChevronRight: IconType;
export declare const LuSquareChevronUp: IconType;
export declare const LuSquareCode: IconType;
export declare const LuSquareDashedBottomCode: IconType;
export declare const LuSquareDashedBottom: IconType;
export declare const LuSquareDashedKanban: IconType;
export declare const LuSquareDashedMousePointer: IconType;
export declare const LuSquareDashed: IconType;
export declare const LuSquareDivide: IconType;
export declare const LuSquareDot: IconType;
export declare const LuSquareEqual: IconType;
export declare const LuSquareFunction: IconType;
export declare const LuSquareKanban: IconType;
export declare const LuSquareLibrary: IconType;
export declare const LuSquareM: IconType;
export declare const LuSquareMenu: IconType;
export declare const LuSquareMinus: IconType;
export declare const LuSquareMousePointer: IconType;
export declare const LuSquareParkingOff: IconType;
export declare const LuSquareParking: IconType;
export declare const LuSquarePen: IconType;
export declare const LuSquarePercent: IconType;
export declare const LuSquarePi: IconType;
export declare const LuSquarePilcrow: IconType;
export declare const LuSquarePlay: IconType;
export declare const LuSquarePlus: IconType;
export declare const LuSquarePower: IconType;
export declare const LuSquareRadical: IconType;
export declare const LuSquareScissors: IconType;
export declare const LuSquareSigma: IconType;
export declare const LuSquareSlash: IconType;
export declare const LuSquareSplitHorizontal: IconType;
export declare const LuSquareSplitVertical: IconType;
export declare const LuSquareSquare: IconType;
export declare const LuSquareStack: IconType;
export declare const LuSquareTerminal: IconType;
export declare const LuSquareUserRound: IconType;
export declare const LuSquareUser: IconType;
export declare const LuSquareX: IconType;
export declare const LuSquare: IconType;
export declare const LuSquircle: IconType;
export declare const LuSquirrel: IconType;
export declare const LuStamp: IconType;
export declare const LuStarHalf: IconType;
export declare const LuStarOff: IconType;
export declare const LuStar: IconType;
export declare const LuStepBack: IconType;
export declare const LuStepForward: IconType;
export declare const LuStethoscope: IconType;
export declare const LuSticker: IconType;
export declare const LuStickyNote: IconType;
export declare const LuStore: IconType;
export declare const LuStretchHorizontal: IconType;
export declare const LuStretchVertical: IconType;
export declare const LuStrikethrough: IconType;
export declare const LuSubscript: IconType;
export declare const LuSunDim: IconType;
export declare const LuSunMedium: IconType;
export declare const LuSunMoon: IconType;
export declare const LuSunSnow: IconType;
export declare const LuSun: IconType;
export declare const LuSunrise: IconType;
export declare const LuSunset: IconType;
export declare const LuSuperscript: IconType;
export declare const LuSwatchBook: IconType;
export declare const LuSwissFranc: IconType;
export declare const LuSwitchCamera: IconType;
export declare const LuSword: IconType;
export declare const LuSwords: IconType;
export declare const LuSyringe: IconType;
export declare const LuTable2: IconType;
export declare const LuTableCellsMerge: IconType;
export declare const LuTableCellsSplit: IconType;
export declare const LuTableColumnsSplit: IconType;
export declare const LuTableOfContents: IconType;
export declare const LuTableProperties: IconType;
export declare const LuTableRowsSplit: IconType;
export declare const LuTable: IconType;
export declare const LuTabletSmartphone: IconType;
export declare const LuTablet: IconType;
export declare const LuTablets: IconType;
export declare const LuTag: IconType;
export declare const LuTags: IconType;
export declare const LuTally1: IconType;
export declare const LuTally2: IconType;
export declare const LuTally3: IconType;
export declare const LuTally4: IconType;
export declare const LuTally5: IconType;
export declare const LuTangent: IconType;
export declare const LuTarget: IconType;
export declare const LuTelescope: IconType;
export declare const LuTentTree: IconType;
export declare const LuTent: IconType;
export declare const LuTerminal: IconType;
export declare const LuTestTubeDiagonal: IconType;
export declare const LuTestTube: IconType;
export declare const LuTestTubes: IconType;
export declare const LuTextCursorInput: IconType;
export declare const LuTextCursor: IconType;
export declare const LuTextQuote: IconType;
export declare const LuTextSearch: IconType;
export declare const LuTextSelect: IconType;
export declare const LuText: IconType;
export declare const LuTheater: IconType;
export declare const LuThermometerSnowflake: IconType;
export declare const LuThermometerSun: IconType;
export declare const LuThermometer: IconType;
export declare const LuThumbsDown: IconType;
export declare const LuThumbsUp: IconType;
export declare const LuTicketCheck: IconType;
export declare const LuTicketMinus: IconType;
export declare const LuTicketPercent: IconType;
export declare const LuTicketPlus: IconType;
export declare const LuTicketSlash: IconType;
export declare const LuTicketX: IconType;
export declare const LuTicket: IconType;
export declare const LuTicketsPlane: IconType;
export declare const LuTickets: IconType;
export declare const LuTimerOff: IconType;
export declare const LuTimerReset: IconType;
export declare const LuTimer: IconType;
export declare const LuToggleLeft: IconType;
export declare const LuToggleRight: IconType;
export declare const LuToilet: IconType;
export declare const LuTornado: IconType;
export declare const LuTorus: IconType;
export declare const LuTouchpadOff: IconType;
export declare const LuTouchpad: IconType;
export declare const LuTowerControl: IconType;
export declare const LuToyBrick: IconType;
export declare const LuTractor: IconType;
export declare const LuTrafficCone: IconType;
export declare const LuTrainFrontTunnel: IconType;
export declare const LuTrainFront: IconType;
export declare const LuTrainTrack: IconType;
export declare const LuTramFront: IconType;
export declare const LuTrash2: IconType;
export declare const LuTrash: IconType;
export declare const LuTreeDeciduous: IconType;
export declare const LuTreePalm: IconType;
export declare const LuTreePine: IconType;
export declare const LuTrees: IconType;
export declare const LuTrello: IconType;
export declare const LuTrendingDown: IconType;
export declare const LuTrendingUpDown: IconType;
export declare const LuTrendingUp: IconType;
export declare const LuTriangleAlert: IconType;
export declare const LuTriangleRight: IconType;
export declare const LuTriangle: IconType;
export declare const LuTrophy: IconType;
export declare const LuTruck: IconType;
export declare const LuTurtle: IconType;
export declare const LuTvMinimalPlay: IconType;
export declare const LuTvMinimal: IconType;
export declare const LuTv: IconType;
export declare const LuTwitch: IconType;
export declare const LuTwitter: IconType;
export declare const LuTypeOutline: IconType;
export declare const LuType: IconType;
export declare const LuUmbrellaOff: IconType;
export declare const LuUmbrella: IconType;
export declare const LuUnderline: IconType;
export declare const LuUndo2: IconType;
export declare const LuUndoDot: IconType;
export declare const LuUndo: IconType;
export declare const LuUnfoldHorizontal: IconType;
export declare const LuUnfoldVertical: IconType;
export declare const LuUngroup: IconType;
export declare const LuUniversity: IconType;
export declare const LuUnlink2: IconType;
export declare const LuUnlink: IconType;
export declare const LuUnplug: IconType;
export declare const LuUpload: IconType;
export declare const LuUsb: IconType;
export declare const LuUserCheck: IconType;
export declare const LuUserCog: IconType;
export declare const LuUserMinus: IconType;
export declare const LuUserPen: IconType;
export declare const LuUserPlus: IconType;
export declare const LuUserRoundCheck: IconType;
export declare const LuUserRoundCog: IconType;
export declare const LuUserRoundMinus: IconType;
export declare const LuUserRoundPen: IconType;
export declare const LuUserRoundPlus: IconType;
export declare const LuUserRoundSearch: IconType;
export declare const LuUserRoundX: IconType;
export declare const LuUserRound: IconType;
export declare const LuUserSearch: IconType;
export declare const LuUserX: IconType;
export declare const LuUser: IconType;
export declare const LuUsersRound: IconType;
export declare const LuUsers: IconType;
export declare const LuUtensilsCrossed: IconType;
export declare const LuUtensils: IconType;
export declare const LuUtilityPole: IconType;
export declare const LuVariable: IconType;
export declare const LuVault: IconType;
export declare const LuVegan: IconType;
export declare const LuVenetianMask: IconType;
export declare const LuVibrateOff: IconType;
export declare const LuVibrate: IconType;
export declare const LuVideoOff: IconType;
export declare const LuVideo: IconType;
export declare const LuVideotape: IconType;
export declare const LuView: IconType;
export declare const LuVoicemail: IconType;
export declare const LuVolleyball: IconType;
export declare const LuVolume1: IconType;
export declare const LuVolume2: IconType;
export declare const LuVolumeOff: IconType;
export declare const LuVolumeX: IconType;
export declare const LuVolume: IconType;
export declare const LuVote: IconType;
export declare const LuWalletCards: IconType;
export declare const LuWalletMinimal: IconType;
export declare const LuWallet: IconType;
export declare const LuWallpaper: IconType;
export declare const LuWandSparkles: IconType;
export declare const LuWand: IconType;
export declare const LuWarehouse: IconType;
export declare const LuWashingMachine: IconType;
export declare const LuWatch: IconType;
export declare const LuWaves: IconType;
export declare const LuWaypoints: IconType;
export declare const LuWebcam: IconType;
export declare const LuWebhookOff: IconType;
export declare const LuWebhook: IconType;
export declare const LuWeight: IconType;
export declare const LuWheatOff: IconType;
export declare const LuWheat: IconType;
export declare const LuWholeWord: IconType;
export declare const LuWifiHigh: IconType;
export declare const LuWifiLow: IconType;
export declare const LuWifiOff: IconType;
export declare const LuWifiZero: IconType;
export declare const LuWifi: IconType;
export declare const LuWindArrowDown: IconType;
export declare const LuWind: IconType;
export declare const LuWineOff: IconType;
export declare const LuWine: IconType;
export declare const LuWorkflow: IconType;
export declare const LuWorm: IconType;
export declare const LuWrapText: IconType;
export declare const LuWrench: IconType;
export declare const LuX: IconType;
export declare const LuYoutube: IconType;
export declare const LuZapOff: IconType;
export declare const LuZap: IconType;
export declare const LuZoomIn: IconType;
export declare const LuZoomOut: IconType;
