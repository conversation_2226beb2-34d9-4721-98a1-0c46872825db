// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const PiAcornBold: IconType;
export declare const PiAddressBookBold: IconType;
export declare const PiAddressBookTabsBold: IconType;
export declare const PiAirTrafficControlBold: IconType;
export declare const PiAirplaneBold: IconType;
export declare const PiAirplaneInFlightBold: IconType;
export declare const PiAirplaneLandingBold: IconType;
export declare const PiAirplaneTakeoffBold: IconType;
export declare const PiAirplaneTaxiingBold: IconType;
export declare const PiAirplaneTiltBold: IconType;
export declare const PiAirplayBold: IconType;
export declare const PiAlarmBold: IconType;
export declare const PiAlienBold: IconType;
export declare const PiAlignBottomBold: IconType;
export declare const PiAlignBottomSimpleBold: IconType;
export declare const PiAlignCenterHorizontalBold: IconType;
export declare const PiAlignCenterHorizontalSimpleBold: IconType;
export declare const PiAlignCenterVerticalBold: IconType;
export declare const PiAlignCenterVerticalSimpleBold: IconType;
export declare const PiAlignLeftBold: IconType;
export declare const PiAlignLeftSimpleBold: IconType;
export declare const PiAlignRightBold: IconType;
export declare const PiAlignRightSimpleBold: IconType;
export declare const PiAlignTopBold: IconType;
export declare const PiAlignTopSimpleBold: IconType;
export declare const PiAmazonLogoBold: IconType;
export declare const PiAmbulanceBold: IconType;
export declare const PiAnchorBold: IconType;
export declare const PiAnchorSimpleBold: IconType;
export declare const PiAndroidLogoBold: IconType;
export declare const PiAngleBold: IconType;
export declare const PiAngularLogoBold: IconType;
export declare const PiApertureBold: IconType;
export declare const PiAppStoreLogoBold: IconType;
export declare const PiAppWindowBold: IconType;
export declare const PiAppleLogoBold: IconType;
export declare const PiApplePodcastsLogoBold: IconType;
export declare const PiApproximateEqualsBold: IconType;
export declare const PiArchiveBold: IconType;
export declare const PiArmchairBold: IconType;
export declare const PiArrowArcLeftBold: IconType;
export declare const PiArrowArcRightBold: IconType;
export declare const PiArrowBendDoubleUpLeftBold: IconType;
export declare const PiArrowBendDoubleUpRightBold: IconType;
export declare const PiArrowBendDownLeftBold: IconType;
export declare const PiArrowBendDownRightBold: IconType;
export declare const PiArrowBendLeftDownBold: IconType;
export declare const PiArrowBendLeftUpBold: IconType;
export declare const PiArrowBendRightDownBold: IconType;
export declare const PiArrowBendRightUpBold: IconType;
export declare const PiArrowBendUpLeftBold: IconType;
export declare const PiArrowBendUpRightBold: IconType;
export declare const PiArrowCircleDownBold: IconType;
export declare const PiArrowCircleDownLeftBold: IconType;
export declare const PiArrowCircleDownRightBold: IconType;
export declare const PiArrowCircleLeftBold: IconType;
export declare const PiArrowCircleRightBold: IconType;
export declare const PiArrowCircleUpBold: IconType;
export declare const PiArrowCircleUpLeftBold: IconType;
export declare const PiArrowCircleUpRightBold: IconType;
export declare const PiArrowClockwiseBold: IconType;
export declare const PiArrowCounterClockwiseBold: IconType;
export declare const PiArrowDownBold: IconType;
export declare const PiArrowDownLeftBold: IconType;
export declare const PiArrowDownRightBold: IconType;
export declare const PiArrowElbowDownLeftBold: IconType;
export declare const PiArrowElbowDownRightBold: IconType;
export declare const PiArrowElbowLeftBold: IconType;
export declare const PiArrowElbowLeftDownBold: IconType;
export declare const PiArrowElbowLeftUpBold: IconType;
export declare const PiArrowElbowRightBold: IconType;
export declare const PiArrowElbowRightDownBold: IconType;
export declare const PiArrowElbowRightUpBold: IconType;
export declare const PiArrowElbowUpLeftBold: IconType;
export declare const PiArrowElbowUpRightBold: IconType;
export declare const PiArrowFatDownBold: IconType;
export declare const PiArrowFatLeftBold: IconType;
export declare const PiArrowFatLineDownBold: IconType;
export declare const PiArrowFatLineLeftBold: IconType;
export declare const PiArrowFatLineRightBold: IconType;
export declare const PiArrowFatLineUpBold: IconType;
export declare const PiArrowFatLinesDownBold: IconType;
export declare const PiArrowFatLinesLeftBold: IconType;
export declare const PiArrowFatLinesRightBold: IconType;
export declare const PiArrowFatLinesUpBold: IconType;
export declare const PiArrowFatRightBold: IconType;
export declare const PiArrowFatUpBold: IconType;
export declare const PiArrowLeftBold: IconType;
export declare const PiArrowLineDownBold: IconType;
export declare const PiArrowLineDownLeftBold: IconType;
export declare const PiArrowLineDownRightBold: IconType;
export declare const PiArrowLineLeftBold: IconType;
export declare const PiArrowLineRightBold: IconType;
export declare const PiArrowLineUpBold: IconType;
export declare const PiArrowLineUpLeftBold: IconType;
export declare const PiArrowLineUpRightBold: IconType;
export declare const PiArrowRightBold: IconType;
export declare const PiArrowSquareDownBold: IconType;
export declare const PiArrowSquareDownLeftBold: IconType;
export declare const PiArrowSquareDownRightBold: IconType;
export declare const PiArrowSquareInBold: IconType;
export declare const PiArrowSquareLeftBold: IconType;
export declare const PiArrowSquareOutBold: IconType;
export declare const PiArrowSquareRightBold: IconType;
export declare const PiArrowSquareUpBold: IconType;
export declare const PiArrowSquareUpLeftBold: IconType;
export declare const PiArrowSquareUpRightBold: IconType;
export declare const PiArrowUDownLeftBold: IconType;
export declare const PiArrowUDownRightBold: IconType;
export declare const PiArrowULeftDownBold: IconType;
export declare const PiArrowULeftUpBold: IconType;
export declare const PiArrowURightDownBold: IconType;
export declare const PiArrowURightUpBold: IconType;
export declare const PiArrowUUpLeftBold: IconType;
export declare const PiArrowUUpRightBold: IconType;
export declare const PiArrowUpBold: IconType;
export declare const PiArrowUpLeftBold: IconType;
export declare const PiArrowUpRightBold: IconType;
export declare const PiArrowsClockwiseBold: IconType;
export declare const PiArrowsCounterClockwiseBold: IconType;
export declare const PiArrowsDownUpBold: IconType;
export declare const PiArrowsHorizontalBold: IconType;
export declare const PiArrowsInBold: IconType;
export declare const PiArrowsInCardinalBold: IconType;
export declare const PiArrowsInLineHorizontalBold: IconType;
export declare const PiArrowsInLineVerticalBold: IconType;
export declare const PiArrowsInSimpleBold: IconType;
export declare const PiArrowsLeftRightBold: IconType;
export declare const PiArrowsMergeBold: IconType;
export declare const PiArrowsOutBold: IconType;
export declare const PiArrowsOutCardinalBold: IconType;
export declare const PiArrowsOutLineHorizontalBold: IconType;
export declare const PiArrowsOutLineVerticalBold: IconType;
export declare const PiArrowsOutSimpleBold: IconType;
export declare const PiArrowsSplitBold: IconType;
export declare const PiArrowsVerticalBold: IconType;
export declare const PiArticleBold: IconType;
export declare const PiArticleMediumBold: IconType;
export declare const PiArticleNyTimesBold: IconType;
export declare const PiAsclepiusBold: IconType;
export declare const PiAsteriskBold: IconType;
export declare const PiAsteriskSimpleBold: IconType;
export declare const PiAtBold: IconType;
export declare const PiAtomBold: IconType;
export declare const PiAvocadoBold: IconType;
export declare const PiAxeBold: IconType;
export declare const PiBabyBold: IconType;
export declare const PiBabyCarriageBold: IconType;
export declare const PiBackpackBold: IconType;
export declare const PiBackspaceBold: IconType;
export declare const PiBagBold: IconType;
export declare const PiBagSimpleBold: IconType;
export declare const PiBalloonBold: IconType;
export declare const PiBandaidsBold: IconType;
export declare const PiBankBold: IconType;
export declare const PiBarbellBold: IconType;
export declare const PiBarcodeBold: IconType;
export declare const PiBarnBold: IconType;
export declare const PiBarricadeBold: IconType;
export declare const PiBaseballBold: IconType;
export declare const PiBaseballCapBold: IconType;
export declare const PiBaseballHelmetBold: IconType;
export declare const PiBasketBold: IconType;
export declare const PiBasketballBold: IconType;
export declare const PiBathtubBold: IconType;
export declare const PiBatteryChargingBold: IconType;
export declare const PiBatteryChargingVerticalBold: IconType;
export declare const PiBatteryEmptyBold: IconType;
export declare const PiBatteryFullBold: IconType;
export declare const PiBatteryHighBold: IconType;
export declare const PiBatteryLowBold: IconType;
export declare const PiBatteryMediumBold: IconType;
export declare const PiBatteryPlusBold: IconType;
export declare const PiBatteryPlusVerticalBold: IconType;
export declare const PiBatteryVerticalEmptyBold: IconType;
export declare const PiBatteryVerticalFullBold: IconType;
export declare const PiBatteryVerticalHighBold: IconType;
export declare const PiBatteryVerticalLowBold: IconType;
export declare const PiBatteryVerticalMediumBold: IconType;
export declare const PiBatteryWarningBold: IconType;
export declare const PiBatteryWarningVerticalBold: IconType;
export declare const PiBeachBallBold: IconType;
export declare const PiBeanieBold: IconType;
export declare const PiBedBold: IconType;
export declare const PiBeerBottleBold: IconType;
export declare const PiBeerSteinBold: IconType;
export declare const PiBehanceLogoBold: IconType;
export declare const PiBellBold: IconType;
export declare const PiBellRingingBold: IconType;
export declare const PiBellSimpleBold: IconType;
export declare const PiBellSimpleRingingBold: IconType;
export declare const PiBellSimpleSlashBold: IconType;
export declare const PiBellSimpleZBold: IconType;
export declare const PiBellSlashBold: IconType;
export declare const PiBellZBold: IconType;
export declare const PiBeltBold: IconType;
export declare const PiBezierCurveBold: IconType;
export declare const PiBicycleBold: IconType;
export declare const PiBinaryBold: IconType;
export declare const PiBinocularsBold: IconType;
export declare const PiBiohazardBold: IconType;
export declare const PiBirdBold: IconType;
export declare const PiBlueprintBold: IconType;
export declare const PiBluetoothBold: IconType;
export declare const PiBluetoothConnectedBold: IconType;
export declare const PiBluetoothSlashBold: IconType;
export declare const PiBluetoothXBold: IconType;
export declare const PiBoatBold: IconType;
export declare const PiBombBold: IconType;
export declare const PiBoneBold: IconType;
export declare const PiBookBold: IconType;
export declare const PiBookBookmarkBold: IconType;
export declare const PiBookOpenBold: IconType;
export declare const PiBookOpenTextBold: IconType;
export declare const PiBookOpenUserBold: IconType;
export declare const PiBookmarkBold: IconType;
export declare const PiBookmarkSimpleBold: IconType;
export declare const PiBookmarksBold: IconType;
export declare const PiBookmarksSimpleBold: IconType;
export declare const PiBooksBold: IconType;
export declare const PiBootBold: IconType;
export declare const PiBoulesBold: IconType;
export declare const PiBoundingBoxBold: IconType;
export declare const PiBowlFoodBold: IconType;
export declare const PiBowlSteamBold: IconType;
export declare const PiBowlingBallBold: IconType;
export declare const PiBoxArrowDownBold: IconType;
export declare const PiBoxArrowUpBold: IconType;
export declare const PiBoxingGloveBold: IconType;
export declare const PiBracketsAngleBold: IconType;
export declare const PiBracketsCurlyBold: IconType;
export declare const PiBracketsRoundBold: IconType;
export declare const PiBracketsSquareBold: IconType;
export declare const PiBrainBold: IconType;
export declare const PiBrandyBold: IconType;
export declare const PiBreadBold: IconType;
export declare const PiBridgeBold: IconType;
export declare const PiBriefcaseBold: IconType;
export declare const PiBriefcaseMetalBold: IconType;
export declare const PiBroadcastBold: IconType;
export declare const PiBroomBold: IconType;
export declare const PiBrowserBold: IconType;
export declare const PiBrowsersBold: IconType;
export declare const PiBugBeetleBold: IconType;
export declare const PiBugBold: IconType;
export declare const PiBugDroidBold: IconType;
export declare const PiBuildingApartmentBold: IconType;
export declare const PiBuildingBold: IconType;
export declare const PiBuildingOfficeBold: IconType;
export declare const PiBuildingsBold: IconType;
export declare const PiBulldozerBold: IconType;
export declare const PiBusBold: IconType;
export declare const PiButterflyBold: IconType;
export declare const PiCableCarBold: IconType;
export declare const PiCactusBold: IconType;
export declare const PiCakeBold: IconType;
export declare const PiCalculatorBold: IconType;
export declare const PiCalendarBlankBold: IconType;
export declare const PiCalendarBold: IconType;
export declare const PiCalendarCheckBold: IconType;
export declare const PiCalendarDotBold: IconType;
export declare const PiCalendarDotsBold: IconType;
export declare const PiCalendarHeartBold: IconType;
export declare const PiCalendarMinusBold: IconType;
export declare const PiCalendarPlusBold: IconType;
export declare const PiCalendarSlashBold: IconType;
export declare const PiCalendarStarBold: IconType;
export declare const PiCalendarXBold: IconType;
export declare const PiCallBellBold: IconType;
export declare const PiCameraBold: IconType;
export declare const PiCameraPlusBold: IconType;
export declare const PiCameraRotateBold: IconType;
export declare const PiCameraSlashBold: IconType;
export declare const PiCampfireBold: IconType;
export declare const PiCarBatteryBold: IconType;
export declare const PiCarBold: IconType;
export declare const PiCarProfileBold: IconType;
export declare const PiCarSimpleBold: IconType;
export declare const PiCardholderBold: IconType;
export declare const PiCardsBold: IconType;
export declare const PiCardsThreeBold: IconType;
export declare const PiCaretCircleDoubleDownBold: IconType;
export declare const PiCaretCircleDoubleLeftBold: IconType;
export declare const PiCaretCircleDoubleRightBold: IconType;
export declare const PiCaretCircleDoubleUpBold: IconType;
export declare const PiCaretCircleDownBold: IconType;
export declare const PiCaretCircleLeftBold: IconType;
export declare const PiCaretCircleRightBold: IconType;
export declare const PiCaretCircleUpBold: IconType;
export declare const PiCaretCircleUpDownBold: IconType;
export declare const PiCaretDoubleDownBold: IconType;
export declare const PiCaretDoubleLeftBold: IconType;
export declare const PiCaretDoubleRightBold: IconType;
export declare const PiCaretDoubleUpBold: IconType;
export declare const PiCaretDownBold: IconType;
export declare const PiCaretLeftBold: IconType;
export declare const PiCaretLineDownBold: IconType;
export declare const PiCaretLineLeftBold: IconType;
export declare const PiCaretLineRightBold: IconType;
export declare const PiCaretLineUpBold: IconType;
export declare const PiCaretRightBold: IconType;
export declare const PiCaretUpBold: IconType;
export declare const PiCaretUpDownBold: IconType;
export declare const PiCarrotBold: IconType;
export declare const PiCashRegisterBold: IconType;
export declare const PiCassetteTapeBold: IconType;
export declare const PiCastleTurretBold: IconType;
export declare const PiCatBold: IconType;
export declare const PiCellSignalFullBold: IconType;
export declare const PiCellSignalHighBold: IconType;
export declare const PiCellSignalLowBold: IconType;
export declare const PiCellSignalMediumBold: IconType;
export declare const PiCellSignalNoneBold: IconType;
export declare const PiCellSignalSlashBold: IconType;
export declare const PiCellSignalXBold: IconType;
export declare const PiCellTowerBold: IconType;
export declare const PiCertificateBold: IconType;
export declare const PiChairBold: IconType;
export declare const PiChalkboardBold: IconType;
export declare const PiChalkboardSimpleBold: IconType;
export declare const PiChalkboardTeacherBold: IconType;
export declare const PiChampagneBold: IconType;
export declare const PiChargingStationBold: IconType;
export declare const PiChartBarBold: IconType;
export declare const PiChartBarHorizontalBold: IconType;
export declare const PiChartDonutBold: IconType;
export declare const PiChartLineBold: IconType;
export declare const PiChartLineDownBold: IconType;
export declare const PiChartLineUpBold: IconType;
export declare const PiChartPieBold: IconType;
export declare const PiChartPieSliceBold: IconType;
export declare const PiChartPolarBold: IconType;
export declare const PiChartScatterBold: IconType;
export declare const PiChatBold: IconType;
export declare const PiChatCenteredBold: IconType;
export declare const PiChatCenteredDotsBold: IconType;
export declare const PiChatCenteredSlashBold: IconType;
export declare const PiChatCenteredTextBold: IconType;
export declare const PiChatCircleBold: IconType;
export declare const PiChatCircleDotsBold: IconType;
export declare const PiChatCircleSlashBold: IconType;
export declare const PiChatCircleTextBold: IconType;
export declare const PiChatDotsBold: IconType;
export declare const PiChatSlashBold: IconType;
export declare const PiChatTeardropBold: IconType;
export declare const PiChatTeardropDotsBold: IconType;
export declare const PiChatTeardropSlashBold: IconType;
export declare const PiChatTeardropTextBold: IconType;
export declare const PiChatTextBold: IconType;
export declare const PiChatsBold: IconType;
export declare const PiChatsCircleBold: IconType;
export declare const PiChatsTeardropBold: IconType;
export declare const PiCheckBold: IconType;
export declare const PiCheckCircleBold: IconType;
export declare const PiCheckFatBold: IconType;
export declare const PiCheckSquareBold: IconType;
export declare const PiCheckSquareOffsetBold: IconType;
export declare const PiCheckerboardBold: IconType;
export declare const PiChecksBold: IconType;
export declare const PiCheersBold: IconType;
export declare const PiCheeseBold: IconType;
export declare const PiChefHatBold: IconType;
export declare const PiCherriesBold: IconType;
export declare const PiChurchBold: IconType;
export declare const PiCigaretteBold: IconType;
export declare const PiCigaretteSlashBold: IconType;
export declare const PiCircleBold: IconType;
export declare const PiCircleDashedBold: IconType;
export declare const PiCircleHalfBold: IconType;
export declare const PiCircleHalfTiltBold: IconType;
export declare const PiCircleNotchBold: IconType;
export declare const PiCirclesFourBold: IconType;
export declare const PiCirclesThreeBold: IconType;
export declare const PiCirclesThreePlusBold: IconType;
export declare const PiCircuitryBold: IconType;
export declare const PiCityBold: IconType;
export declare const PiClipboardBold: IconType;
export declare const PiClipboardTextBold: IconType;
export declare const PiClockAfternoonBold: IconType;
export declare const PiClockBold: IconType;
export declare const PiClockClockwiseBold: IconType;
export declare const PiClockCountdownBold: IconType;
export declare const PiClockCounterClockwiseBold: IconType;
export declare const PiClockUserBold: IconType;
export declare const PiClosedCaptioningBold: IconType;
export declare const PiCloudArrowDownBold: IconType;
export declare const PiCloudArrowUpBold: IconType;
export declare const PiCloudBold: IconType;
export declare const PiCloudCheckBold: IconType;
export declare const PiCloudFogBold: IconType;
export declare const PiCloudLightningBold: IconType;
export declare const PiCloudMoonBold: IconType;
export declare const PiCloudRainBold: IconType;
export declare const PiCloudSlashBold: IconType;
export declare const PiCloudSnowBold: IconType;
export declare const PiCloudSunBold: IconType;
export declare const PiCloudWarningBold: IconType;
export declare const PiCloudXBold: IconType;
export declare const PiCloverBold: IconType;
export declare const PiClubBold: IconType;
export declare const PiCoatHangerBold: IconType;
export declare const PiCodaLogoBold: IconType;
export declare const PiCodeBlockBold: IconType;
export declare const PiCodeBold: IconType;
export declare const PiCodeSimpleBold: IconType;
export declare const PiCodepenLogoBold: IconType;
export declare const PiCodesandboxLogoBold: IconType;
export declare const PiCoffeeBeanBold: IconType;
export declare const PiCoffeeBold: IconType;
export declare const PiCoinBold: IconType;
export declare const PiCoinVerticalBold: IconType;
export declare const PiCoinsBold: IconType;
export declare const PiColumnsBold: IconType;
export declare const PiColumnsPlusLeftBold: IconType;
export declare const PiColumnsPlusRightBold: IconType;
export declare const PiCommandBold: IconType;
export declare const PiCompassBold: IconType;
export declare const PiCompassRoseBold: IconType;
export declare const PiCompassToolBold: IconType;
export declare const PiComputerTowerBold: IconType;
export declare const PiConfettiBold: IconType;
export declare const PiContactlessPaymentBold: IconType;
export declare const PiControlBold: IconType;
export declare const PiCookieBold: IconType;
export declare const PiCookingPotBold: IconType;
export declare const PiCopyBold: IconType;
export declare const PiCopySimpleBold: IconType;
export declare const PiCopyleftBold: IconType;
export declare const PiCopyrightBold: IconType;
export declare const PiCornersInBold: IconType;
export declare const PiCornersOutBold: IconType;
export declare const PiCouchBold: IconType;
export declare const PiCourtBasketballBold: IconType;
export declare const PiCowBold: IconType;
export declare const PiCowboyHatBold: IconType;
export declare const PiCpuBold: IconType;
export declare const PiCraneBold: IconType;
export declare const PiCraneTowerBold: IconType;
export declare const PiCreditCardBold: IconType;
export declare const PiCricketBold: IconType;
export declare const PiCropBold: IconType;
export declare const PiCrossBold: IconType;
export declare const PiCrosshairBold: IconType;
export declare const PiCrosshairSimpleBold: IconType;
export declare const PiCrownBold: IconType;
export declare const PiCrownCrossBold: IconType;
export declare const PiCrownSimpleBold: IconType;
export declare const PiCubeBold: IconType;
export declare const PiCubeFocusBold: IconType;
export declare const PiCubeTransparentBold: IconType;
export declare const PiCurrencyBtcBold: IconType;
export declare const PiCurrencyCircleDollarBold: IconType;
export declare const PiCurrencyCnyBold: IconType;
export declare const PiCurrencyDollarBold: IconType;
export declare const PiCurrencyDollarSimpleBold: IconType;
export declare const PiCurrencyEthBold: IconType;
export declare const PiCurrencyEurBold: IconType;
export declare const PiCurrencyGbpBold: IconType;
export declare const PiCurrencyInrBold: IconType;
export declare const PiCurrencyJpyBold: IconType;
export declare const PiCurrencyKrwBold: IconType;
export declare const PiCurrencyKztBold: IconType;
export declare const PiCurrencyNgnBold: IconType;
export declare const PiCurrencyRubBold: IconType;
export declare const PiCursorBold: IconType;
export declare const PiCursorClickBold: IconType;
export declare const PiCursorTextBold: IconType;
export declare const PiCylinderBold: IconType;
export declare const PiDatabaseBold: IconType;
export declare const PiDeskBold: IconType;
export declare const PiDesktopBold: IconType;
export declare const PiDesktopTowerBold: IconType;
export declare const PiDetectiveBold: IconType;
export declare const PiDevToLogoBold: IconType;
export declare const PiDeviceMobileBold: IconType;
export declare const PiDeviceMobileCameraBold: IconType;
export declare const PiDeviceMobileSlashBold: IconType;
export declare const PiDeviceMobileSpeakerBold: IconType;
export declare const PiDeviceRotateBold: IconType;
export declare const PiDeviceTabletBold: IconType;
export declare const PiDeviceTabletCameraBold: IconType;
export declare const PiDeviceTabletSpeakerBold: IconType;
export declare const PiDevicesBold: IconType;
export declare const PiDiamondBold: IconType;
export declare const PiDiamondsFourBold: IconType;
export declare const PiDiceFiveBold: IconType;
export declare const PiDiceFourBold: IconType;
export declare const PiDiceOneBold: IconType;
export declare const PiDiceSixBold: IconType;
export declare const PiDiceThreeBold: IconType;
export declare const PiDiceTwoBold: IconType;
export declare const PiDiscBold: IconType;
export declare const PiDiscoBallBold: IconType;
export declare const PiDiscordLogoBold: IconType;
export declare const PiDivideBold: IconType;
export declare const PiDnaBold: IconType;
export declare const PiDogBold: IconType;
export declare const PiDoorBold: IconType;
export declare const PiDoorOpenBold: IconType;
export declare const PiDotBold: IconType;
export declare const PiDotOutlineBold: IconType;
export declare const PiDotsNineBold: IconType;
export declare const PiDotsSixBold: IconType;
export declare const PiDotsSixVerticalBold: IconType;
export declare const PiDotsThreeBold: IconType;
export declare const PiDotsThreeCircleBold: IconType;
export declare const PiDotsThreeCircleVerticalBold: IconType;
export declare const PiDotsThreeOutlineBold: IconType;
export declare const PiDotsThreeOutlineVerticalBold: IconType;
export declare const PiDotsThreeVerticalBold: IconType;
export declare const PiDownloadBold: IconType;
export declare const PiDownloadSimpleBold: IconType;
export declare const PiDressBold: IconType;
export declare const PiDresserBold: IconType;
export declare const PiDribbbleLogoBold: IconType;
export declare const PiDroneBold: IconType;
export declare const PiDropBold: IconType;
export declare const PiDropHalfBold: IconType;
export declare const PiDropHalfBottomBold: IconType;
export declare const PiDropSimpleBold: IconType;
export declare const PiDropSlashBold: IconType;
export declare const PiDropboxLogoBold: IconType;
export declare const PiEarBold: IconType;
export declare const PiEarSlashBold: IconType;
export declare const PiEggBold: IconType;
export declare const PiEggCrackBold: IconType;
export declare const PiEjectBold: IconType;
export declare const PiEjectSimpleBold: IconType;
export declare const PiElevatorBold: IconType;
export declare const PiEmptyBold: IconType;
export declare const PiEngineBold: IconType;
export declare const PiEnvelopeBold: IconType;
export declare const PiEnvelopeOpenBold: IconType;
export declare const PiEnvelopeSimpleBold: IconType;
export declare const PiEnvelopeSimpleOpenBold: IconType;
export declare const PiEqualizerBold: IconType;
export declare const PiEqualsBold: IconType;
export declare const PiEraserBold: IconType;
export declare const PiEscalatorDownBold: IconType;
export declare const PiEscalatorUpBold: IconType;
export declare const PiExamBold: IconType;
export declare const PiExclamationMarkBold: IconType;
export declare const PiExcludeBold: IconType;
export declare const PiExcludeSquareBold: IconType;
export declare const PiExportBold: IconType;
export declare const PiEyeBold: IconType;
export declare const PiEyeClosedBold: IconType;
export declare const PiEyeSlashBold: IconType;
export declare const PiEyedropperBold: IconType;
export declare const PiEyedropperSampleBold: IconType;
export declare const PiEyeglassesBold: IconType;
export declare const PiEyesBold: IconType;
export declare const PiFaceMaskBold: IconType;
export declare const PiFacebookLogoBold: IconType;
export declare const PiFactoryBold: IconType;
export declare const PiFadersBold: IconType;
export declare const PiFadersHorizontalBold: IconType;
export declare const PiFalloutShelterBold: IconType;
export declare const PiFanBold: IconType;
export declare const PiFarmBold: IconType;
export declare const PiFastForwardBold: IconType;
export declare const PiFastForwardCircleBold: IconType;
export declare const PiFeatherBold: IconType;
export declare const PiFediverseLogoBold: IconType;
export declare const PiFigmaLogoBold: IconType;
export declare const PiFileArchiveBold: IconType;
export declare const PiFileArrowDownBold: IconType;
export declare const PiFileArrowUpBold: IconType;
export declare const PiFileAudioBold: IconType;
export declare const PiFileBold: IconType;
export declare const PiFileCBold: IconType;
export declare const PiFileCSharpBold: IconType;
export declare const PiFileCloudBold: IconType;
export declare const PiFileCodeBold: IconType;
export declare const PiFileCppBold: IconType;
export declare const PiFileCssBold: IconType;
export declare const PiFileCsvBold: IconType;
export declare const PiFileDashedBold: IconType;
export declare const PiFileDocBold: IconType;
export declare const PiFileHtmlBold: IconType;
export declare const PiFileImageBold: IconType;
export declare const PiFileIniBold: IconType;
export declare const PiFileJpgBold: IconType;
export declare const PiFileJsBold: IconType;
export declare const PiFileJsxBold: IconType;
export declare const PiFileLockBold: IconType;
export declare const PiFileMagnifyingGlassBold: IconType;
export declare const PiFileMdBold: IconType;
export declare const PiFileMinusBold: IconType;
export declare const PiFilePdfBold: IconType;
export declare const PiFilePlusBold: IconType;
export declare const PiFilePngBold: IconType;
export declare const PiFilePptBold: IconType;
export declare const PiFilePyBold: IconType;
export declare const PiFileRsBold: IconType;
export declare const PiFileSqlBold: IconType;
export declare const PiFileSvgBold: IconType;
export declare const PiFileTextBold: IconType;
export declare const PiFileTsBold: IconType;
export declare const PiFileTsxBold: IconType;
export declare const PiFileTxtBold: IconType;
export declare const PiFileVideoBold: IconType;
export declare const PiFileVueBold: IconType;
export declare const PiFileXBold: IconType;
export declare const PiFileXlsBold: IconType;
export declare const PiFileZipBold: IconType;
export declare const PiFilesBold: IconType;
export declare const PiFilmReelBold: IconType;
export declare const PiFilmScriptBold: IconType;
export declare const PiFilmSlateBold: IconType;
export declare const PiFilmStripBold: IconType;
export declare const PiFingerprintBold: IconType;
export declare const PiFingerprintSimpleBold: IconType;
export declare const PiFinnTheHumanBold: IconType;
export declare const PiFireBold: IconType;
export declare const PiFireExtinguisherBold: IconType;
export declare const PiFireSimpleBold: IconType;
export declare const PiFireTruckBold: IconType;
export declare const PiFirstAidBold: IconType;
export declare const PiFirstAidKitBold: IconType;
export declare const PiFishBold: IconType;
export declare const PiFishSimpleBold: IconType;
export declare const PiFlagBannerBold: IconType;
export declare const PiFlagBannerFoldBold: IconType;
export declare const PiFlagBold: IconType;
export declare const PiFlagCheckeredBold: IconType;
export declare const PiFlagPennantBold: IconType;
export declare const PiFlameBold: IconType;
export declare const PiFlashlightBold: IconType;
export declare const PiFlaskBold: IconType;
export declare const PiFlipHorizontalBold: IconType;
export declare const PiFlipVerticalBold: IconType;
export declare const PiFloppyDiskBackBold: IconType;
export declare const PiFloppyDiskBold: IconType;
export declare const PiFlowArrowBold: IconType;
export declare const PiFlowerBold: IconType;
export declare const PiFlowerLotusBold: IconType;
export declare const PiFlowerTulipBold: IconType;
export declare const PiFlyingSaucerBold: IconType;
export declare const PiFolderBold: IconType;
export declare const PiFolderDashedBold: IconType;
export declare const PiFolderLockBold: IconType;
export declare const PiFolderMinusBold: IconType;
export declare const PiFolderOpenBold: IconType;
export declare const PiFolderPlusBold: IconType;
export declare const PiFolderSimpleBold: IconType;
export declare const PiFolderSimpleDashedBold: IconType;
export declare const PiFolderSimpleLockBold: IconType;
export declare const PiFolderSimpleMinusBold: IconType;
export declare const PiFolderSimplePlusBold: IconType;
export declare const PiFolderSimpleStarBold: IconType;
export declare const PiFolderSimpleUserBold: IconType;
export declare const PiFolderStarBold: IconType;
export declare const PiFolderUserBold: IconType;
export declare const PiFoldersBold: IconType;
export declare const PiFootballBold: IconType;
export declare const PiFootballHelmetBold: IconType;
export declare const PiFootprintsBold: IconType;
export declare const PiForkKnifeBold: IconType;
export declare const PiFourKBold: IconType;
export declare const PiFrameCornersBold: IconType;
export declare const PiFramerLogoBold: IconType;
export declare const PiFunctionBold: IconType;
export declare const PiFunnelBold: IconType;
export declare const PiFunnelSimpleBold: IconType;
export declare const PiFunnelSimpleXBold: IconType;
export declare const PiFunnelXBold: IconType;
export declare const PiGameControllerBold: IconType;
export declare const PiGarageBold: IconType;
export declare const PiGasCanBold: IconType;
export declare const PiGasPumpBold: IconType;
export declare const PiGaugeBold: IconType;
export declare const PiGavelBold: IconType;
export declare const PiGearBold: IconType;
export declare const PiGearFineBold: IconType;
export declare const PiGearSixBold: IconType;
export declare const PiGenderFemaleBold: IconType;
export declare const PiGenderIntersexBold: IconType;
export declare const PiGenderMaleBold: IconType;
export declare const PiGenderNeuterBold: IconType;
export declare const PiGenderNonbinaryBold: IconType;
export declare const PiGenderTransgenderBold: IconType;
export declare const PiGhostBold: IconType;
export declare const PiGifBold: IconType;
export declare const PiGiftBold: IconType;
export declare const PiGitBranchBold: IconType;
export declare const PiGitCommitBold: IconType;
export declare const PiGitDiffBold: IconType;
export declare const PiGitForkBold: IconType;
export declare const PiGitMergeBold: IconType;
export declare const PiGitPullRequestBold: IconType;
export declare const PiGithubLogoBold: IconType;
export declare const PiGitlabLogoBold: IconType;
export declare const PiGitlabLogoSimpleBold: IconType;
export declare const PiGlobeBold: IconType;
export declare const PiGlobeHemisphereEastBold: IconType;
export declare const PiGlobeHemisphereWestBold: IconType;
export declare const PiGlobeSimpleBold: IconType;
export declare const PiGlobeSimpleXBold: IconType;
export declare const PiGlobeStandBold: IconType;
export declare const PiGlobeXBold: IconType;
export declare const PiGogglesBold: IconType;
export declare const PiGolfBold: IconType;
export declare const PiGoodreadsLogoBold: IconType;
export declare const PiGoogleCardboardLogoBold: IconType;
export declare const PiGoogleChromeLogoBold: IconType;
export declare const PiGoogleDriveLogoBold: IconType;
export declare const PiGoogleLogoBold: IconType;
export declare const PiGooglePhotosLogoBold: IconType;
export declare const PiGooglePlayLogoBold: IconType;
export declare const PiGooglePodcastsLogoBold: IconType;
export declare const PiGpsBold: IconType;
export declare const PiGpsFixBold: IconType;
export declare const PiGpsSlashBold: IconType;
export declare const PiGradientBold: IconType;
export declare const PiGraduationCapBold: IconType;
export declare const PiGrainsBold: IconType;
export declare const PiGrainsSlashBold: IconType;
export declare const PiGraphBold: IconType;
export declare const PiGraphicsCardBold: IconType;
export declare const PiGreaterThanBold: IconType;
export declare const PiGreaterThanOrEqualBold: IconType;
export declare const PiGridFourBold: IconType;
export declare const PiGridNineBold: IconType;
export declare const PiGuitarBold: IconType;
export declare const PiHairDryerBold: IconType;
export declare const PiHamburgerBold: IconType;
export declare const PiHammerBold: IconType;
export declare const PiHandArrowDownBold: IconType;
export declare const PiHandArrowUpBold: IconType;
export declare const PiHandBold: IconType;
export declare const PiHandCoinsBold: IconType;
export declare const PiHandDepositBold: IconType;
export declare const PiHandEyeBold: IconType;
export declare const PiHandFistBold: IconType;
export declare const PiHandGrabbingBold: IconType;
export declare const PiHandHeartBold: IconType;
export declare const PiHandPalmBold: IconType;
export declare const PiHandPeaceBold: IconType;
export declare const PiHandPointingBold: IconType;
export declare const PiHandSoapBold: IconType;
export declare const PiHandSwipeLeftBold: IconType;
export declare const PiHandSwipeRightBold: IconType;
export declare const PiHandTapBold: IconType;
export declare const PiHandWavingBold: IconType;
export declare const PiHandWithdrawBold: IconType;
export declare const PiHandbagBold: IconType;
export declare const PiHandbagSimpleBold: IconType;
export declare const PiHandsClappingBold: IconType;
export declare const PiHandsPrayingBold: IconType;
export declare const PiHandshakeBold: IconType;
export declare const PiHardDriveBold: IconType;
export declare const PiHardDrivesBold: IconType;
export declare const PiHardHatBold: IconType;
export declare const PiHashBold: IconType;
export declare const PiHashStraightBold: IconType;
export declare const PiHeadCircuitBold: IconType;
export declare const PiHeadlightsBold: IconType;
export declare const PiHeadphonesBold: IconType;
export declare const PiHeadsetBold: IconType;
export declare const PiHeartBold: IconType;
export declare const PiHeartBreakBold: IconType;
export declare const PiHeartHalfBold: IconType;
export declare const PiHeartStraightBold: IconType;
export declare const PiHeartStraightBreakBold: IconType;
export declare const PiHeartbeatBold: IconType;
export declare const PiHexagonBold: IconType;
export declare const PiHighDefinitionBold: IconType;
export declare const PiHighHeelBold: IconType;
export declare const PiHighlighterBold: IconType;
export declare const PiHighlighterCircleBold: IconType;
export declare const PiHockeyBold: IconType;
export declare const PiHoodieBold: IconType;
export declare const PiHorseBold: IconType;
export declare const PiHospitalBold: IconType;
export declare const PiHourglassBold: IconType;
export declare const PiHourglassHighBold: IconType;
export declare const PiHourglassLowBold: IconType;
export declare const PiHourglassMediumBold: IconType;
export declare const PiHourglassSimpleBold: IconType;
export declare const PiHourglassSimpleHighBold: IconType;
export declare const PiHourglassSimpleLowBold: IconType;
export declare const PiHourglassSimpleMediumBold: IconType;
export declare const PiHouseBold: IconType;
export declare const PiHouseLineBold: IconType;
export declare const PiHouseSimpleBold: IconType;
export declare const PiHurricaneBold: IconType;
export declare const PiIceCreamBold: IconType;
export declare const PiIdentificationBadgeBold: IconType;
export declare const PiIdentificationCardBold: IconType;
export declare const PiImageBold: IconType;
export declare const PiImageBrokenBold: IconType;
export declare const PiImageSquareBold: IconType;
export declare const PiImagesBold: IconType;
export declare const PiImagesSquareBold: IconType;
export declare const PiInfinityBold: IconType;
export declare const PiInfoBold: IconType;
export declare const PiInstagramLogoBold: IconType;
export declare const PiIntersectBold: IconType;
export declare const PiIntersectSquareBold: IconType;
export declare const PiIntersectThreeBold: IconType;
export declare const PiIntersectionBold: IconType;
export declare const PiInvoiceBold: IconType;
export declare const PiIslandBold: IconType;
export declare const PiJarBold: IconType;
export declare const PiJarLabelBold: IconType;
export declare const PiJeepBold: IconType;
export declare const PiJoystickBold: IconType;
export declare const PiKanbanBold: IconType;
export declare const PiKeyBold: IconType;
export declare const PiKeyReturnBold: IconType;
export declare const PiKeyboardBold: IconType;
export declare const PiKeyholeBold: IconType;
export declare const PiKnifeBold: IconType;
export declare const PiLadderBold: IconType;
export declare const PiLadderSimpleBold: IconType;
export declare const PiLampBold: IconType;
export declare const PiLampPendantBold: IconType;
export declare const PiLaptopBold: IconType;
export declare const PiLassoBold: IconType;
export declare const PiLastfmLogoBold: IconType;
export declare const PiLayoutBold: IconType;
export declare const PiLeafBold: IconType;
export declare const PiLecternBold: IconType;
export declare const PiLegoBold: IconType;
export declare const PiLegoSmileyBold: IconType;
export declare const PiLessThanBold: IconType;
export declare const PiLessThanOrEqualBold: IconType;
export declare const PiLetterCircleHBold: IconType;
export declare const PiLetterCirclePBold: IconType;
export declare const PiLetterCircleVBold: IconType;
export declare const PiLifebuoyBold: IconType;
export declare const PiLightbulbBold: IconType;
export declare const PiLightbulbFilamentBold: IconType;
export declare const PiLighthouseBold: IconType;
export declare const PiLightningABold: IconType;
export declare const PiLightningBold: IconType;
export declare const PiLightningSlashBold: IconType;
export declare const PiLineSegmentBold: IconType;
export declare const PiLineSegmentsBold: IconType;
export declare const PiLineVerticalBold: IconType;
export declare const PiLinkBold: IconType;
export declare const PiLinkBreakBold: IconType;
export declare const PiLinkSimpleBold: IconType;
export declare const PiLinkSimpleBreakBold: IconType;
export declare const PiLinkSimpleHorizontalBold: IconType;
export declare const PiLinkSimpleHorizontalBreakBold: IconType;
export declare const PiLinkedinLogoBold: IconType;
export declare const PiLinktreeLogoBold: IconType;
export declare const PiLinuxLogoBold: IconType;
export declare const PiListBold: IconType;
export declare const PiListBulletsBold: IconType;
export declare const PiListChecksBold: IconType;
export declare const PiListDashesBold: IconType;
export declare const PiListHeartBold: IconType;
export declare const PiListMagnifyingGlassBold: IconType;
export declare const PiListNumbersBold: IconType;
export declare const PiListPlusBold: IconType;
export declare const PiListStarBold: IconType;
export declare const PiLockBold: IconType;
export declare const PiLockKeyBold: IconType;
export declare const PiLockKeyOpenBold: IconType;
export declare const PiLockLaminatedBold: IconType;
export declare const PiLockLaminatedOpenBold: IconType;
export declare const PiLockOpenBold: IconType;
export declare const PiLockSimpleBold: IconType;
export declare const PiLockSimpleOpenBold: IconType;
export declare const PiLockersBold: IconType;
export declare const PiLogBold: IconType;
export declare const PiMagicWandBold: IconType;
export declare const PiMagnetBold: IconType;
export declare const PiMagnetStraightBold: IconType;
export declare const PiMagnifyingGlassBold: IconType;
export declare const PiMagnifyingGlassMinusBold: IconType;
export declare const PiMagnifyingGlassPlusBold: IconType;
export declare const PiMailboxBold: IconType;
export declare const PiMapPinAreaBold: IconType;
export declare const PiMapPinBold: IconType;
export declare const PiMapPinLineBold: IconType;
export declare const PiMapPinPlusBold: IconType;
export declare const PiMapPinSimpleAreaBold: IconType;
export declare const PiMapPinSimpleBold: IconType;
export declare const PiMapPinSimpleLineBold: IconType;
export declare const PiMapTrifoldBold: IconType;
export declare const PiMarkdownLogoBold: IconType;
export declare const PiMarkerCircleBold: IconType;
export declare const PiMartiniBold: IconType;
export declare const PiMaskHappyBold: IconType;
export declare const PiMaskSadBold: IconType;
export declare const PiMastodonLogoBold: IconType;
export declare const PiMathOperationsBold: IconType;
export declare const PiMatrixLogoBold: IconType;
export declare const PiMedalBold: IconType;
export declare const PiMedalMilitaryBold: IconType;
export declare const PiMediumLogoBold: IconType;
export declare const PiMegaphoneBold: IconType;
export declare const PiMegaphoneSimpleBold: IconType;
export declare const PiMemberOfBold: IconType;
export declare const PiMemoryBold: IconType;
export declare const PiMessengerLogoBold: IconType;
export declare const PiMetaLogoBold: IconType;
export declare const PiMeteorBold: IconType;
export declare const PiMetronomeBold: IconType;
export declare const PiMicrophoneBold: IconType;
export declare const PiMicrophoneSlashBold: IconType;
export declare const PiMicrophoneStageBold: IconType;
export declare const PiMicroscopeBold: IconType;
export declare const PiMicrosoftExcelLogoBold: IconType;
export declare const PiMicrosoftOutlookLogoBold: IconType;
export declare const PiMicrosoftPowerpointLogoBold: IconType;
export declare const PiMicrosoftTeamsLogoBold: IconType;
export declare const PiMicrosoftWordLogoBold: IconType;
export declare const PiMinusBold: IconType;
export declare const PiMinusCircleBold: IconType;
export declare const PiMinusSquareBold: IconType;
export declare const PiMoneyBold: IconType;
export declare const PiMoneyWavyBold: IconType;
export declare const PiMonitorArrowUpBold: IconType;
export declare const PiMonitorBold: IconType;
export declare const PiMonitorPlayBold: IconType;
export declare const PiMoonBold: IconType;
export declare const PiMoonStarsBold: IconType;
export declare const PiMopedBold: IconType;
export declare const PiMopedFrontBold: IconType;
export declare const PiMosqueBold: IconType;
export declare const PiMotorcycleBold: IconType;
export declare const PiMountainsBold: IconType;
export declare const PiMouseBold: IconType;
export declare const PiMouseLeftClickBold: IconType;
export declare const PiMouseMiddleClickBold: IconType;
export declare const PiMouseRightClickBold: IconType;
export declare const PiMouseScrollBold: IconType;
export declare const PiMouseSimpleBold: IconType;
export declare const PiMusicNoteBold: IconType;
export declare const PiMusicNoteSimpleBold: IconType;
export declare const PiMusicNotesBold: IconType;
export declare const PiMusicNotesMinusBold: IconType;
export declare const PiMusicNotesPlusBold: IconType;
export declare const PiMusicNotesSimpleBold: IconType;
export declare const PiNavigationArrowBold: IconType;
export declare const PiNeedleBold: IconType;
export declare const PiNetworkBold: IconType;
export declare const PiNetworkSlashBold: IconType;
export declare const PiNetworkXBold: IconType;
export declare const PiNewspaperBold: IconType;
export declare const PiNewspaperClippingBold: IconType;
export declare const PiNotEqualsBold: IconType;
export declare const PiNotMemberOfBold: IconType;
export declare const PiNotSubsetOfBold: IconType;
export declare const PiNotSupersetOfBold: IconType;
export declare const PiNotchesBold: IconType;
export declare const PiNoteBlankBold: IconType;
export declare const PiNoteBold: IconType;
export declare const PiNotePencilBold: IconType;
export declare const PiNotebookBold: IconType;
export declare const PiNotepadBold: IconType;
export declare const PiNotificationBold: IconType;
export declare const PiNotionLogoBold: IconType;
export declare const PiNuclearPlantBold: IconType;
export declare const PiNumberCircleEightBold: IconType;
export declare const PiNumberCircleFiveBold: IconType;
export declare const PiNumberCircleFourBold: IconType;
export declare const PiNumberCircleNineBold: IconType;
export declare const PiNumberCircleOneBold: IconType;
export declare const PiNumberCircleSevenBold: IconType;
export declare const PiNumberCircleSixBold: IconType;
export declare const PiNumberCircleThreeBold: IconType;
export declare const PiNumberCircleTwoBold: IconType;
export declare const PiNumberCircleZeroBold: IconType;
export declare const PiNumberEightBold: IconType;
export declare const PiNumberFiveBold: IconType;
export declare const PiNumberFourBold: IconType;
export declare const PiNumberNineBold: IconType;
export declare const PiNumberOneBold: IconType;
export declare const PiNumberSevenBold: IconType;
export declare const PiNumberSixBold: IconType;
export declare const PiNumberSquareEightBold: IconType;
export declare const PiNumberSquareFiveBold: IconType;
export declare const PiNumberSquareFourBold: IconType;
export declare const PiNumberSquareNineBold: IconType;
export declare const PiNumberSquareOneBold: IconType;
export declare const PiNumberSquareSevenBold: IconType;
export declare const PiNumberSquareSixBold: IconType;
export declare const PiNumberSquareThreeBold: IconType;
export declare const PiNumberSquareTwoBold: IconType;
export declare const PiNumberSquareZeroBold: IconType;
export declare const PiNumberThreeBold: IconType;
export declare const PiNumberTwoBold: IconType;
export declare const PiNumberZeroBold: IconType;
export declare const PiNumpadBold: IconType;
export declare const PiNutBold: IconType;
export declare const PiNyTimesLogoBold: IconType;
export declare const PiOctagonBold: IconType;
export declare const PiOfficeChairBold: IconType;
export declare const PiOnigiriBold: IconType;
export declare const PiOpenAiLogoBold: IconType;
export declare const PiOptionBold: IconType;
export declare const PiOrangeBold: IconType;
export declare const PiOrangeSliceBold: IconType;
export declare const PiOvenBold: IconType;
export declare const PiPackageBold: IconType;
export declare const PiPaintBrushBold: IconType;
export declare const PiPaintBrushBroadBold: IconType;
export declare const PiPaintBrushHouseholdBold: IconType;
export declare const PiPaintBucketBold: IconType;
export declare const PiPaintRollerBold: IconType;
export declare const PiPaletteBold: IconType;
export declare const PiPanoramaBold: IconType;
export declare const PiPantsBold: IconType;
export declare const PiPaperPlaneBold: IconType;
export declare const PiPaperPlaneRightBold: IconType;
export declare const PiPaperPlaneTiltBold: IconType;
export declare const PiPaperclipBold: IconType;
export declare const PiPaperclipHorizontalBold: IconType;
export declare const PiParachuteBold: IconType;
export declare const PiParagraphBold: IconType;
export declare const PiParallelogramBold: IconType;
export declare const PiParkBold: IconType;
export declare const PiPasswordBold: IconType;
export declare const PiPathBold: IconType;
export declare const PiPatreonLogoBold: IconType;
export declare const PiPauseBold: IconType;
export declare const PiPauseCircleBold: IconType;
export declare const PiPawPrintBold: IconType;
export declare const PiPaypalLogoBold: IconType;
export declare const PiPeaceBold: IconType;
export declare const PiPenBold: IconType;
export declare const PiPenNibBold: IconType;
export declare const PiPenNibStraightBold: IconType;
export declare const PiPencilBold: IconType;
export declare const PiPencilCircleBold: IconType;
export declare const PiPencilLineBold: IconType;
export declare const PiPencilRulerBold: IconType;
export declare const PiPencilSimpleBold: IconType;
export declare const PiPencilSimpleLineBold: IconType;
export declare const PiPencilSimpleSlashBold: IconType;
export declare const PiPencilSlashBold: IconType;
export declare const PiPentagonBold: IconType;
export declare const PiPentagramBold: IconType;
export declare const PiPepperBold: IconType;
export declare const PiPercentBold: IconType;
export declare const PiPersonArmsSpreadBold: IconType;
export declare const PiPersonBold: IconType;
export declare const PiPersonSimpleBikeBold: IconType;
export declare const PiPersonSimpleBold: IconType;
export declare const PiPersonSimpleCircleBold: IconType;
export declare const PiPersonSimpleHikeBold: IconType;
export declare const PiPersonSimpleRunBold: IconType;
export declare const PiPersonSimpleSkiBold: IconType;
export declare const PiPersonSimpleSnowboardBold: IconType;
export declare const PiPersonSimpleSwimBold: IconType;
export declare const PiPersonSimpleTaiChiBold: IconType;
export declare const PiPersonSimpleThrowBold: IconType;
export declare const PiPersonSimpleWalkBold: IconType;
export declare const PiPerspectiveBold: IconType;
export declare const PiPhoneBold: IconType;
export declare const PiPhoneCallBold: IconType;
export declare const PiPhoneDisconnectBold: IconType;
export declare const PiPhoneIncomingBold: IconType;
export declare const PiPhoneListBold: IconType;
export declare const PiPhoneOutgoingBold: IconType;
export declare const PiPhonePauseBold: IconType;
export declare const PiPhonePlusBold: IconType;
export declare const PiPhoneSlashBold: IconType;
export declare const PiPhoneTransferBold: IconType;
export declare const PiPhoneXBold: IconType;
export declare const PiPhosphorLogoBold: IconType;
export declare const PiPiBold: IconType;
export declare const PiPianoKeysBold: IconType;
export declare const PiPicnicTableBold: IconType;
export declare const PiPictureInPictureBold: IconType;
export declare const PiPiggyBankBold: IconType;
export declare const PiPillBold: IconType;
export declare const PiPingPongBold: IconType;
export declare const PiPintGlassBold: IconType;
export declare const PiPinterestLogoBold: IconType;
export declare const PiPinwheelBold: IconType;
export declare const PiPipeBold: IconType;
export declare const PiPipeWrenchBold: IconType;
export declare const PiPixLogoBold: IconType;
export declare const PiPizzaBold: IconType;
export declare const PiPlaceholderBold: IconType;
export declare const PiPlanetBold: IconType;
export declare const PiPlantBold: IconType;
export declare const PiPlayBold: IconType;
export declare const PiPlayCircleBold: IconType;
export declare const PiPlayPauseBold: IconType;
export declare const PiPlaylistBold: IconType;
export declare const PiPlugBold: IconType;
export declare const PiPlugChargingBold: IconType;
export declare const PiPlugsBold: IconType;
export declare const PiPlugsConnectedBold: IconType;
export declare const PiPlusBold: IconType;
export declare const PiPlusCircleBold: IconType;
export declare const PiPlusMinusBold: IconType;
export declare const PiPlusSquareBold: IconType;
export declare const PiPokerChipBold: IconType;
export declare const PiPoliceCarBold: IconType;
export declare const PiPolygonBold: IconType;
export declare const PiPopcornBold: IconType;
export declare const PiPopsicleBold: IconType;
export declare const PiPottedPlantBold: IconType;
export declare const PiPowerBold: IconType;
export declare const PiPrescriptionBold: IconType;
export declare const PiPresentationBold: IconType;
export declare const PiPresentationChartBold: IconType;
export declare const PiPrinterBold: IconType;
export declare const PiProhibitBold: IconType;
export declare const PiProhibitInsetBold: IconType;
export declare const PiProjectorScreenBold: IconType;
export declare const PiProjectorScreenChartBold: IconType;
export declare const PiPulseBold: IconType;
export declare const PiPushPinBold: IconType;
export declare const PiPushPinSimpleBold: IconType;
export declare const PiPushPinSimpleSlashBold: IconType;
export declare const PiPushPinSlashBold: IconType;
export declare const PiPuzzlePieceBold: IconType;
export declare const PiQrCodeBold: IconType;
export declare const PiQuestionBold: IconType;
export declare const PiQuestionMarkBold: IconType;
export declare const PiQueueBold: IconType;
export declare const PiQuotesBold: IconType;
export declare const PiRabbitBold: IconType;
export declare const PiRacquetBold: IconType;
export declare const PiRadicalBold: IconType;
export declare const PiRadioBold: IconType;
export declare const PiRadioButtonBold: IconType;
export declare const PiRadioactiveBold: IconType;
export declare const PiRainbowBold: IconType;
export declare const PiRainbowCloudBold: IconType;
export declare const PiRankingBold: IconType;
export declare const PiReadCvLogoBold: IconType;
export declare const PiReceiptBold: IconType;
export declare const PiReceiptXBold: IconType;
export declare const PiRecordBold: IconType;
export declare const PiRectangleBold: IconType;
export declare const PiRectangleDashedBold: IconType;
export declare const PiRecycleBold: IconType;
export declare const PiRedditLogoBold: IconType;
export declare const PiRepeatBold: IconType;
export declare const PiRepeatOnceBold: IconType;
export declare const PiReplitLogoBold: IconType;
export declare const PiResizeBold: IconType;
export declare const PiRewindBold: IconType;
export declare const PiRewindCircleBold: IconType;
export declare const PiRoadHorizonBold: IconType;
export declare const PiRobotBold: IconType;
export declare const PiRocketBold: IconType;
export declare const PiRocketLaunchBold: IconType;
export declare const PiRowsBold: IconType;
export declare const PiRowsPlusBottomBold: IconType;
export declare const PiRowsPlusTopBold: IconType;
export declare const PiRssBold: IconType;
export declare const PiRssSimpleBold: IconType;
export declare const PiRugBold: IconType;
export declare const PiRulerBold: IconType;
export declare const PiSailboatBold: IconType;
export declare const PiScalesBold: IconType;
export declare const PiScanBold: IconType;
export declare const PiScanSmileyBold: IconType;
export declare const PiScissorsBold: IconType;
export declare const PiScooterBold: IconType;
export declare const PiScreencastBold: IconType;
export declare const PiScrewdriverBold: IconType;
export declare const PiScribbleBold: IconType;
export declare const PiScribbleLoopBold: IconType;
export declare const PiScrollBold: IconType;
export declare const PiSealBold: IconType;
export declare const PiSealCheckBold: IconType;
export declare const PiSealPercentBold: IconType;
export declare const PiSealQuestionBold: IconType;
export declare const PiSealWarningBold: IconType;
export declare const PiSeatBold: IconType;
export declare const PiSeatbeltBold: IconType;
export declare const PiSecurityCameraBold: IconType;
export declare const PiSelectionAllBold: IconType;
export declare const PiSelectionBackgroundBold: IconType;
export declare const PiSelectionBold: IconType;
export declare const PiSelectionForegroundBold: IconType;
export declare const PiSelectionInverseBold: IconType;
export declare const PiSelectionPlusBold: IconType;
export declare const PiSelectionSlashBold: IconType;
export declare const PiShapesBold: IconType;
export declare const PiShareBold: IconType;
export declare const PiShareFatBold: IconType;
export declare const PiShareNetworkBold: IconType;
export declare const PiShieldBold: IconType;
export declare const PiShieldCheckBold: IconType;
export declare const PiShieldCheckeredBold: IconType;
export declare const PiShieldChevronBold: IconType;
export declare const PiShieldPlusBold: IconType;
export declare const PiShieldSlashBold: IconType;
export declare const PiShieldStarBold: IconType;
export declare const PiShieldWarningBold: IconType;
export declare const PiShippingContainerBold: IconType;
export declare const PiShirtFoldedBold: IconType;
export declare const PiShootingStarBold: IconType;
export declare const PiShoppingBagBold: IconType;
export declare const PiShoppingBagOpenBold: IconType;
export declare const PiShoppingCartBold: IconType;
export declare const PiShoppingCartSimpleBold: IconType;
export declare const PiShovelBold: IconType;
export declare const PiShowerBold: IconType;
export declare const PiShrimpBold: IconType;
export declare const PiShuffleAngularBold: IconType;
export declare const PiShuffleBold: IconType;
export declare const PiShuffleSimpleBold: IconType;
export declare const PiSidebarBold: IconType;
export declare const PiSidebarSimpleBold: IconType;
export declare const PiSigmaBold: IconType;
export declare const PiSignInBold: IconType;
export declare const PiSignOutBold: IconType;
export declare const PiSignatureBold: IconType;
export declare const PiSignpostBold: IconType;
export declare const PiSimCardBold: IconType;
export declare const PiSirenBold: IconType;
export declare const PiSketchLogoBold: IconType;
export declare const PiSkipBackBold: IconType;
export declare const PiSkipBackCircleBold: IconType;
export declare const PiSkipForwardBold: IconType;
export declare const PiSkipForwardCircleBold: IconType;
export declare const PiSkullBold: IconType;
export declare const PiSkypeLogoBold: IconType;
export declare const PiSlackLogoBold: IconType;
export declare const PiSlidersBold: IconType;
export declare const PiSlidersHorizontalBold: IconType;
export declare const PiSlideshowBold: IconType;
export declare const PiSmileyAngryBold: IconType;
export declare const PiSmileyBlankBold: IconType;
export declare const PiSmileyBold: IconType;
export declare const PiSmileyMehBold: IconType;
export declare const PiSmileyMeltingBold: IconType;
export declare const PiSmileyNervousBold: IconType;
export declare const PiSmileySadBold: IconType;
export declare const PiSmileyStickerBold: IconType;
export declare const PiSmileyWinkBold: IconType;
export declare const PiSmileyXEyesBold: IconType;
export declare const PiSnapchatLogoBold: IconType;
export declare const PiSneakerBold: IconType;
export declare const PiSneakerMoveBold: IconType;
export declare const PiSnowflakeBold: IconType;
export declare const PiSoccerBallBold: IconType;
export declare const PiSockBold: IconType;
export declare const PiSolarPanelBold: IconType;
export declare const PiSolarRoofBold: IconType;
export declare const PiSortAscendingBold: IconType;
export declare const PiSortDescendingBold: IconType;
export declare const PiSoundcloudLogoBold: IconType;
export declare const PiSpadeBold: IconType;
export declare const PiSparkleBold: IconType;
export declare const PiSpeakerHifiBold: IconType;
export declare const PiSpeakerHighBold: IconType;
export declare const PiSpeakerLowBold: IconType;
export declare const PiSpeakerNoneBold: IconType;
export declare const PiSpeakerSimpleHighBold: IconType;
export declare const PiSpeakerSimpleLowBold: IconType;
export declare const PiSpeakerSimpleNoneBold: IconType;
export declare const PiSpeakerSimpleSlashBold: IconType;
export declare const PiSpeakerSimpleXBold: IconType;
export declare const PiSpeakerSlashBold: IconType;
export declare const PiSpeakerXBold: IconType;
export declare const PiSpeedometerBold: IconType;
export declare const PiSphereBold: IconType;
export declare const PiSpinnerBallBold: IconType;
export declare const PiSpinnerBold: IconType;
export declare const PiSpinnerGapBold: IconType;
export declare const PiSpiralBold: IconType;
export declare const PiSplitHorizontalBold: IconType;
export declare const PiSplitVerticalBold: IconType;
export declare const PiSpotifyLogoBold: IconType;
export declare const PiSprayBottleBold: IconType;
export declare const PiSquareBold: IconType;
export declare const PiSquareHalfBold: IconType;
export declare const PiSquareHalfBottomBold: IconType;
export declare const PiSquareLogoBold: IconType;
export declare const PiSquareSplitHorizontalBold: IconType;
export declare const PiSquareSplitVerticalBold: IconType;
export declare const PiSquaresFourBold: IconType;
export declare const PiStackBold: IconType;
export declare const PiStackMinusBold: IconType;
export declare const PiStackOverflowLogoBold: IconType;
export declare const PiStackPlusBold: IconType;
export declare const PiStackSimpleBold: IconType;
export declare const PiStairsBold: IconType;
export declare const PiStampBold: IconType;
export declare const PiStandardDefinitionBold: IconType;
export declare const PiStarAndCrescentBold: IconType;
export declare const PiStarBold: IconType;
export declare const PiStarFourBold: IconType;
export declare const PiStarHalfBold: IconType;
export declare const PiStarOfDavidBold: IconType;
export declare const PiSteamLogoBold: IconType;
export declare const PiSteeringWheelBold: IconType;
export declare const PiStepsBold: IconType;
export declare const PiStethoscopeBold: IconType;
export declare const PiStickerBold: IconType;
export declare const PiStoolBold: IconType;
export declare const PiStopBold: IconType;
export declare const PiStopCircleBold: IconType;
export declare const PiStorefrontBold: IconType;
export declare const PiStrategyBold: IconType;
export declare const PiStripeLogoBold: IconType;
export declare const PiStudentBold: IconType;
export declare const PiSubsetOfBold: IconType;
export declare const PiSubsetProperOfBold: IconType;
export declare const PiSubtitlesBold: IconType;
export declare const PiSubtitlesSlashBold: IconType;
export declare const PiSubtractBold: IconType;
export declare const PiSubtractSquareBold: IconType;
export declare const PiSubwayBold: IconType;
export declare const PiSuitcaseBold: IconType;
export declare const PiSuitcaseRollingBold: IconType;
export declare const PiSuitcaseSimpleBold: IconType;
export declare const PiSunBold: IconType;
export declare const PiSunDimBold: IconType;
export declare const PiSunHorizonBold: IconType;
export declare const PiSunglassesBold: IconType;
export declare const PiSupersetOfBold: IconType;
export declare const PiSupersetProperOfBold: IconType;
export declare const PiSwapBold: IconType;
export declare const PiSwatchesBold: IconType;
export declare const PiSwimmingPoolBold: IconType;
export declare const PiSwordBold: IconType;
export declare const PiSynagogueBold: IconType;
export declare const PiSyringeBold: IconType;
export declare const PiTShirtBold: IconType;
export declare const PiTableBold: IconType;
export declare const PiTabsBold: IconType;
export declare const PiTagBold: IconType;
export declare const PiTagChevronBold: IconType;
export declare const PiTagSimpleBold: IconType;
export declare const PiTargetBold: IconType;
export declare const PiTaxiBold: IconType;
export declare const PiTeaBagBold: IconType;
export declare const PiTelegramLogoBold: IconType;
export declare const PiTelevisionBold: IconType;
export declare const PiTelevisionSimpleBold: IconType;
export declare const PiTennisBallBold: IconType;
export declare const PiTentBold: IconType;
export declare const PiTerminalBold: IconType;
export declare const PiTerminalWindowBold: IconType;
export declare const PiTestTubeBold: IconType;
export declare const PiTextAUnderlineBold: IconType;
export declare const PiTextAaBold: IconType;
export declare const PiTextAlignCenterBold: IconType;
export declare const PiTextAlignJustifyBold: IconType;
export declare const PiTextAlignLeftBold: IconType;
export declare const PiTextAlignRightBold: IconType;
export declare const PiTextBBold: IconType;
export declare const PiTextColumnsBold: IconType;
export declare const PiTextHBold: IconType;
export declare const PiTextHFiveBold: IconType;
export declare const PiTextHFourBold: IconType;
export declare const PiTextHOneBold: IconType;
export declare const PiTextHSixBold: IconType;
export declare const PiTextHThreeBold: IconType;
export declare const PiTextHTwoBold: IconType;
export declare const PiTextIndentBold: IconType;
export declare const PiTextItalicBold: IconType;
export declare const PiTextOutdentBold: IconType;
export declare const PiTextStrikethroughBold: IconType;
export declare const PiTextSubscriptBold: IconType;
export declare const PiTextSuperscriptBold: IconType;
export declare const PiTextTBold: IconType;
export declare const PiTextTSlashBold: IconType;
export declare const PiTextUnderlineBold: IconType;
export declare const PiTextboxBold: IconType;
export declare const PiThermometerBold: IconType;
export declare const PiThermometerColdBold: IconType;
export declare const PiThermometerHotBold: IconType;
export declare const PiThermometerSimpleBold: IconType;
export declare const PiThreadsLogoBold: IconType;
export declare const PiThreeDBold: IconType;
export declare const PiThumbsDownBold: IconType;
export declare const PiThumbsUpBold: IconType;
export declare const PiTicketBold: IconType;
export declare const PiTidalLogoBold: IconType;
export declare const PiTiktokLogoBold: IconType;
export declare const PiTildeBold: IconType;
export declare const PiTimerBold: IconType;
export declare const PiTipJarBold: IconType;
export declare const PiTipiBold: IconType;
export declare const PiTireBold: IconType;
export declare const PiToggleLeftBold: IconType;
export declare const PiToggleRightBold: IconType;
export declare const PiToiletBold: IconType;
export declare const PiToiletPaperBold: IconType;
export declare const PiToolboxBold: IconType;
export declare const PiToothBold: IconType;
export declare const PiTornadoBold: IconType;
export declare const PiToteBold: IconType;
export declare const PiToteSimpleBold: IconType;
export declare const PiTowelBold: IconType;
export declare const PiTractorBold: IconType;
export declare const PiTrademarkBold: IconType;
export declare const PiTrademarkRegisteredBold: IconType;
export declare const PiTrafficConeBold: IconType;
export declare const PiTrafficSignBold: IconType;
export declare const PiTrafficSignalBold: IconType;
export declare const PiTrainBold: IconType;
export declare const PiTrainRegionalBold: IconType;
export declare const PiTrainSimpleBold: IconType;
export declare const PiTramBold: IconType;
export declare const PiTranslateBold: IconType;
export declare const PiTrashBold: IconType;
export declare const PiTrashSimpleBold: IconType;
export declare const PiTrayArrowDownBold: IconType;
export declare const PiTrayArrowUpBold: IconType;
export declare const PiTrayBold: IconType;
export declare const PiTreasureChestBold: IconType;
export declare const PiTreeBold: IconType;
export declare const PiTreeEvergreenBold: IconType;
export declare const PiTreePalmBold: IconType;
export declare const PiTreeStructureBold: IconType;
export declare const PiTreeViewBold: IconType;
export declare const PiTrendDownBold: IconType;
export declare const PiTrendUpBold: IconType;
export declare const PiTriangleBold: IconType;
export declare const PiTriangleDashedBold: IconType;
export declare const PiTrolleyBold: IconType;
export declare const PiTrolleySuitcaseBold: IconType;
export declare const PiTrophyBold: IconType;
export declare const PiTruckBold: IconType;
export declare const PiTruckTrailerBold: IconType;
export declare const PiTumblrLogoBold: IconType;
export declare const PiTwitchLogoBold: IconType;
export declare const PiTwitterLogoBold: IconType;
export declare const PiUmbrellaBold: IconType;
export declare const PiUmbrellaSimpleBold: IconType;
export declare const PiUnionBold: IconType;
export declare const PiUniteBold: IconType;
export declare const PiUniteSquareBold: IconType;
export declare const PiUploadBold: IconType;
export declare const PiUploadSimpleBold: IconType;
export declare const PiUsbBold: IconType;
export declare const PiUserBold: IconType;
export declare const PiUserCheckBold: IconType;
export declare const PiUserCircleBold: IconType;
export declare const PiUserCircleCheckBold: IconType;
export declare const PiUserCircleDashedBold: IconType;
export declare const PiUserCircleGearBold: IconType;
export declare const PiUserCircleMinusBold: IconType;
export declare const PiUserCirclePlusBold: IconType;
export declare const PiUserFocusBold: IconType;
export declare const PiUserGearBold: IconType;
export declare const PiUserListBold: IconType;
export declare const PiUserMinusBold: IconType;
export declare const PiUserPlusBold: IconType;
export declare const PiUserRectangleBold: IconType;
export declare const PiUserSoundBold: IconType;
export declare const PiUserSquareBold: IconType;
export declare const PiUserSwitchBold: IconType;
export declare const PiUsersBold: IconType;
export declare const PiUsersFourBold: IconType;
export declare const PiUsersThreeBold: IconType;
export declare const PiVanBold: IconType;
export declare const PiVaultBold: IconType;
export declare const PiVectorThreeBold: IconType;
export declare const PiVectorTwoBold: IconType;
export declare const PiVibrateBold: IconType;
export declare const PiVideoBold: IconType;
export declare const PiVideoCameraBold: IconType;
export declare const PiVideoCameraSlashBold: IconType;
export declare const PiVideoConferenceBold: IconType;
export declare const PiVignetteBold: IconType;
export declare const PiVinylRecordBold: IconType;
export declare const PiVirtualRealityBold: IconType;
export declare const PiVirusBold: IconType;
export declare const PiVisorBold: IconType;
export declare const PiVoicemailBold: IconType;
export declare const PiVolleyballBold: IconType;
export declare const PiWallBold: IconType;
export declare const PiWalletBold: IconType;
export declare const PiWarehouseBold: IconType;
export declare const PiWarningBold: IconType;
export declare const PiWarningCircleBold: IconType;
export declare const PiWarningDiamondBold: IconType;
export declare const PiWarningOctagonBold: IconType;
export declare const PiWashingMachineBold: IconType;
export declare const PiWatchBold: IconType;
export declare const PiWaveSawtoothBold: IconType;
export declare const PiWaveSineBold: IconType;
export declare const PiWaveSquareBold: IconType;
export declare const PiWaveTriangleBold: IconType;
export declare const PiWaveformBold: IconType;
export declare const PiWaveformSlashBold: IconType;
export declare const PiWavesBold: IconType;
export declare const PiWebcamBold: IconType;
export declare const PiWebcamSlashBold: IconType;
export declare const PiWebhooksLogoBold: IconType;
export declare const PiWechatLogoBold: IconType;
export declare const PiWhatsappLogoBold: IconType;
export declare const PiWheelchairBold: IconType;
export declare const PiWheelchairMotionBold: IconType;
export declare const PiWifiHighBold: IconType;
export declare const PiWifiLowBold: IconType;
export declare const PiWifiMediumBold: IconType;
export declare const PiWifiNoneBold: IconType;
export declare const PiWifiSlashBold: IconType;
export declare const PiWifiXBold: IconType;
export declare const PiWindBold: IconType;
export declare const PiWindmillBold: IconType;
export declare const PiWindowsLogoBold: IconType;
export declare const PiWineBold: IconType;
export declare const PiWrenchBold: IconType;
export declare const PiXBold: IconType;
export declare const PiXCircleBold: IconType;
export declare const PiXLogoBold: IconType;
export declare const PiXSquareBold: IconType;
export declare const PiYarnBold: IconType;
export declare const PiYinYangBold: IconType;
export declare const PiYoutubeLogoBold: IconType;
export declare const PiAcornDuotone: IconType;
export declare const PiAddressBookDuotone: IconType;
export declare const PiAddressBookTabsDuotone: IconType;
export declare const PiAirTrafficControlDuotone: IconType;
export declare const PiAirplaneDuotone: IconType;
export declare const PiAirplaneInFlightDuotone: IconType;
export declare const PiAirplaneLandingDuotone: IconType;
export declare const PiAirplaneTakeoffDuotone: IconType;
export declare const PiAirplaneTaxiingDuotone: IconType;
export declare const PiAirplaneTiltDuotone: IconType;
export declare const PiAirplayDuotone: IconType;
export declare const PiAlarmDuotone: IconType;
export declare const PiAlienDuotone: IconType;
export declare const PiAlignBottomDuotone: IconType;
export declare const PiAlignBottomSimpleDuotone: IconType;
export declare const PiAlignCenterHorizontalDuotone: IconType;
export declare const PiAlignCenterHorizontalSimpleDuotone: IconType;
export declare const PiAlignCenterVerticalDuotone: IconType;
export declare const PiAlignCenterVerticalSimpleDuotone: IconType;
export declare const PiAlignLeftDuotone: IconType;
export declare const PiAlignLeftSimpleDuotone: IconType;
export declare const PiAlignRightDuotone: IconType;
export declare const PiAlignRightSimpleDuotone: IconType;
export declare const PiAlignTopDuotone: IconType;
export declare const PiAlignTopSimpleDuotone: IconType;
export declare const PiAmazonLogoDuotone: IconType;
export declare const PiAmbulanceDuotone: IconType;
export declare const PiAnchorDuotone: IconType;
export declare const PiAnchorSimpleDuotone: IconType;
export declare const PiAndroidLogoDuotone: IconType;
export declare const PiAngleDuotone: IconType;
export declare const PiAngularLogoDuotone: IconType;
export declare const PiApertureDuotone: IconType;
export declare const PiAppStoreLogoDuotone: IconType;
export declare const PiAppWindowDuotone: IconType;
export declare const PiAppleLogoDuotone: IconType;
export declare const PiApplePodcastsLogoDuotone: IconType;
export declare const PiApproximateEqualsDuotone: IconType;
export declare const PiArchiveDuotone: IconType;
export declare const PiArmchairDuotone: IconType;
export declare const PiArrowArcLeftDuotone: IconType;
export declare const PiArrowArcRightDuotone: IconType;
export declare const PiArrowBendDoubleUpLeftDuotone: IconType;
export declare const PiArrowBendDoubleUpRightDuotone: IconType;
export declare const PiArrowBendDownLeftDuotone: IconType;
export declare const PiArrowBendDownRightDuotone: IconType;
export declare const PiArrowBendLeftDownDuotone: IconType;
export declare const PiArrowBendLeftUpDuotone: IconType;
export declare const PiArrowBendRightDownDuotone: IconType;
export declare const PiArrowBendRightUpDuotone: IconType;
export declare const PiArrowBendUpLeftDuotone: IconType;
export declare const PiArrowBendUpRightDuotone: IconType;
export declare const PiArrowCircleDownDuotone: IconType;
export declare const PiArrowCircleDownLeftDuotone: IconType;
export declare const PiArrowCircleDownRightDuotone: IconType;
export declare const PiArrowCircleLeftDuotone: IconType;
export declare const PiArrowCircleRightDuotone: IconType;
export declare const PiArrowCircleUpDuotone: IconType;
export declare const PiArrowCircleUpLeftDuotone: IconType;
export declare const PiArrowCircleUpRightDuotone: IconType;
export declare const PiArrowClockwiseDuotone: IconType;
export declare const PiArrowCounterClockwiseDuotone: IconType;
export declare const PiArrowDownDuotone: IconType;
export declare const PiArrowDownLeftDuotone: IconType;
export declare const PiArrowDownRightDuotone: IconType;
export declare const PiArrowElbowDownLeftDuotone: IconType;
export declare const PiArrowElbowDownRightDuotone: IconType;
export declare const PiArrowElbowLeftDownDuotone: IconType;
export declare const PiArrowElbowLeftDuotone: IconType;
export declare const PiArrowElbowLeftUpDuotone: IconType;
export declare const PiArrowElbowRightDownDuotone: IconType;
export declare const PiArrowElbowRightDuotone: IconType;
export declare const PiArrowElbowRightUpDuotone: IconType;
export declare const PiArrowElbowUpLeftDuotone: IconType;
export declare const PiArrowElbowUpRightDuotone: IconType;
export declare const PiArrowFatDownDuotone: IconType;
export declare const PiArrowFatLeftDuotone: IconType;
export declare const PiArrowFatLineDownDuotone: IconType;
export declare const PiArrowFatLineLeftDuotone: IconType;
export declare const PiArrowFatLineRightDuotone: IconType;
export declare const PiArrowFatLineUpDuotone: IconType;
export declare const PiArrowFatLinesDownDuotone: IconType;
export declare const PiArrowFatLinesLeftDuotone: IconType;
export declare const PiArrowFatLinesRightDuotone: IconType;
export declare const PiArrowFatLinesUpDuotone: IconType;
export declare const PiArrowFatRightDuotone: IconType;
export declare const PiArrowFatUpDuotone: IconType;
export declare const PiArrowLeftDuotone: IconType;
export declare const PiArrowLineDownDuotone: IconType;
export declare const PiArrowLineDownLeftDuotone: IconType;
export declare const PiArrowLineDownRightDuotone: IconType;
export declare const PiArrowLineLeftDuotone: IconType;
export declare const PiArrowLineRightDuotone: IconType;
export declare const PiArrowLineUpDuotone: IconType;
export declare const PiArrowLineUpLeftDuotone: IconType;
export declare const PiArrowLineUpRightDuotone: IconType;
export declare const PiArrowRightDuotone: IconType;
export declare const PiArrowSquareDownDuotone: IconType;
export declare const PiArrowSquareDownLeftDuotone: IconType;
export declare const PiArrowSquareDownRightDuotone: IconType;
export declare const PiArrowSquareInDuotone: IconType;
export declare const PiArrowSquareLeftDuotone: IconType;
export declare const PiArrowSquareOutDuotone: IconType;
export declare const PiArrowSquareRightDuotone: IconType;
export declare const PiArrowSquareUpDuotone: IconType;
export declare const PiArrowSquareUpLeftDuotone: IconType;
export declare const PiArrowSquareUpRightDuotone: IconType;
export declare const PiArrowUDownLeftDuotone: IconType;
export declare const PiArrowUDownRightDuotone: IconType;
export declare const PiArrowULeftDownDuotone: IconType;
export declare const PiArrowULeftUpDuotone: IconType;
export declare const PiArrowURightDownDuotone: IconType;
export declare const PiArrowURightUpDuotone: IconType;
export declare const PiArrowUUpLeftDuotone: IconType;
export declare const PiArrowUUpRightDuotone: IconType;
export declare const PiArrowUpDuotone: IconType;
export declare const PiArrowUpLeftDuotone: IconType;
export declare const PiArrowUpRightDuotone: IconType;
export declare const PiArrowsClockwiseDuotone: IconType;
export declare const PiArrowsCounterClockwiseDuotone: IconType;
export declare const PiArrowsDownUpDuotone: IconType;
export declare const PiArrowsHorizontalDuotone: IconType;
export declare const PiArrowsInCardinalDuotone: IconType;
export declare const PiArrowsInDuotone: IconType;
export declare const PiArrowsInLineHorizontalDuotone: IconType;
export declare const PiArrowsInLineVerticalDuotone: IconType;
export declare const PiArrowsInSimpleDuotone: IconType;
export declare const PiArrowsLeftRightDuotone: IconType;
export declare const PiArrowsMergeDuotone: IconType;
export declare const PiArrowsOutCardinalDuotone: IconType;
export declare const PiArrowsOutDuotone: IconType;
export declare const PiArrowsOutLineHorizontalDuotone: IconType;
export declare const PiArrowsOutLineVerticalDuotone: IconType;
export declare const PiArrowsOutSimpleDuotone: IconType;
export declare const PiArrowsSplitDuotone: IconType;
export declare const PiArrowsVerticalDuotone: IconType;
export declare const PiArticleDuotone: IconType;
export declare const PiArticleMediumDuotone: IconType;
export declare const PiArticleNyTimesDuotone: IconType;
export declare const PiAsclepiusDuotone: IconType;
export declare const PiAsteriskDuotone: IconType;
export declare const PiAsteriskSimpleDuotone: IconType;
export declare const PiAtDuotone: IconType;
export declare const PiAtomDuotone: IconType;
export declare const PiAvocadoDuotone: IconType;
export declare const PiAxeDuotone: IconType;
export declare const PiBabyCarriageDuotone: IconType;
export declare const PiBabyDuotone: IconType;
export declare const PiBackpackDuotone: IconType;
export declare const PiBackspaceDuotone: IconType;
export declare const PiBagDuotone: IconType;
export declare const PiBagSimpleDuotone: IconType;
export declare const PiBalloonDuotone: IconType;
export declare const PiBandaidsDuotone: IconType;
export declare const PiBankDuotone: IconType;
export declare const PiBarbellDuotone: IconType;
export declare const PiBarcodeDuotone: IconType;
export declare const PiBarnDuotone: IconType;
export declare const PiBarricadeDuotone: IconType;
export declare const PiBaseballCapDuotone: IconType;
export declare const PiBaseballDuotone: IconType;
export declare const PiBaseballHelmetDuotone: IconType;
export declare const PiBasketDuotone: IconType;
export declare const PiBasketballDuotone: IconType;
export declare const PiBathtubDuotone: IconType;
export declare const PiBatteryChargingDuotone: IconType;
export declare const PiBatteryChargingVerticalDuotone: IconType;
export declare const PiBatteryEmptyDuotone: IconType;
export declare const PiBatteryFullDuotone: IconType;
export declare const PiBatteryHighDuotone: IconType;
export declare const PiBatteryLowDuotone: IconType;
export declare const PiBatteryMediumDuotone: IconType;
export declare const PiBatteryPlusDuotone: IconType;
export declare const PiBatteryPlusVerticalDuotone: IconType;
export declare const PiBatteryVerticalEmptyDuotone: IconType;
export declare const PiBatteryVerticalFullDuotone: IconType;
export declare const PiBatteryVerticalHighDuotone: IconType;
export declare const PiBatteryVerticalLowDuotone: IconType;
export declare const PiBatteryVerticalMediumDuotone: IconType;
export declare const PiBatteryWarningDuotone: IconType;
export declare const PiBatteryWarningVerticalDuotone: IconType;
export declare const PiBeachBallDuotone: IconType;
export declare const PiBeanieDuotone: IconType;
export declare const PiBedDuotone: IconType;
export declare const PiBeerBottleDuotone: IconType;
export declare const PiBeerSteinDuotone: IconType;
export declare const PiBehanceLogoDuotone: IconType;
export declare const PiBellDuotone: IconType;
export declare const PiBellRingingDuotone: IconType;
export declare const PiBellSimpleDuotone: IconType;
export declare const PiBellSimpleRingingDuotone: IconType;
export declare const PiBellSimpleSlashDuotone: IconType;
export declare const PiBellSimpleZDuotone: IconType;
export declare const PiBellSlashDuotone: IconType;
export declare const PiBellZDuotone: IconType;
export declare const PiBeltDuotone: IconType;
export declare const PiBezierCurveDuotone: IconType;
export declare const PiBicycleDuotone: IconType;
export declare const PiBinaryDuotone: IconType;
export declare const PiBinocularsDuotone: IconType;
export declare const PiBiohazardDuotone: IconType;
export declare const PiBirdDuotone: IconType;
export declare const PiBlueprintDuotone: IconType;
export declare const PiBluetoothConnectedDuotone: IconType;
export declare const PiBluetoothDuotone: IconType;
export declare const PiBluetoothSlashDuotone: IconType;
export declare const PiBluetoothXDuotone: IconType;
export declare const PiBoatDuotone: IconType;
export declare const PiBombDuotone: IconType;
export declare const PiBoneDuotone: IconType;
export declare const PiBookBookmarkDuotone: IconType;
export declare const PiBookDuotone: IconType;
export declare const PiBookOpenDuotone: IconType;
export declare const PiBookOpenTextDuotone: IconType;
export declare const PiBookOpenUserDuotone: IconType;
export declare const PiBookmarkDuotone: IconType;
export declare const PiBookmarkSimpleDuotone: IconType;
export declare const PiBookmarksDuotone: IconType;
export declare const PiBookmarksSimpleDuotone: IconType;
export declare const PiBooksDuotone: IconType;
export declare const PiBootDuotone: IconType;
export declare const PiBoulesDuotone: IconType;
export declare const PiBoundingBoxDuotone: IconType;
export declare const PiBowlFoodDuotone: IconType;
export declare const PiBowlSteamDuotone: IconType;
export declare const PiBowlingBallDuotone: IconType;
export declare const PiBoxArrowDownDuotone: IconType;
export declare const PiBoxArrowUpDuotone: IconType;
export declare const PiBoxingGloveDuotone: IconType;
export declare const PiBracketsAngleDuotone: IconType;
export declare const PiBracketsCurlyDuotone: IconType;
export declare const PiBracketsRoundDuotone: IconType;
export declare const PiBracketsSquareDuotone: IconType;
export declare const PiBrainDuotone: IconType;
export declare const PiBrandyDuotone: IconType;
export declare const PiBreadDuotone: IconType;
export declare const PiBridgeDuotone: IconType;
export declare const PiBriefcaseDuotone: IconType;
export declare const PiBriefcaseMetalDuotone: IconType;
export declare const PiBroadcastDuotone: IconType;
export declare const PiBroomDuotone: IconType;
export declare const PiBrowserDuotone: IconType;
export declare const PiBrowsersDuotone: IconType;
export declare const PiBugBeetleDuotone: IconType;
export declare const PiBugDroidDuotone: IconType;
export declare const PiBugDuotone: IconType;
export declare const PiBuildingApartmentDuotone: IconType;
export declare const PiBuildingDuotone: IconType;
export declare const PiBuildingOfficeDuotone: IconType;
export declare const PiBuildingsDuotone: IconType;
export declare const PiBulldozerDuotone: IconType;
export declare const PiBusDuotone: IconType;
export declare const PiButterflyDuotone: IconType;
export declare const PiCableCarDuotone: IconType;
export declare const PiCactusDuotone: IconType;
export declare const PiCakeDuotone: IconType;
export declare const PiCalculatorDuotone: IconType;
export declare const PiCalendarBlankDuotone: IconType;
export declare const PiCalendarCheckDuotone: IconType;
export declare const PiCalendarDotDuotone: IconType;
export declare const PiCalendarDotsDuotone: IconType;
export declare const PiCalendarDuotone: IconType;
export declare const PiCalendarHeartDuotone: IconType;
export declare const PiCalendarMinusDuotone: IconType;
export declare const PiCalendarPlusDuotone: IconType;
export declare const PiCalendarSlashDuotone: IconType;
export declare const PiCalendarStarDuotone: IconType;
export declare const PiCalendarXDuotone: IconType;
export declare const PiCallBellDuotone: IconType;
export declare const PiCameraDuotone: IconType;
export declare const PiCameraPlusDuotone: IconType;
export declare const PiCameraRotateDuotone: IconType;
export declare const PiCameraSlashDuotone: IconType;
export declare const PiCampfireDuotone: IconType;
export declare const PiCarBatteryDuotone: IconType;
export declare const PiCarDuotone: IconType;
export declare const PiCarProfileDuotone: IconType;
export declare const PiCarSimpleDuotone: IconType;
export declare const PiCardholderDuotone: IconType;
export declare const PiCardsDuotone: IconType;
export declare const PiCardsThreeDuotone: IconType;
export declare const PiCaretCircleDoubleDownDuotone: IconType;
export declare const PiCaretCircleDoubleLeftDuotone: IconType;
export declare const PiCaretCircleDoubleRightDuotone: IconType;
export declare const PiCaretCircleDoubleUpDuotone: IconType;
export declare const PiCaretCircleDownDuotone: IconType;
export declare const PiCaretCircleLeftDuotone: IconType;
export declare const PiCaretCircleRightDuotone: IconType;
export declare const PiCaretCircleUpDownDuotone: IconType;
export declare const PiCaretCircleUpDuotone: IconType;
export declare const PiCaretDoubleDownDuotone: IconType;
export declare const PiCaretDoubleLeftDuotone: IconType;
export declare const PiCaretDoubleRightDuotone: IconType;
export declare const PiCaretDoubleUpDuotone: IconType;
export declare const PiCaretDownDuotone: IconType;
export declare const PiCaretLeftDuotone: IconType;
export declare const PiCaretLineDownDuotone: IconType;
export declare const PiCaretLineLeftDuotone: IconType;
export declare const PiCaretLineRightDuotone: IconType;
export declare const PiCaretLineUpDuotone: IconType;
export declare const PiCaretRightDuotone: IconType;
export declare const PiCaretUpDownDuotone: IconType;
export declare const PiCaretUpDuotone: IconType;
export declare const PiCarrotDuotone: IconType;
export declare const PiCashRegisterDuotone: IconType;
export declare const PiCassetteTapeDuotone: IconType;
export declare const PiCastleTurretDuotone: IconType;
export declare const PiCatDuotone: IconType;
export declare const PiCellSignalFullDuotone: IconType;
export declare const PiCellSignalHighDuotone: IconType;
export declare const PiCellSignalLowDuotone: IconType;
export declare const PiCellSignalMediumDuotone: IconType;
export declare const PiCellSignalNoneDuotone: IconType;
export declare const PiCellSignalSlashDuotone: IconType;
export declare const PiCellSignalXDuotone: IconType;
export declare const PiCellTowerDuotone: IconType;
export declare const PiCertificateDuotone: IconType;
export declare const PiChairDuotone: IconType;
export declare const PiChalkboardDuotone: IconType;
export declare const PiChalkboardSimpleDuotone: IconType;
export declare const PiChalkboardTeacherDuotone: IconType;
export declare const PiChampagneDuotone: IconType;
export declare const PiChargingStationDuotone: IconType;
export declare const PiChartBarDuotone: IconType;
export declare const PiChartBarHorizontalDuotone: IconType;
export declare const PiChartDonutDuotone: IconType;
export declare const PiChartLineDownDuotone: IconType;
export declare const PiChartLineDuotone: IconType;
export declare const PiChartLineUpDuotone: IconType;
export declare const PiChartPieDuotone: IconType;
export declare const PiChartPieSliceDuotone: IconType;
export declare const PiChartPolarDuotone: IconType;
export declare const PiChartScatterDuotone: IconType;
export declare const PiChatCenteredDotsDuotone: IconType;
export declare const PiChatCenteredDuotone: IconType;
export declare const PiChatCenteredSlashDuotone: IconType;
export declare const PiChatCenteredTextDuotone: IconType;
export declare const PiChatCircleDotsDuotone: IconType;
export declare const PiChatCircleDuotone: IconType;
export declare const PiChatCircleSlashDuotone: IconType;
export declare const PiChatCircleTextDuotone: IconType;
export declare const PiChatDotsDuotone: IconType;
export declare const PiChatDuotone: IconType;
export declare const PiChatSlashDuotone: IconType;
export declare const PiChatTeardropDotsDuotone: IconType;
export declare const PiChatTeardropDuotone: IconType;
export declare const PiChatTeardropSlashDuotone: IconType;
export declare const PiChatTeardropTextDuotone: IconType;
export declare const PiChatTextDuotone: IconType;
export declare const PiChatsCircleDuotone: IconType;
export declare const PiChatsDuotone: IconType;
export declare const PiChatsTeardropDuotone: IconType;
export declare const PiCheckCircleDuotone: IconType;
export declare const PiCheckDuotone: IconType;
export declare const PiCheckFatDuotone: IconType;
export declare const PiCheckSquareDuotone: IconType;
export declare const PiCheckSquareOffsetDuotone: IconType;
export declare const PiCheckerboardDuotone: IconType;
export declare const PiChecksDuotone: IconType;
export declare const PiCheersDuotone: IconType;
export declare const PiCheeseDuotone: IconType;
export declare const PiChefHatDuotone: IconType;
export declare const PiCherriesDuotone: IconType;
export declare const PiChurchDuotone: IconType;
export declare const PiCigaretteDuotone: IconType;
export declare const PiCigaretteSlashDuotone: IconType;
export declare const PiCircleDashedDuotone: IconType;
export declare const PiCircleDuotone: IconType;
export declare const PiCircleHalfDuotone: IconType;
export declare const PiCircleHalfTiltDuotone: IconType;
export declare const PiCircleNotchDuotone: IconType;
export declare const PiCirclesFourDuotone: IconType;
export declare const PiCirclesThreeDuotone: IconType;
export declare const PiCirclesThreePlusDuotone: IconType;
export declare const PiCircuitryDuotone: IconType;
export declare const PiCityDuotone: IconType;
export declare const PiClipboardDuotone: IconType;
export declare const PiClipboardTextDuotone: IconType;
export declare const PiClockAfternoonDuotone: IconType;
export declare const PiClockClockwiseDuotone: IconType;
export declare const PiClockCountdownDuotone: IconType;
export declare const PiClockCounterClockwiseDuotone: IconType;
export declare const PiClockDuotone: IconType;
export declare const PiClockUserDuotone: IconType;
export declare const PiClosedCaptioningDuotone: IconType;
export declare const PiCloudArrowDownDuotone: IconType;
export declare const PiCloudArrowUpDuotone: IconType;
export declare const PiCloudCheckDuotone: IconType;
export declare const PiCloudDuotone: IconType;
export declare const PiCloudFogDuotone: IconType;
export declare const PiCloudLightningDuotone: IconType;
export declare const PiCloudMoonDuotone: IconType;
export declare const PiCloudRainDuotone: IconType;
export declare const PiCloudSlashDuotone: IconType;
export declare const PiCloudSnowDuotone: IconType;
export declare const PiCloudSunDuotone: IconType;
export declare const PiCloudWarningDuotone: IconType;
export declare const PiCloudXDuotone: IconType;
export declare const PiCloverDuotone: IconType;
export declare const PiClubDuotone: IconType;
export declare const PiCoatHangerDuotone: IconType;
export declare const PiCodaLogoDuotone: IconType;
export declare const PiCodeBlockDuotone: IconType;
export declare const PiCodeDuotone: IconType;
export declare const PiCodeSimpleDuotone: IconType;
export declare const PiCodepenLogoDuotone: IconType;
export declare const PiCodesandboxLogoDuotone: IconType;
export declare const PiCoffeeBeanDuotone: IconType;
export declare const PiCoffeeDuotone: IconType;
export declare const PiCoinDuotone: IconType;
export declare const PiCoinVerticalDuotone: IconType;
export declare const PiCoinsDuotone: IconType;
export declare const PiColumnsDuotone: IconType;
export declare const PiColumnsPlusLeftDuotone: IconType;
export declare const PiColumnsPlusRightDuotone: IconType;
export declare const PiCommandDuotone: IconType;
export declare const PiCompassDuotone: IconType;
export declare const PiCompassRoseDuotone: IconType;
export declare const PiCompassToolDuotone: IconType;
export declare const PiComputerTowerDuotone: IconType;
export declare const PiConfettiDuotone: IconType;
export declare const PiContactlessPaymentDuotone: IconType;
export declare const PiControlDuotone: IconType;
export declare const PiCookieDuotone: IconType;
export declare const PiCookingPotDuotone: IconType;
export declare const PiCopyDuotone: IconType;
export declare const PiCopySimpleDuotone: IconType;
export declare const PiCopyleftDuotone: IconType;
export declare const PiCopyrightDuotone: IconType;
export declare const PiCornersInDuotone: IconType;
export declare const PiCornersOutDuotone: IconType;
export declare const PiCouchDuotone: IconType;
export declare const PiCourtBasketballDuotone: IconType;
export declare const PiCowDuotone: IconType;
export declare const PiCowboyHatDuotone: IconType;
export declare const PiCpuDuotone: IconType;
export declare const PiCraneDuotone: IconType;
export declare const PiCraneTowerDuotone: IconType;
export declare const PiCreditCardDuotone: IconType;
export declare const PiCricketDuotone: IconType;
export declare const PiCropDuotone: IconType;
export declare const PiCrossDuotone: IconType;
export declare const PiCrosshairDuotone: IconType;
export declare const PiCrosshairSimpleDuotone: IconType;
export declare const PiCrownCrossDuotone: IconType;
export declare const PiCrownDuotone: IconType;
export declare const PiCrownSimpleDuotone: IconType;
export declare const PiCubeDuotone: IconType;
export declare const PiCubeFocusDuotone: IconType;
export declare const PiCubeTransparentDuotone: IconType;
export declare const PiCurrencyBtcDuotone: IconType;
export declare const PiCurrencyCircleDollarDuotone: IconType;
export declare const PiCurrencyCnyDuotone: IconType;
export declare const PiCurrencyDollarDuotone: IconType;
export declare const PiCurrencyDollarSimpleDuotone: IconType;
export declare const PiCurrencyEthDuotone: IconType;
export declare const PiCurrencyEurDuotone: IconType;
export declare const PiCurrencyGbpDuotone: IconType;
export declare const PiCurrencyInrDuotone: IconType;
export declare const PiCurrencyJpyDuotone: IconType;
export declare const PiCurrencyKrwDuotone: IconType;
export declare const PiCurrencyKztDuotone: IconType;
export declare const PiCurrencyNgnDuotone: IconType;
export declare const PiCurrencyRubDuotone: IconType;
export declare const PiCursorClickDuotone: IconType;
export declare const PiCursorDuotone: IconType;
export declare const PiCursorTextDuotone: IconType;
export declare const PiCylinderDuotone: IconType;
export declare const PiDatabaseDuotone: IconType;
export declare const PiDeskDuotone: IconType;
export declare const PiDesktopDuotone: IconType;
export declare const PiDesktopTowerDuotone: IconType;
export declare const PiDetectiveDuotone: IconType;
export declare const PiDevToLogoDuotone: IconType;
export declare const PiDeviceMobileCameraDuotone: IconType;
export declare const PiDeviceMobileDuotone: IconType;
export declare const PiDeviceMobileSlashDuotone: IconType;
export declare const PiDeviceMobileSpeakerDuotone: IconType;
export declare const PiDeviceRotateDuotone: IconType;
export declare const PiDeviceTabletCameraDuotone: IconType;
export declare const PiDeviceTabletDuotone: IconType;
export declare const PiDeviceTabletSpeakerDuotone: IconType;
export declare const PiDevicesDuotone: IconType;
export declare const PiDiamondDuotone: IconType;
export declare const PiDiamondsFourDuotone: IconType;
export declare const PiDiceFiveDuotone: IconType;
export declare const PiDiceFourDuotone: IconType;
export declare const PiDiceOneDuotone: IconType;
export declare const PiDiceSixDuotone: IconType;
export declare const PiDiceThreeDuotone: IconType;
export declare const PiDiceTwoDuotone: IconType;
export declare const PiDiscDuotone: IconType;
export declare const PiDiscoBallDuotone: IconType;
export declare const PiDiscordLogoDuotone: IconType;
export declare const PiDivideDuotone: IconType;
export declare const PiDnaDuotone: IconType;
export declare const PiDogDuotone: IconType;
export declare const PiDoorDuotone: IconType;
export declare const PiDoorOpenDuotone: IconType;
export declare const PiDotDuotone: IconType;
export declare const PiDotOutlineDuotone: IconType;
export declare const PiDotsNineDuotone: IconType;
export declare const PiDotsSixDuotone: IconType;
export declare const PiDotsSixVerticalDuotone: IconType;
export declare const PiDotsThreeCircleDuotone: IconType;
export declare const PiDotsThreeCircleVerticalDuotone: IconType;
export declare const PiDotsThreeDuotone: IconType;
export declare const PiDotsThreeOutlineDuotone: IconType;
export declare const PiDotsThreeOutlineVerticalDuotone: IconType;
export declare const PiDotsThreeVerticalDuotone: IconType;
export declare const PiDownloadDuotone: IconType;
export declare const PiDownloadSimpleDuotone: IconType;
export declare const PiDressDuotone: IconType;
export declare const PiDresserDuotone: IconType;
export declare const PiDribbbleLogoDuotone: IconType;
export declare const PiDroneDuotone: IconType;
export declare const PiDropDuotone: IconType;
export declare const PiDropHalfBottomDuotone: IconType;
export declare const PiDropHalfDuotone: IconType;
export declare const PiDropSimpleDuotone: IconType;
export declare const PiDropSlashDuotone: IconType;
export declare const PiDropboxLogoDuotone: IconType;
export declare const PiEarDuotone: IconType;
export declare const PiEarSlashDuotone: IconType;
export declare const PiEggCrackDuotone: IconType;
export declare const PiEggDuotone: IconType;
export declare const PiEjectDuotone: IconType;
export declare const PiEjectSimpleDuotone: IconType;
export declare const PiElevatorDuotone: IconType;
export declare const PiEmptyDuotone: IconType;
export declare const PiEngineDuotone: IconType;
export declare const PiEnvelopeDuotone: IconType;
export declare const PiEnvelopeOpenDuotone: IconType;
export declare const PiEnvelopeSimpleDuotone: IconType;
export declare const PiEnvelopeSimpleOpenDuotone: IconType;
export declare const PiEqualizerDuotone: IconType;
export declare const PiEqualsDuotone: IconType;
export declare const PiEraserDuotone: IconType;
export declare const PiEscalatorDownDuotone: IconType;
export declare const PiEscalatorUpDuotone: IconType;
export declare const PiExamDuotone: IconType;
export declare const PiExclamationMarkDuotone: IconType;
export declare const PiExcludeDuotone: IconType;
export declare const PiExcludeSquareDuotone: IconType;
export declare const PiExportDuotone: IconType;
export declare const PiEyeClosedDuotone: IconType;
export declare const PiEyeDuotone: IconType;
export declare const PiEyeSlashDuotone: IconType;
export declare const PiEyedropperDuotone: IconType;
export declare const PiEyedropperSampleDuotone: IconType;
export declare const PiEyeglassesDuotone: IconType;
export declare const PiEyesDuotone: IconType;
export declare const PiFaceMaskDuotone: IconType;
export declare const PiFacebookLogoDuotone: IconType;
export declare const PiFactoryDuotone: IconType;
export declare const PiFadersDuotone: IconType;
export declare const PiFadersHorizontalDuotone: IconType;
export declare const PiFalloutShelterDuotone: IconType;
export declare const PiFanDuotone: IconType;
export declare const PiFarmDuotone: IconType;
export declare const PiFastForwardCircleDuotone: IconType;
export declare const PiFastForwardDuotone: IconType;
export declare const PiFeatherDuotone: IconType;
export declare const PiFediverseLogoDuotone: IconType;
export declare const PiFigmaLogoDuotone: IconType;
export declare const PiFileArchiveDuotone: IconType;
export declare const PiFileArrowDownDuotone: IconType;
export declare const PiFileArrowUpDuotone: IconType;
export declare const PiFileAudioDuotone: IconType;
export declare const PiFileCDuotone: IconType;
export declare const PiFileCSharpDuotone: IconType;
export declare const PiFileCloudDuotone: IconType;
export declare const PiFileCodeDuotone: IconType;
export declare const PiFileCppDuotone: IconType;
export declare const PiFileCssDuotone: IconType;
export declare const PiFileCsvDuotone: IconType;
export declare const PiFileDashedDuotone: IconType;
export declare const PiFileDocDuotone: IconType;
export declare const PiFileDuotone: IconType;
export declare const PiFileHtmlDuotone: IconType;
export declare const PiFileImageDuotone: IconType;
export declare const PiFileIniDuotone: IconType;
export declare const PiFileJpgDuotone: IconType;
export declare const PiFileJsDuotone: IconType;
export declare const PiFileJsxDuotone: IconType;
export declare const PiFileLockDuotone: IconType;
export declare const PiFileMagnifyingGlassDuotone: IconType;
export declare const PiFileMdDuotone: IconType;
export declare const PiFileMinusDuotone: IconType;
export declare const PiFilePdfDuotone: IconType;
export declare const PiFilePlusDuotone: IconType;
export declare const PiFilePngDuotone: IconType;
export declare const PiFilePptDuotone: IconType;
export declare const PiFilePyDuotone: IconType;
export declare const PiFileRsDuotone: IconType;
export declare const PiFileSqlDuotone: IconType;
export declare const PiFileSvgDuotone: IconType;
export declare const PiFileTextDuotone: IconType;
export declare const PiFileTsDuotone: IconType;
export declare const PiFileTsxDuotone: IconType;
export declare const PiFileTxtDuotone: IconType;
export declare const PiFileVideoDuotone: IconType;
export declare const PiFileVueDuotone: IconType;
export declare const PiFileXDuotone: IconType;
export declare const PiFileXlsDuotone: IconType;
export declare const PiFileZipDuotone: IconType;
export declare const PiFilesDuotone: IconType;
export declare const PiFilmReelDuotone: IconType;
export declare const PiFilmScriptDuotone: IconType;
export declare const PiFilmSlateDuotone: IconType;
export declare const PiFilmStripDuotone: IconType;
export declare const PiFingerprintDuotone: IconType;
export declare const PiFingerprintSimpleDuotone: IconType;
export declare const PiFinnTheHumanDuotone: IconType;
export declare const PiFireDuotone: IconType;
export declare const PiFireExtinguisherDuotone: IconType;
export declare const PiFireSimpleDuotone: IconType;
export declare const PiFireTruckDuotone: IconType;
export declare const PiFirstAidDuotone: IconType;
export declare const PiFirstAidKitDuotone: IconType;
export declare const PiFishDuotone: IconType;
export declare const PiFishSimpleDuotone: IconType;
export declare const PiFlagBannerDuotone: IconType;
export declare const PiFlagBannerFoldDuotone: IconType;
export declare const PiFlagCheckeredDuotone: IconType;
export declare const PiFlagDuotone: IconType;
export declare const PiFlagPennantDuotone: IconType;
export declare const PiFlameDuotone: IconType;
export declare const PiFlashlightDuotone: IconType;
export declare const PiFlaskDuotone: IconType;
export declare const PiFlipHorizontalDuotone: IconType;
export declare const PiFlipVerticalDuotone: IconType;
export declare const PiFloppyDiskBackDuotone: IconType;
export declare const PiFloppyDiskDuotone: IconType;
export declare const PiFlowArrowDuotone: IconType;
export declare const PiFlowerDuotone: IconType;
export declare const PiFlowerLotusDuotone: IconType;
export declare const PiFlowerTulipDuotone: IconType;
export declare const PiFlyingSaucerDuotone: IconType;
export declare const PiFolderDashedDuotone: IconType;
export declare const PiFolderDuotone: IconType;
export declare const PiFolderLockDuotone: IconType;
export declare const PiFolderMinusDuotone: IconType;
export declare const PiFolderOpenDuotone: IconType;
export declare const PiFolderPlusDuotone: IconType;
export declare const PiFolderSimpleDashedDuotone: IconType;
export declare const PiFolderSimpleDuotone: IconType;
export declare const PiFolderSimpleLockDuotone: IconType;
export declare const PiFolderSimpleMinusDuotone: IconType;
export declare const PiFolderSimplePlusDuotone: IconType;
export declare const PiFolderSimpleStarDuotone: IconType;
export declare const PiFolderSimpleUserDuotone: IconType;
export declare const PiFolderStarDuotone: IconType;
export declare const PiFolderUserDuotone: IconType;
export declare const PiFoldersDuotone: IconType;
export declare const PiFootballDuotone: IconType;
export declare const PiFootballHelmetDuotone: IconType;
export declare const PiFootprintsDuotone: IconType;
export declare const PiForkKnifeDuotone: IconType;
export declare const PiFourKDuotone: IconType;
export declare const PiFrameCornersDuotone: IconType;
export declare const PiFramerLogoDuotone: IconType;
export declare const PiFunctionDuotone: IconType;
export declare const PiFunnelDuotone: IconType;
export declare const PiFunnelSimpleDuotone: IconType;
export declare const PiFunnelSimpleXDuotone: IconType;
export declare const PiFunnelXDuotone: IconType;
export declare const PiGameControllerDuotone: IconType;
export declare const PiGarageDuotone: IconType;
export declare const PiGasCanDuotone: IconType;
export declare const PiGasPumpDuotone: IconType;
export declare const PiGaugeDuotone: IconType;
export declare const PiGavelDuotone: IconType;
export declare const PiGearDuotone: IconType;
export declare const PiGearFineDuotone: IconType;
export declare const PiGearSixDuotone: IconType;
export declare const PiGenderFemaleDuotone: IconType;
export declare const PiGenderIntersexDuotone: IconType;
export declare const PiGenderMaleDuotone: IconType;
export declare const PiGenderNeuterDuotone: IconType;
export declare const PiGenderNonbinaryDuotone: IconType;
export declare const PiGenderTransgenderDuotone: IconType;
export declare const PiGhostDuotone: IconType;
export declare const PiGifDuotone: IconType;
export declare const PiGiftDuotone: IconType;
export declare const PiGitBranchDuotone: IconType;
export declare const PiGitCommitDuotone: IconType;
export declare const PiGitDiffDuotone: IconType;
export declare const PiGitForkDuotone: IconType;
export declare const PiGitMergeDuotone: IconType;
export declare const PiGitPullRequestDuotone: IconType;
export declare const PiGithubLogoDuotone: IconType;
export declare const PiGitlabLogoDuotone: IconType;
export declare const PiGitlabLogoSimpleDuotone: IconType;
export declare const PiGlobeDuotone: IconType;
export declare const PiGlobeHemisphereEastDuotone: IconType;
export declare const PiGlobeHemisphereWestDuotone: IconType;
export declare const PiGlobeSimpleDuotone: IconType;
export declare const PiGlobeSimpleXDuotone: IconType;
export declare const PiGlobeStandDuotone: IconType;
export declare const PiGlobeXDuotone: IconType;
export declare const PiGogglesDuotone: IconType;
export declare const PiGolfDuotone: IconType;
export declare const PiGoodreadsLogoDuotone: IconType;
export declare const PiGoogleCardboardLogoDuotone: IconType;
export declare const PiGoogleChromeLogoDuotone: IconType;
export declare const PiGoogleDriveLogoDuotone: IconType;
export declare const PiGoogleLogoDuotone: IconType;
export declare const PiGooglePhotosLogoDuotone: IconType;
export declare const PiGooglePlayLogoDuotone: IconType;
export declare const PiGooglePodcastsLogoDuotone: IconType;
export declare const PiGpsDuotone: IconType;
export declare const PiGpsFixDuotone: IconType;
export declare const PiGpsSlashDuotone: IconType;
export declare const PiGradientDuotone: IconType;
export declare const PiGraduationCapDuotone: IconType;
export declare const PiGrainsDuotone: IconType;
export declare const PiGrainsSlashDuotone: IconType;
export declare const PiGraphDuotone: IconType;
export declare const PiGraphicsCardDuotone: IconType;
export declare const PiGreaterThanDuotone: IconType;
export declare const PiGreaterThanOrEqualDuotone: IconType;
export declare const PiGridFourDuotone: IconType;
export declare const PiGridNineDuotone: IconType;
export declare const PiGuitarDuotone: IconType;
export declare const PiHairDryerDuotone: IconType;
export declare const PiHamburgerDuotone: IconType;
export declare const PiHammerDuotone: IconType;
export declare const PiHandArrowDownDuotone: IconType;
export declare const PiHandArrowUpDuotone: IconType;
export declare const PiHandCoinsDuotone: IconType;
export declare const PiHandDepositDuotone: IconType;
export declare const PiHandDuotone: IconType;
export declare const PiHandEyeDuotone: IconType;
export declare const PiHandFistDuotone: IconType;
export declare const PiHandGrabbingDuotone: IconType;
export declare const PiHandHeartDuotone: IconType;
export declare const PiHandPalmDuotone: IconType;
export declare const PiHandPeaceDuotone: IconType;
export declare const PiHandPointingDuotone: IconType;
export declare const PiHandSoapDuotone: IconType;
export declare const PiHandSwipeLeftDuotone: IconType;
export declare const PiHandSwipeRightDuotone: IconType;
export declare const PiHandTapDuotone: IconType;
export declare const PiHandWavingDuotone: IconType;
export declare const PiHandWithdrawDuotone: IconType;
export declare const PiHandbagDuotone: IconType;
export declare const PiHandbagSimpleDuotone: IconType;
export declare const PiHandsClappingDuotone: IconType;
export declare const PiHandsPrayingDuotone: IconType;
export declare const PiHandshakeDuotone: IconType;
export declare const PiHardDriveDuotone: IconType;
export declare const PiHardDrivesDuotone: IconType;
export declare const PiHardHatDuotone: IconType;
export declare const PiHashDuotone: IconType;
export declare const PiHashStraightDuotone: IconType;
export declare const PiHeadCircuitDuotone: IconType;
export declare const PiHeadlightsDuotone: IconType;
export declare const PiHeadphonesDuotone: IconType;
export declare const PiHeadsetDuotone: IconType;
export declare const PiHeartBreakDuotone: IconType;
export declare const PiHeartDuotone: IconType;
export declare const PiHeartHalfDuotone: IconType;
export declare const PiHeartStraightBreakDuotone: IconType;
export declare const PiHeartStraightDuotone: IconType;
export declare const PiHeartbeatDuotone: IconType;
export declare const PiHexagonDuotone: IconType;
export declare const PiHighDefinitionDuotone: IconType;
export declare const PiHighHeelDuotone: IconType;
export declare const PiHighlighterCircleDuotone: IconType;
export declare const PiHighlighterDuotone: IconType;
export declare const PiHockeyDuotone: IconType;
export declare const PiHoodieDuotone: IconType;
export declare const PiHorseDuotone: IconType;
export declare const PiHospitalDuotone: IconType;
export declare const PiHourglassDuotone: IconType;
export declare const PiHourglassHighDuotone: IconType;
export declare const PiHourglassLowDuotone: IconType;
export declare const PiHourglassMediumDuotone: IconType;
export declare const PiHourglassSimpleDuotone: IconType;
export declare const PiHourglassSimpleHighDuotone: IconType;
export declare const PiHourglassSimpleLowDuotone: IconType;
export declare const PiHourglassSimpleMediumDuotone: IconType;
export declare const PiHouseDuotone: IconType;
export declare const PiHouseLineDuotone: IconType;
export declare const PiHouseSimpleDuotone: IconType;
export declare const PiHurricaneDuotone: IconType;
export declare const PiIceCreamDuotone: IconType;
export declare const PiIdentificationBadgeDuotone: IconType;
export declare const PiIdentificationCardDuotone: IconType;
export declare const PiImageBrokenDuotone: IconType;
export declare const PiImageDuotone: IconType;
export declare const PiImageSquareDuotone: IconType;
export declare const PiImagesDuotone: IconType;
export declare const PiImagesSquareDuotone: IconType;
export declare const PiInfinityDuotone: IconType;
export declare const PiInfoDuotone: IconType;
export declare const PiInstagramLogoDuotone: IconType;
export declare const PiIntersectDuotone: IconType;
export declare const PiIntersectSquareDuotone: IconType;
export declare const PiIntersectThreeDuotone: IconType;
export declare const PiIntersectionDuotone: IconType;
export declare const PiInvoiceDuotone: IconType;
export declare const PiIslandDuotone: IconType;
export declare const PiJarDuotone: IconType;
export declare const PiJarLabelDuotone: IconType;
export declare const PiJeepDuotone: IconType;
export declare const PiJoystickDuotone: IconType;
export declare const PiKanbanDuotone: IconType;
export declare const PiKeyDuotone: IconType;
export declare const PiKeyReturnDuotone: IconType;
export declare const PiKeyboardDuotone: IconType;
export declare const PiKeyholeDuotone: IconType;
export declare const PiKnifeDuotone: IconType;
export declare const PiLadderDuotone: IconType;
export declare const PiLadderSimpleDuotone: IconType;
export declare const PiLampDuotone: IconType;
export declare const PiLampPendantDuotone: IconType;
export declare const PiLaptopDuotone: IconType;
export declare const PiLassoDuotone: IconType;
export declare const PiLastfmLogoDuotone: IconType;
export declare const PiLayoutDuotone: IconType;
export declare const PiLeafDuotone: IconType;
export declare const PiLecternDuotone: IconType;
export declare const PiLegoDuotone: IconType;
export declare const PiLegoSmileyDuotone: IconType;
export declare const PiLessThanDuotone: IconType;
export declare const PiLessThanOrEqualDuotone: IconType;
export declare const PiLetterCircleHDuotone: IconType;
export declare const PiLetterCirclePDuotone: IconType;
export declare const PiLetterCircleVDuotone: IconType;
export declare const PiLifebuoyDuotone: IconType;
export declare const PiLightbulbDuotone: IconType;
export declare const PiLightbulbFilamentDuotone: IconType;
export declare const PiLighthouseDuotone: IconType;
export declare const PiLightningADuotone: IconType;
export declare const PiLightningDuotone: IconType;
export declare const PiLightningSlashDuotone: IconType;
export declare const PiLineSegmentDuotone: IconType;
export declare const PiLineSegmentsDuotone: IconType;
export declare const PiLineVerticalDuotone: IconType;
export declare const PiLinkBreakDuotone: IconType;
export declare const PiLinkDuotone: IconType;
export declare const PiLinkSimpleBreakDuotone: IconType;
export declare const PiLinkSimpleDuotone: IconType;
export declare const PiLinkSimpleHorizontalBreakDuotone: IconType;
export declare const PiLinkSimpleHorizontalDuotone: IconType;
export declare const PiLinkedinLogoDuotone: IconType;
export declare const PiLinktreeLogoDuotone: IconType;
export declare const PiLinuxLogoDuotone: IconType;
export declare const PiListBulletsDuotone: IconType;
export declare const PiListChecksDuotone: IconType;
export declare const PiListDashesDuotone: IconType;
export declare const PiListDuotone: IconType;
export declare const PiListHeartDuotone: IconType;
export declare const PiListMagnifyingGlassDuotone: IconType;
export declare const PiListNumbersDuotone: IconType;
export declare const PiListPlusDuotone: IconType;
export declare const PiListStarDuotone: IconType;
export declare const PiLockDuotone: IconType;
export declare const PiLockKeyDuotone: IconType;
export declare const PiLockKeyOpenDuotone: IconType;
export declare const PiLockLaminatedDuotone: IconType;
export declare const PiLockLaminatedOpenDuotone: IconType;
export declare const PiLockOpenDuotone: IconType;
export declare const PiLockSimpleDuotone: IconType;
export declare const PiLockSimpleOpenDuotone: IconType;
export declare const PiLockersDuotone: IconType;
export declare const PiLogDuotone: IconType;
export declare const PiMagicWandDuotone: IconType;
export declare const PiMagnetDuotone: IconType;
export declare const PiMagnetStraightDuotone: IconType;
export declare const PiMagnifyingGlassDuotone: IconType;
export declare const PiMagnifyingGlassMinusDuotone: IconType;
export declare const PiMagnifyingGlassPlusDuotone: IconType;
export declare const PiMailboxDuotone: IconType;
export declare const PiMapPinAreaDuotone: IconType;
export declare const PiMapPinDuotone: IconType;
export declare const PiMapPinLineDuotone: IconType;
export declare const PiMapPinPlusDuotone: IconType;
export declare const PiMapPinSimpleAreaDuotone: IconType;
export declare const PiMapPinSimpleDuotone: IconType;
export declare const PiMapPinSimpleLineDuotone: IconType;
export declare const PiMapTrifoldDuotone: IconType;
export declare const PiMarkdownLogoDuotone: IconType;
export declare const PiMarkerCircleDuotone: IconType;
export declare const PiMartiniDuotone: IconType;
export declare const PiMaskHappyDuotone: IconType;
export declare const PiMaskSadDuotone: IconType;
export declare const PiMastodonLogoDuotone: IconType;
export declare const PiMathOperationsDuotone: IconType;
export declare const PiMatrixLogoDuotone: IconType;
export declare const PiMedalDuotone: IconType;
export declare const PiMedalMilitaryDuotone: IconType;
export declare const PiMediumLogoDuotone: IconType;
export declare const PiMegaphoneDuotone: IconType;
export declare const PiMegaphoneSimpleDuotone: IconType;
export declare const PiMemberOfDuotone: IconType;
export declare const PiMemoryDuotone: IconType;
export declare const PiMessengerLogoDuotone: IconType;
export declare const PiMetaLogoDuotone: IconType;
export declare const PiMeteorDuotone: IconType;
export declare const PiMetronomeDuotone: IconType;
export declare const PiMicrophoneDuotone: IconType;
export declare const PiMicrophoneSlashDuotone: IconType;
export declare const PiMicrophoneStageDuotone: IconType;
export declare const PiMicroscopeDuotone: IconType;
export declare const PiMicrosoftExcelLogoDuotone: IconType;
export declare const PiMicrosoftOutlookLogoDuotone: IconType;
export declare const PiMicrosoftPowerpointLogoDuotone: IconType;
export declare const PiMicrosoftTeamsLogoDuotone: IconType;
export declare const PiMicrosoftWordLogoDuotone: IconType;
export declare const PiMinusCircleDuotone: IconType;
export declare const PiMinusDuotone: IconType;
export declare const PiMinusSquareDuotone: IconType;
export declare const PiMoneyDuotone: IconType;
export declare const PiMoneyWavyDuotone: IconType;
export declare const PiMonitorArrowUpDuotone: IconType;
export declare const PiMonitorDuotone: IconType;
export declare const PiMonitorPlayDuotone: IconType;
export declare const PiMoonDuotone: IconType;
export declare const PiMoonStarsDuotone: IconType;
export declare const PiMopedDuotone: IconType;
export declare const PiMopedFrontDuotone: IconType;
export declare const PiMosqueDuotone: IconType;
export declare const PiMotorcycleDuotone: IconType;
export declare const PiMountainsDuotone: IconType;
export declare const PiMouseDuotone: IconType;
export declare const PiMouseLeftClickDuotone: IconType;
export declare const PiMouseMiddleClickDuotone: IconType;
export declare const PiMouseRightClickDuotone: IconType;
export declare const PiMouseScrollDuotone: IconType;
export declare const PiMouseSimpleDuotone: IconType;
export declare const PiMusicNoteDuotone: IconType;
export declare const PiMusicNoteSimpleDuotone: IconType;
export declare const PiMusicNotesDuotone: IconType;
export declare const PiMusicNotesMinusDuotone: IconType;
export declare const PiMusicNotesPlusDuotone: IconType;
export declare const PiMusicNotesSimpleDuotone: IconType;
export declare const PiNavigationArrowDuotone: IconType;
export declare const PiNeedleDuotone: IconType;
export declare const PiNetworkDuotone: IconType;
export declare const PiNetworkSlashDuotone: IconType;
export declare const PiNetworkXDuotone: IconType;
export declare const PiNewspaperClippingDuotone: IconType;
export declare const PiNewspaperDuotone: IconType;
export declare const PiNotEqualsDuotone: IconType;
export declare const PiNotMemberOfDuotone: IconType;
export declare const PiNotSubsetOfDuotone: IconType;
export declare const PiNotSupersetOfDuotone: IconType;
export declare const PiNotchesDuotone: IconType;
export declare const PiNoteBlankDuotone: IconType;
export declare const PiNoteDuotone: IconType;
export declare const PiNotePencilDuotone: IconType;
export declare const PiNotebookDuotone: IconType;
export declare const PiNotepadDuotone: IconType;
export declare const PiNotificationDuotone: IconType;
export declare const PiNotionLogoDuotone: IconType;
export declare const PiNuclearPlantDuotone: IconType;
export declare const PiNumberCircleEightDuotone: IconType;
export declare const PiNumberCircleFiveDuotone: IconType;
export declare const PiNumberCircleFourDuotone: IconType;
export declare const PiNumberCircleNineDuotone: IconType;
export declare const PiNumberCircleOneDuotone: IconType;
export declare const PiNumberCircleSevenDuotone: IconType;
export declare const PiNumberCircleSixDuotone: IconType;
export declare const PiNumberCircleThreeDuotone: IconType;
export declare const PiNumberCircleTwoDuotone: IconType;
export declare const PiNumberCircleZeroDuotone: IconType;
export declare const PiNumberEightDuotone: IconType;
export declare const PiNumberFiveDuotone: IconType;
export declare const PiNumberFourDuotone: IconType;
export declare const PiNumberNineDuotone: IconType;
export declare const PiNumberOneDuotone: IconType;
export declare const PiNumberSevenDuotone: IconType;
export declare const PiNumberSixDuotone: IconType;
export declare const PiNumberSquareEightDuotone: IconType;
export declare const PiNumberSquareFiveDuotone: IconType;
export declare const PiNumberSquareFourDuotone: IconType;
export declare const PiNumberSquareNineDuotone: IconType;
export declare const PiNumberSquareOneDuotone: IconType;
export declare const PiNumberSquareSevenDuotone: IconType;
export declare const PiNumberSquareSixDuotone: IconType;
export declare const PiNumberSquareThreeDuotone: IconType;
export declare const PiNumberSquareTwoDuotone: IconType;
export declare const PiNumberSquareZeroDuotone: IconType;
export declare const PiNumberThreeDuotone: IconType;
export declare const PiNumberTwoDuotone: IconType;
export declare const PiNumberZeroDuotone: IconType;
export declare const PiNumpadDuotone: IconType;
export declare const PiNutDuotone: IconType;
export declare const PiNyTimesLogoDuotone: IconType;
export declare const PiOctagonDuotone: IconType;
export declare const PiOfficeChairDuotone: IconType;
export declare const PiOnigiriDuotone: IconType;
export declare const PiOpenAiLogoDuotone: IconType;
export declare const PiOptionDuotone: IconType;
export declare const PiOrangeDuotone: IconType;
export declare const PiOrangeSliceDuotone: IconType;
export declare const PiOvenDuotone: IconType;
export declare const PiPackageDuotone: IconType;
export declare const PiPaintBrushBroadDuotone: IconType;
export declare const PiPaintBrushDuotone: IconType;
export declare const PiPaintBrushHouseholdDuotone: IconType;
export declare const PiPaintBucketDuotone: IconType;
export declare const PiPaintRollerDuotone: IconType;
export declare const PiPaletteDuotone: IconType;
export declare const PiPanoramaDuotone: IconType;
export declare const PiPantsDuotone: IconType;
export declare const PiPaperPlaneDuotone: IconType;
export declare const PiPaperPlaneRightDuotone: IconType;
export declare const PiPaperPlaneTiltDuotone: IconType;
export declare const PiPaperclipDuotone: IconType;
export declare const PiPaperclipHorizontalDuotone: IconType;
export declare const PiParachuteDuotone: IconType;
export declare const PiParagraphDuotone: IconType;
export declare const PiParallelogramDuotone: IconType;
export declare const PiParkDuotone: IconType;
export declare const PiPasswordDuotone: IconType;
export declare const PiPathDuotone: IconType;
export declare const PiPatreonLogoDuotone: IconType;
export declare const PiPauseCircleDuotone: IconType;
export declare const PiPauseDuotone: IconType;
export declare const PiPawPrintDuotone: IconType;
export declare const PiPaypalLogoDuotone: IconType;
export declare const PiPeaceDuotone: IconType;
export declare const PiPenDuotone: IconType;
export declare const PiPenNibDuotone: IconType;
export declare const PiPenNibStraightDuotone: IconType;
export declare const PiPencilCircleDuotone: IconType;
export declare const PiPencilDuotone: IconType;
export declare const PiPencilLineDuotone: IconType;
export declare const PiPencilRulerDuotone: IconType;
export declare const PiPencilSimpleDuotone: IconType;
export declare const PiPencilSimpleLineDuotone: IconType;
export declare const PiPencilSimpleSlashDuotone: IconType;
export declare const PiPencilSlashDuotone: IconType;
export declare const PiPentagonDuotone: IconType;
export declare const PiPentagramDuotone: IconType;
export declare const PiPepperDuotone: IconType;
export declare const PiPercentDuotone: IconType;
export declare const PiPersonArmsSpreadDuotone: IconType;
export declare const PiPersonDuotone: IconType;
export declare const PiPersonSimpleBikeDuotone: IconType;
export declare const PiPersonSimpleCircleDuotone: IconType;
export declare const PiPersonSimpleDuotone: IconType;
export declare const PiPersonSimpleHikeDuotone: IconType;
export declare const PiPersonSimpleRunDuotone: IconType;
export declare const PiPersonSimpleSkiDuotone: IconType;
export declare const PiPersonSimpleSnowboardDuotone: IconType;
export declare const PiPersonSimpleSwimDuotone: IconType;
export declare const PiPersonSimpleTaiChiDuotone: IconType;
export declare const PiPersonSimpleThrowDuotone: IconType;
export declare const PiPersonSimpleWalkDuotone: IconType;
export declare const PiPerspectiveDuotone: IconType;
export declare const PiPhoneCallDuotone: IconType;
export declare const PiPhoneDisconnectDuotone: IconType;
export declare const PiPhoneDuotone: IconType;
export declare const PiPhoneIncomingDuotone: IconType;
export declare const PiPhoneListDuotone: IconType;
export declare const PiPhoneOutgoingDuotone: IconType;
export declare const PiPhonePauseDuotone: IconType;
export declare const PiPhonePlusDuotone: IconType;
export declare const PiPhoneSlashDuotone: IconType;
export declare const PiPhoneTransferDuotone: IconType;
export declare const PiPhoneXDuotone: IconType;
export declare const PiPhosphorLogoDuotone: IconType;
export declare const PiPiDuotone: IconType;
export declare const PiPianoKeysDuotone: IconType;
export declare const PiPicnicTableDuotone: IconType;
export declare const PiPictureInPictureDuotone: IconType;
export declare const PiPiggyBankDuotone: IconType;
export declare const PiPillDuotone: IconType;
export declare const PiPingPongDuotone: IconType;
export declare const PiPintGlassDuotone: IconType;
export declare const PiPinterestLogoDuotone: IconType;
export declare const PiPinwheelDuotone: IconType;
export declare const PiPipeDuotone: IconType;
export declare const PiPipeWrenchDuotone: IconType;
export declare const PiPixLogoDuotone: IconType;
export declare const PiPizzaDuotone: IconType;
export declare const PiPlaceholderDuotone: IconType;
export declare const PiPlanetDuotone: IconType;
export declare const PiPlantDuotone: IconType;
export declare const PiPlayCircleDuotone: IconType;
export declare const PiPlayDuotone: IconType;
export declare const PiPlayPauseDuotone: IconType;
export declare const PiPlaylistDuotone: IconType;
export declare const PiPlugChargingDuotone: IconType;
export declare const PiPlugDuotone: IconType;
export declare const PiPlugsConnectedDuotone: IconType;
export declare const PiPlugsDuotone: IconType;
export declare const PiPlusCircleDuotone: IconType;
export declare const PiPlusDuotone: IconType;
export declare const PiPlusMinusDuotone: IconType;
export declare const PiPlusSquareDuotone: IconType;
export declare const PiPokerChipDuotone: IconType;
export declare const PiPoliceCarDuotone: IconType;
export declare const PiPolygonDuotone: IconType;
export declare const PiPopcornDuotone: IconType;
export declare const PiPopsicleDuotone: IconType;
export declare const PiPottedPlantDuotone: IconType;
export declare const PiPowerDuotone: IconType;
export declare const PiPrescriptionDuotone: IconType;
export declare const PiPresentationChartDuotone: IconType;
export declare const PiPresentationDuotone: IconType;
export declare const PiPrinterDuotone: IconType;
export declare const PiProhibitDuotone: IconType;
export declare const PiProhibitInsetDuotone: IconType;
export declare const PiProjectorScreenChartDuotone: IconType;
export declare const PiProjectorScreenDuotone: IconType;
export declare const PiPulseDuotone: IconType;
export declare const PiPushPinDuotone: IconType;
export declare const PiPushPinSimpleDuotone: IconType;
export declare const PiPushPinSimpleSlashDuotone: IconType;
export declare const PiPushPinSlashDuotone: IconType;
export declare const PiPuzzlePieceDuotone: IconType;
export declare const PiQrCodeDuotone: IconType;
export declare const PiQuestionDuotone: IconType;
export declare const PiQuestionMarkDuotone: IconType;
export declare const PiQueueDuotone: IconType;
export declare const PiQuotesDuotone: IconType;
export declare const PiRabbitDuotone: IconType;
export declare const PiRacquetDuotone: IconType;
export declare const PiRadicalDuotone: IconType;
export declare const PiRadioButtonDuotone: IconType;
export declare const PiRadioDuotone: IconType;
export declare const PiRadioactiveDuotone: IconType;
export declare const PiRainbowCloudDuotone: IconType;
export declare const PiRainbowDuotone: IconType;
export declare const PiRankingDuotone: IconType;
export declare const PiReadCvLogoDuotone: IconType;
export declare const PiReceiptDuotone: IconType;
export declare const PiReceiptXDuotone: IconType;
export declare const PiRecordDuotone: IconType;
export declare const PiRectangleDashedDuotone: IconType;
export declare const PiRectangleDuotone: IconType;
export declare const PiRecycleDuotone: IconType;
export declare const PiRedditLogoDuotone: IconType;
export declare const PiRepeatDuotone: IconType;
export declare const PiRepeatOnceDuotone: IconType;
export declare const PiReplitLogoDuotone: IconType;
export declare const PiResizeDuotone: IconType;
export declare const PiRewindCircleDuotone: IconType;
export declare const PiRewindDuotone: IconType;
export declare const PiRoadHorizonDuotone: IconType;
export declare const PiRobotDuotone: IconType;
export declare const PiRocketDuotone: IconType;
export declare const PiRocketLaunchDuotone: IconType;
export declare const PiRowsDuotone: IconType;
export declare const PiRowsPlusBottomDuotone: IconType;
export declare const PiRowsPlusTopDuotone: IconType;
export declare const PiRssDuotone: IconType;
export declare const PiRssSimpleDuotone: IconType;
export declare const PiRugDuotone: IconType;
export declare const PiRulerDuotone: IconType;
export declare const PiSailboatDuotone: IconType;
export declare const PiScalesDuotone: IconType;
export declare const PiScanDuotone: IconType;
export declare const PiScanSmileyDuotone: IconType;
export declare const PiScissorsDuotone: IconType;
export declare const PiScooterDuotone: IconType;
export declare const PiScreencastDuotone: IconType;
export declare const PiScrewdriverDuotone: IconType;
export declare const PiScribbleDuotone: IconType;
export declare const PiScribbleLoopDuotone: IconType;
export declare const PiScrollDuotone: IconType;
export declare const PiSealCheckDuotone: IconType;
export declare const PiSealDuotone: IconType;
export declare const PiSealPercentDuotone: IconType;
export declare const PiSealQuestionDuotone: IconType;
export declare const PiSealWarningDuotone: IconType;
export declare const PiSeatDuotone: IconType;
export declare const PiSeatbeltDuotone: IconType;
export declare const PiSecurityCameraDuotone: IconType;
export declare const PiSelectionAllDuotone: IconType;
export declare const PiSelectionBackgroundDuotone: IconType;
export declare const PiSelectionDuotone: IconType;
export declare const PiSelectionForegroundDuotone: IconType;
export declare const PiSelectionInverseDuotone: IconType;
export declare const PiSelectionPlusDuotone: IconType;
export declare const PiSelectionSlashDuotone: IconType;
export declare const PiShapesDuotone: IconType;
export declare const PiShareDuotone: IconType;
export declare const PiShareFatDuotone: IconType;
export declare const PiShareNetworkDuotone: IconType;
export declare const PiShieldCheckDuotone: IconType;
export declare const PiShieldCheckeredDuotone: IconType;
export declare const PiShieldChevronDuotone: IconType;
export declare const PiShieldDuotone: IconType;
export declare const PiShieldPlusDuotone: IconType;
export declare const PiShieldSlashDuotone: IconType;
export declare const PiShieldStarDuotone: IconType;
export declare const PiShieldWarningDuotone: IconType;
export declare const PiShippingContainerDuotone: IconType;
export declare const PiShirtFoldedDuotone: IconType;
export declare const PiShootingStarDuotone: IconType;
export declare const PiShoppingBagDuotone: IconType;
export declare const PiShoppingBagOpenDuotone: IconType;
export declare const PiShoppingCartDuotone: IconType;
export declare const PiShoppingCartSimpleDuotone: IconType;
export declare const PiShovelDuotone: IconType;
export declare const PiShowerDuotone: IconType;
export declare const PiShrimpDuotone: IconType;
export declare const PiShuffleAngularDuotone: IconType;
export declare const PiShuffleDuotone: IconType;
export declare const PiShuffleSimpleDuotone: IconType;
export declare const PiSidebarDuotone: IconType;
export declare const PiSidebarSimpleDuotone: IconType;
export declare const PiSigmaDuotone: IconType;
export declare const PiSignInDuotone: IconType;
export declare const PiSignOutDuotone: IconType;
export declare const PiSignatureDuotone: IconType;
export declare const PiSignpostDuotone: IconType;
export declare const PiSimCardDuotone: IconType;
export declare const PiSirenDuotone: IconType;
export declare const PiSketchLogoDuotone: IconType;
export declare const PiSkipBackCircleDuotone: IconType;
export declare const PiSkipBackDuotone: IconType;
export declare const PiSkipForwardCircleDuotone: IconType;
export declare const PiSkipForwardDuotone: IconType;
export declare const PiSkullDuotone: IconType;
export declare const PiSkypeLogoDuotone: IconType;
export declare const PiSlackLogoDuotone: IconType;
export declare const PiSlidersDuotone: IconType;
export declare const PiSlidersHorizontalDuotone: IconType;
export declare const PiSlideshowDuotone: IconType;
export declare const PiSmileyAngryDuotone: IconType;
export declare const PiSmileyBlankDuotone: IconType;
export declare const PiSmileyDuotone: IconType;
export declare const PiSmileyMehDuotone: IconType;
export declare const PiSmileyMeltingDuotone: IconType;
export declare const PiSmileyNervousDuotone: IconType;
export declare const PiSmileySadDuotone: IconType;
export declare const PiSmileyStickerDuotone: IconType;
export declare const PiSmileyWinkDuotone: IconType;
export declare const PiSmileyXEyesDuotone: IconType;
export declare const PiSnapchatLogoDuotone: IconType;
export declare const PiSneakerDuotone: IconType;
export declare const PiSneakerMoveDuotone: IconType;
export declare const PiSnowflakeDuotone: IconType;
export declare const PiSoccerBallDuotone: IconType;
export declare const PiSockDuotone: IconType;
export declare const PiSolarPanelDuotone: IconType;
export declare const PiSolarRoofDuotone: IconType;
export declare const PiSortAscendingDuotone: IconType;
export declare const PiSortDescendingDuotone: IconType;
export declare const PiSoundcloudLogoDuotone: IconType;
export declare const PiSpadeDuotone: IconType;
export declare const PiSparkleDuotone: IconType;
export declare const PiSpeakerHifiDuotone: IconType;
export declare const PiSpeakerHighDuotone: IconType;
export declare const PiSpeakerLowDuotone: IconType;
export declare const PiSpeakerNoneDuotone: IconType;
export declare const PiSpeakerSimpleHighDuotone: IconType;
export declare const PiSpeakerSimpleLowDuotone: IconType;
export declare const PiSpeakerSimpleNoneDuotone: IconType;
export declare const PiSpeakerSimpleSlashDuotone: IconType;
export declare const PiSpeakerSimpleXDuotone: IconType;
export declare const PiSpeakerSlashDuotone: IconType;
export declare const PiSpeakerXDuotone: IconType;
export declare const PiSpeedometerDuotone: IconType;
export declare const PiSphereDuotone: IconType;
export declare const PiSpinnerBallDuotone: IconType;
export declare const PiSpinnerDuotone: IconType;
export declare const PiSpinnerGapDuotone: IconType;
export declare const PiSpiralDuotone: IconType;
export declare const PiSplitHorizontalDuotone: IconType;
export declare const PiSplitVerticalDuotone: IconType;
export declare const PiSpotifyLogoDuotone: IconType;
export declare const PiSprayBottleDuotone: IconType;
export declare const PiSquareDuotone: IconType;
export declare const PiSquareHalfBottomDuotone: IconType;
export declare const PiSquareHalfDuotone: IconType;
export declare const PiSquareLogoDuotone: IconType;
export declare const PiSquareSplitHorizontalDuotone: IconType;
export declare const PiSquareSplitVerticalDuotone: IconType;
export declare const PiSquaresFourDuotone: IconType;
export declare const PiStackDuotone: IconType;
export declare const PiStackMinusDuotone: IconType;
export declare const PiStackOverflowLogoDuotone: IconType;
export declare const PiStackPlusDuotone: IconType;
export declare const PiStackSimpleDuotone: IconType;
export declare const PiStairsDuotone: IconType;
export declare const PiStampDuotone: IconType;
export declare const PiStandardDefinitionDuotone: IconType;
export declare const PiStarAndCrescentDuotone: IconType;
export declare const PiStarDuotone: IconType;
export declare const PiStarFourDuotone: IconType;
export declare const PiStarHalfDuotone: IconType;
export declare const PiStarOfDavidDuotone: IconType;
export declare const PiSteamLogoDuotone: IconType;
export declare const PiSteeringWheelDuotone: IconType;
export declare const PiStepsDuotone: IconType;
export declare const PiStethoscopeDuotone: IconType;
export declare const PiStickerDuotone: IconType;
export declare const PiStoolDuotone: IconType;
export declare const PiStopCircleDuotone: IconType;
export declare const PiStopDuotone: IconType;
export declare const PiStorefrontDuotone: IconType;
export declare const PiStrategyDuotone: IconType;
export declare const PiStripeLogoDuotone: IconType;
export declare const PiStudentDuotone: IconType;
export declare const PiSubsetOfDuotone: IconType;
export declare const PiSubsetProperOfDuotone: IconType;
export declare const PiSubtitlesDuotone: IconType;
export declare const PiSubtitlesSlashDuotone: IconType;
export declare const PiSubtractDuotone: IconType;
export declare const PiSubtractSquareDuotone: IconType;
export declare const PiSubwayDuotone: IconType;
export declare const PiSuitcaseDuotone: IconType;
export declare const PiSuitcaseRollingDuotone: IconType;
export declare const PiSuitcaseSimpleDuotone: IconType;
export declare const PiSunDimDuotone: IconType;
export declare const PiSunDuotone: IconType;
export declare const PiSunHorizonDuotone: IconType;
export declare const PiSunglassesDuotone: IconType;
export declare const PiSupersetOfDuotone: IconType;
export declare const PiSupersetProperOfDuotone: IconType;
export declare const PiSwapDuotone: IconType;
export declare const PiSwatchesDuotone: IconType;
export declare const PiSwimmingPoolDuotone: IconType;
export declare const PiSwordDuotone: IconType;
export declare const PiSynagogueDuotone: IconType;
export declare const PiSyringeDuotone: IconType;
export declare const PiTShirtDuotone: IconType;
export declare const PiTableDuotone: IconType;
export declare const PiTabsDuotone: IconType;
export declare const PiTagChevronDuotone: IconType;
export declare const PiTagDuotone: IconType;
export declare const PiTagSimpleDuotone: IconType;
export declare const PiTargetDuotone: IconType;
export declare const PiTaxiDuotone: IconType;
export declare const PiTeaBagDuotone: IconType;
export declare const PiTelegramLogoDuotone: IconType;
export declare const PiTelevisionDuotone: IconType;
export declare const PiTelevisionSimpleDuotone: IconType;
export declare const PiTennisBallDuotone: IconType;
export declare const PiTentDuotone: IconType;
export declare const PiTerminalDuotone: IconType;
export declare const PiTerminalWindowDuotone: IconType;
export declare const PiTestTubeDuotone: IconType;
export declare const PiTextAUnderlineDuotone: IconType;
export declare const PiTextAaDuotone: IconType;
export declare const PiTextAlignCenterDuotone: IconType;
export declare const PiTextAlignJustifyDuotone: IconType;
export declare const PiTextAlignLeftDuotone: IconType;
export declare const PiTextAlignRightDuotone: IconType;
export declare const PiTextBDuotone: IconType;
export declare const PiTextColumnsDuotone: IconType;
export declare const PiTextHDuotone: IconType;
export declare const PiTextHFiveDuotone: IconType;
export declare const PiTextHFourDuotone: IconType;
export declare const PiTextHOneDuotone: IconType;
export declare const PiTextHSixDuotone: IconType;
export declare const PiTextHThreeDuotone: IconType;
export declare const PiTextHTwoDuotone: IconType;
export declare const PiTextIndentDuotone: IconType;
export declare const PiTextItalicDuotone: IconType;
export declare const PiTextOutdentDuotone: IconType;
export declare const PiTextStrikethroughDuotone: IconType;
export declare const PiTextSubscriptDuotone: IconType;
export declare const PiTextSuperscriptDuotone: IconType;
export declare const PiTextTDuotone: IconType;
export declare const PiTextTSlashDuotone: IconType;
export declare const PiTextUnderlineDuotone: IconType;
export declare const PiTextboxDuotone: IconType;
export declare const PiThermometerColdDuotone: IconType;
export declare const PiThermometerDuotone: IconType;
export declare const PiThermometerHotDuotone: IconType;
export declare const PiThermometerSimpleDuotone: IconType;
export declare const PiThreadsLogoDuotone: IconType;
export declare const PiThreeDDuotone: IconType;
export declare const PiThumbsDownDuotone: IconType;
export declare const PiThumbsUpDuotone: IconType;
export declare const PiTicketDuotone: IconType;
export declare const PiTidalLogoDuotone: IconType;
export declare const PiTiktokLogoDuotone: IconType;
export declare const PiTildeDuotone: IconType;
export declare const PiTimerDuotone: IconType;
export declare const PiTipJarDuotone: IconType;
export declare const PiTipiDuotone: IconType;
export declare const PiTireDuotone: IconType;
export declare const PiToggleLeftDuotone: IconType;
export declare const PiToggleRightDuotone: IconType;
export declare const PiToiletDuotone: IconType;
export declare const PiToiletPaperDuotone: IconType;
export declare const PiToolboxDuotone: IconType;
export declare const PiToothDuotone: IconType;
export declare const PiTornadoDuotone: IconType;
export declare const PiToteDuotone: IconType;
export declare const PiToteSimpleDuotone: IconType;
export declare const PiTowelDuotone: IconType;
export declare const PiTractorDuotone: IconType;
export declare const PiTrademarkDuotone: IconType;
export declare const PiTrademarkRegisteredDuotone: IconType;
export declare const PiTrafficConeDuotone: IconType;
export declare const PiTrafficSignDuotone: IconType;
export declare const PiTrafficSignalDuotone: IconType;
export declare const PiTrainDuotone: IconType;
export declare const PiTrainRegionalDuotone: IconType;
export declare const PiTrainSimpleDuotone: IconType;
export declare const PiTramDuotone: IconType;
export declare const PiTranslateDuotone: IconType;
export declare const PiTrashDuotone: IconType;
export declare const PiTrashSimpleDuotone: IconType;
export declare const PiTrayArrowDownDuotone: IconType;
export declare const PiTrayArrowUpDuotone: IconType;
export declare const PiTrayDuotone: IconType;
export declare const PiTreasureChestDuotone: IconType;
export declare const PiTreeDuotone: IconType;
export declare const PiTreeEvergreenDuotone: IconType;
export declare const PiTreePalmDuotone: IconType;
export declare const PiTreeStructureDuotone: IconType;
export declare const PiTreeViewDuotone: IconType;
export declare const PiTrendDownDuotone: IconType;
export declare const PiTrendUpDuotone: IconType;
export declare const PiTriangleDashedDuotone: IconType;
export declare const PiTriangleDuotone: IconType;
export declare const PiTrolleyDuotone: IconType;
export declare const PiTrolleySuitcaseDuotone: IconType;
export declare const PiTrophyDuotone: IconType;
export declare const PiTruckDuotone: IconType;
export declare const PiTruckTrailerDuotone: IconType;
export declare const PiTumblrLogoDuotone: IconType;
export declare const PiTwitchLogoDuotone: IconType;
export declare const PiTwitterLogoDuotone: IconType;
export declare const PiUmbrellaDuotone: IconType;
export declare const PiUmbrellaSimpleDuotone: IconType;
export declare const PiUnionDuotone: IconType;
export declare const PiUniteDuotone: IconType;
export declare const PiUniteSquareDuotone: IconType;
export declare const PiUploadDuotone: IconType;
export declare const PiUploadSimpleDuotone: IconType;
export declare const PiUsbDuotone: IconType;
export declare const PiUserCheckDuotone: IconType;
export declare const PiUserCircleCheckDuotone: IconType;
export declare const PiUserCircleDashedDuotone: IconType;
export declare const PiUserCircleDuotone: IconType;
export declare const PiUserCircleGearDuotone: IconType;
export declare const PiUserCircleMinusDuotone: IconType;
export declare const PiUserCirclePlusDuotone: IconType;
export declare const PiUserDuotone: IconType;
export declare const PiUserFocusDuotone: IconType;
export declare const PiUserGearDuotone: IconType;
export declare const PiUserListDuotone: IconType;
export declare const PiUserMinusDuotone: IconType;
export declare const PiUserPlusDuotone: IconType;
export declare const PiUserRectangleDuotone: IconType;
export declare const PiUserSoundDuotone: IconType;
export declare const PiUserSquareDuotone: IconType;
export declare const PiUserSwitchDuotone: IconType;
export declare const PiUsersDuotone: IconType;
export declare const PiUsersFourDuotone: IconType;
export declare const PiUsersThreeDuotone: IconType;
export declare const PiVanDuotone: IconType;
export declare const PiVaultDuotone: IconType;
export declare const PiVectorThreeDuotone: IconType;
export declare const PiVectorTwoDuotone: IconType;
export declare const PiVibrateDuotone: IconType;
export declare const PiVideoCameraDuotone: IconType;
export declare const PiVideoCameraSlashDuotone: IconType;
export declare const PiVideoConferenceDuotone: IconType;
export declare const PiVideoDuotone: IconType;
export declare const PiVignetteDuotone: IconType;
export declare const PiVinylRecordDuotone: IconType;
export declare const PiVirtualRealityDuotone: IconType;
export declare const PiVirusDuotone: IconType;
export declare const PiVisorDuotone: IconType;
export declare const PiVoicemailDuotone: IconType;
export declare const PiVolleyballDuotone: IconType;
export declare const PiWallDuotone: IconType;
export declare const PiWalletDuotone: IconType;
export declare const PiWarehouseDuotone: IconType;
export declare const PiWarningCircleDuotone: IconType;
export declare const PiWarningDiamondDuotone: IconType;
export declare const PiWarningDuotone: IconType;
export declare const PiWarningOctagonDuotone: IconType;
export declare const PiWashingMachineDuotone: IconType;
export declare const PiWatchDuotone: IconType;
export declare const PiWaveSawtoothDuotone: IconType;
export declare const PiWaveSineDuotone: IconType;
export declare const PiWaveSquareDuotone: IconType;
export declare const PiWaveTriangleDuotone: IconType;
export declare const PiWaveformDuotone: IconType;
export declare const PiWaveformSlashDuotone: IconType;
export declare const PiWavesDuotone: IconType;
export declare const PiWebcamDuotone: IconType;
export declare const PiWebcamSlashDuotone: IconType;
export declare const PiWebhooksLogoDuotone: IconType;
export declare const PiWechatLogoDuotone: IconType;
export declare const PiWhatsappLogoDuotone: IconType;
export declare const PiWheelchairDuotone: IconType;
export declare const PiWheelchairMotionDuotone: IconType;
export declare const PiWifiHighDuotone: IconType;
export declare const PiWifiLowDuotone: IconType;
export declare const PiWifiMediumDuotone: IconType;
export declare const PiWifiNoneDuotone: IconType;
export declare const PiWifiSlashDuotone: IconType;
export declare const PiWifiXDuotone: IconType;
export declare const PiWindDuotone: IconType;
export declare const PiWindmillDuotone: IconType;
export declare const PiWindowsLogoDuotone: IconType;
export declare const PiWineDuotone: IconType;
export declare const PiWrenchDuotone: IconType;
export declare const PiXCircleDuotone: IconType;
export declare const PiXDuotone: IconType;
export declare const PiXLogoDuotone: IconType;
export declare const PiXSquareDuotone: IconType;
export declare const PiYarnDuotone: IconType;
export declare const PiYinYangDuotone: IconType;
export declare const PiYoutubeLogoDuotone: IconType;
export declare const PiAcornFill: IconType;
export declare const PiAddressBookFill: IconType;
export declare const PiAddressBookTabsFill: IconType;
export declare const PiAirTrafficControlFill: IconType;
export declare const PiAirplaneFill: IconType;
export declare const PiAirplaneInFlightFill: IconType;
export declare const PiAirplaneLandingFill: IconType;
export declare const PiAirplaneTakeoffFill: IconType;
export declare const PiAirplaneTaxiingFill: IconType;
export declare const PiAirplaneTiltFill: IconType;
export declare const PiAirplayFill: IconType;
export declare const PiAlarmFill: IconType;
export declare const PiAlienFill: IconType;
export declare const PiAlignBottomFill: IconType;
export declare const PiAlignBottomSimpleFill: IconType;
export declare const PiAlignCenterHorizontalFill: IconType;
export declare const PiAlignCenterHorizontalSimpleFill: IconType;
export declare const PiAlignCenterVerticalFill: IconType;
export declare const PiAlignCenterVerticalSimpleFill: IconType;
export declare const PiAlignLeftFill: IconType;
export declare const PiAlignLeftSimpleFill: IconType;
export declare const PiAlignRightFill: IconType;
export declare const PiAlignRightSimpleFill: IconType;
export declare const PiAlignTopFill: IconType;
export declare const PiAlignTopSimpleFill: IconType;
export declare const PiAmazonLogoFill: IconType;
export declare const PiAmbulanceFill: IconType;
export declare const PiAnchorFill: IconType;
export declare const PiAnchorSimpleFill: IconType;
export declare const PiAndroidLogoFill: IconType;
export declare const PiAngleFill: IconType;
export declare const PiAngularLogoFill: IconType;
export declare const PiApertureFill: IconType;
export declare const PiAppStoreLogoFill: IconType;
export declare const PiAppWindowFill: IconType;
export declare const PiAppleLogoFill: IconType;
export declare const PiApplePodcastsLogoFill: IconType;
export declare const PiApproximateEqualsFill: IconType;
export declare const PiArchiveFill: IconType;
export declare const PiArmchairFill: IconType;
export declare const PiArrowArcLeftFill: IconType;
export declare const PiArrowArcRightFill: IconType;
export declare const PiArrowBendDoubleUpLeftFill: IconType;
export declare const PiArrowBendDoubleUpRightFill: IconType;
export declare const PiArrowBendDownLeftFill: IconType;
export declare const PiArrowBendDownRightFill: IconType;
export declare const PiArrowBendLeftDownFill: IconType;
export declare const PiArrowBendLeftUpFill: IconType;
export declare const PiArrowBendRightDownFill: IconType;
export declare const PiArrowBendRightUpFill: IconType;
export declare const PiArrowBendUpLeftFill: IconType;
export declare const PiArrowBendUpRightFill: IconType;
export declare const PiArrowCircleDownFill: IconType;
export declare const PiArrowCircleDownLeftFill: IconType;
export declare const PiArrowCircleDownRightFill: IconType;
export declare const PiArrowCircleLeftFill: IconType;
export declare const PiArrowCircleRightFill: IconType;
export declare const PiArrowCircleUpFill: IconType;
export declare const PiArrowCircleUpLeftFill: IconType;
export declare const PiArrowCircleUpRightFill: IconType;
export declare const PiArrowClockwiseFill: IconType;
export declare const PiArrowCounterClockwiseFill: IconType;
export declare const PiArrowDownFill: IconType;
export declare const PiArrowDownLeftFill: IconType;
export declare const PiArrowDownRightFill: IconType;
export declare const PiArrowElbowDownLeftFill: IconType;
export declare const PiArrowElbowDownRightFill: IconType;
export declare const PiArrowElbowLeftDownFill: IconType;
export declare const PiArrowElbowLeftFill: IconType;
export declare const PiArrowElbowLeftUpFill: IconType;
export declare const PiArrowElbowRightDownFill: IconType;
export declare const PiArrowElbowRightFill: IconType;
export declare const PiArrowElbowRightUpFill: IconType;
export declare const PiArrowElbowUpLeftFill: IconType;
export declare const PiArrowElbowUpRightFill: IconType;
export declare const PiArrowFatDownFill: IconType;
export declare const PiArrowFatLeftFill: IconType;
export declare const PiArrowFatLineDownFill: IconType;
export declare const PiArrowFatLineLeftFill: IconType;
export declare const PiArrowFatLineRightFill: IconType;
export declare const PiArrowFatLineUpFill: IconType;
export declare const PiArrowFatLinesDownFill: IconType;
export declare const PiArrowFatLinesLeftFill: IconType;
export declare const PiArrowFatLinesRightFill: IconType;
export declare const PiArrowFatLinesUpFill: IconType;
export declare const PiArrowFatRightFill: IconType;
export declare const PiArrowFatUpFill: IconType;
export declare const PiArrowLeftFill: IconType;
export declare const PiArrowLineDownFill: IconType;
export declare const PiArrowLineDownLeftFill: IconType;
export declare const PiArrowLineDownRightFill: IconType;
export declare const PiArrowLineLeftFill: IconType;
export declare const PiArrowLineRightFill: IconType;
export declare const PiArrowLineUpFill: IconType;
export declare const PiArrowLineUpLeftFill: IconType;
export declare const PiArrowLineUpRightFill: IconType;
export declare const PiArrowRightFill: IconType;
export declare const PiArrowSquareDownFill: IconType;
export declare const PiArrowSquareDownLeftFill: IconType;
export declare const PiArrowSquareDownRightFill: IconType;
export declare const PiArrowSquareInFill: IconType;
export declare const PiArrowSquareLeftFill: IconType;
export declare const PiArrowSquareOutFill: IconType;
export declare const PiArrowSquareRightFill: IconType;
export declare const PiArrowSquareUpFill: IconType;
export declare const PiArrowSquareUpLeftFill: IconType;
export declare const PiArrowSquareUpRightFill: IconType;
export declare const PiArrowUDownLeftFill: IconType;
export declare const PiArrowUDownRightFill: IconType;
export declare const PiArrowULeftDownFill: IconType;
export declare const PiArrowULeftUpFill: IconType;
export declare const PiArrowURightDownFill: IconType;
export declare const PiArrowURightUpFill: IconType;
export declare const PiArrowUUpLeftFill: IconType;
export declare const PiArrowUUpRightFill: IconType;
export declare const PiArrowUpFill: IconType;
export declare const PiArrowUpLeftFill: IconType;
export declare const PiArrowUpRightFill: IconType;
export declare const PiArrowsClockwiseFill: IconType;
export declare const PiArrowsCounterClockwiseFill: IconType;
export declare const PiArrowsDownUpFill: IconType;
export declare const PiArrowsHorizontalFill: IconType;
export declare const PiArrowsInCardinalFill: IconType;
export declare const PiArrowsInFill: IconType;
export declare const PiArrowsInLineHorizontalFill: IconType;
export declare const PiArrowsInLineVerticalFill: IconType;
export declare const PiArrowsInSimpleFill: IconType;
export declare const PiArrowsLeftRightFill: IconType;
export declare const PiArrowsMergeFill: IconType;
export declare const PiArrowsOutCardinalFill: IconType;
export declare const PiArrowsOutFill: IconType;
export declare const PiArrowsOutLineHorizontalFill: IconType;
export declare const PiArrowsOutLineVerticalFill: IconType;
export declare const PiArrowsOutSimpleFill: IconType;
export declare const PiArrowsSplitFill: IconType;
export declare const PiArrowsVerticalFill: IconType;
export declare const PiArticleFill: IconType;
export declare const PiArticleMediumFill: IconType;
export declare const PiArticleNyTimesFill: IconType;
export declare const PiAsclepiusFill: IconType;
export declare const PiAsteriskFill: IconType;
export declare const PiAsteriskSimpleFill: IconType;
export declare const PiAtFill: IconType;
export declare const PiAtomFill: IconType;
export declare const PiAvocadoFill: IconType;
export declare const PiAxeFill: IconType;
export declare const PiBabyCarriageFill: IconType;
export declare const PiBabyFill: IconType;
export declare const PiBackpackFill: IconType;
export declare const PiBackspaceFill: IconType;
export declare const PiBagFill: IconType;
export declare const PiBagSimpleFill: IconType;
export declare const PiBalloonFill: IconType;
export declare const PiBandaidsFill: IconType;
export declare const PiBankFill: IconType;
export declare const PiBarbellFill: IconType;
export declare const PiBarcodeFill: IconType;
export declare const PiBarnFill: IconType;
export declare const PiBarricadeFill: IconType;
export declare const PiBaseballCapFill: IconType;
export declare const PiBaseballFill: IconType;
export declare const PiBaseballHelmetFill: IconType;
export declare const PiBasketFill: IconType;
export declare const PiBasketballFill: IconType;
export declare const PiBathtubFill: IconType;
export declare const PiBatteryChargingFill: IconType;
export declare const PiBatteryChargingVerticalFill: IconType;
export declare const PiBatteryEmptyFill: IconType;
export declare const PiBatteryFullFill: IconType;
export declare const PiBatteryHighFill: IconType;
export declare const PiBatteryLowFill: IconType;
export declare const PiBatteryMediumFill: IconType;
export declare const PiBatteryPlusFill: IconType;
export declare const PiBatteryPlusVerticalFill: IconType;
export declare const PiBatteryVerticalEmptyFill: IconType;
export declare const PiBatteryVerticalFullFill: IconType;
export declare const PiBatteryVerticalHighFill: IconType;
export declare const PiBatteryVerticalLowFill: IconType;
export declare const PiBatteryVerticalMediumFill: IconType;
export declare const PiBatteryWarningFill: IconType;
export declare const PiBatteryWarningVerticalFill: IconType;
export declare const PiBeachBallFill: IconType;
export declare const PiBeanieFill: IconType;
export declare const PiBedFill: IconType;
export declare const PiBeerBottleFill: IconType;
export declare const PiBeerSteinFill: IconType;
export declare const PiBehanceLogoFill: IconType;
export declare const PiBellFill: IconType;
export declare const PiBellRingingFill: IconType;
export declare const PiBellSimpleFill: IconType;
export declare const PiBellSimpleRingingFill: IconType;
export declare const PiBellSimpleSlashFill: IconType;
export declare const PiBellSimpleZFill: IconType;
export declare const PiBellSlashFill: IconType;
export declare const PiBellZFill: IconType;
export declare const PiBeltFill: IconType;
export declare const PiBezierCurveFill: IconType;
export declare const PiBicycleFill: IconType;
export declare const PiBinaryFill: IconType;
export declare const PiBinocularsFill: IconType;
export declare const PiBiohazardFill: IconType;
export declare const PiBirdFill: IconType;
export declare const PiBlueprintFill: IconType;
export declare const PiBluetoothConnectedFill: IconType;
export declare const PiBluetoothFill: IconType;
export declare const PiBluetoothSlashFill: IconType;
export declare const PiBluetoothXFill: IconType;
export declare const PiBoatFill: IconType;
export declare const PiBombFill: IconType;
export declare const PiBoneFill: IconType;
export declare const PiBookBookmarkFill: IconType;
export declare const PiBookFill: IconType;
export declare const PiBookOpenFill: IconType;
export declare const PiBookOpenTextFill: IconType;
export declare const PiBookOpenUserFill: IconType;
export declare const PiBookmarkFill: IconType;
export declare const PiBookmarkSimpleFill: IconType;
export declare const PiBookmarksFill: IconType;
export declare const PiBookmarksSimpleFill: IconType;
export declare const PiBooksFill: IconType;
export declare const PiBootFill: IconType;
export declare const PiBoulesFill: IconType;
export declare const PiBoundingBoxFill: IconType;
export declare const PiBowlFoodFill: IconType;
export declare const PiBowlSteamFill: IconType;
export declare const PiBowlingBallFill: IconType;
export declare const PiBoxArrowDownFill: IconType;
export declare const PiBoxArrowUpFill: IconType;
export declare const PiBoxingGloveFill: IconType;
export declare const PiBracketsAngleFill: IconType;
export declare const PiBracketsCurlyFill: IconType;
export declare const PiBracketsRoundFill: IconType;
export declare const PiBracketsSquareFill: IconType;
export declare const PiBrainFill: IconType;
export declare const PiBrandyFill: IconType;
export declare const PiBreadFill: IconType;
export declare const PiBridgeFill: IconType;
export declare const PiBriefcaseFill: IconType;
export declare const PiBriefcaseMetalFill: IconType;
export declare const PiBroadcastFill: IconType;
export declare const PiBroomFill: IconType;
export declare const PiBrowserFill: IconType;
export declare const PiBrowsersFill: IconType;
export declare const PiBugBeetleFill: IconType;
export declare const PiBugDroidFill: IconType;
export declare const PiBugFill: IconType;
export declare const PiBuildingApartmentFill: IconType;
export declare const PiBuildingFill: IconType;
export declare const PiBuildingOfficeFill: IconType;
export declare const PiBuildingsFill: IconType;
export declare const PiBulldozerFill: IconType;
export declare const PiBusFill: IconType;
export declare const PiButterflyFill: IconType;
export declare const PiCableCarFill: IconType;
export declare const PiCactusFill: IconType;
export declare const PiCakeFill: IconType;
export declare const PiCalculatorFill: IconType;
export declare const PiCalendarBlankFill: IconType;
export declare const PiCalendarCheckFill: IconType;
export declare const PiCalendarDotFill: IconType;
export declare const PiCalendarDotsFill: IconType;
export declare const PiCalendarFill: IconType;
export declare const PiCalendarHeartFill: IconType;
export declare const PiCalendarMinusFill: IconType;
export declare const PiCalendarPlusFill: IconType;
export declare const PiCalendarSlashFill: IconType;
export declare const PiCalendarStarFill: IconType;
export declare const PiCalendarXFill: IconType;
export declare const PiCallBellFill: IconType;
export declare const PiCameraFill: IconType;
export declare const PiCameraPlusFill: IconType;
export declare const PiCameraRotateFill: IconType;
export declare const PiCameraSlashFill: IconType;
export declare const PiCampfireFill: IconType;
export declare const PiCarBatteryFill: IconType;
export declare const PiCarFill: IconType;
export declare const PiCarProfileFill: IconType;
export declare const PiCarSimpleFill: IconType;
export declare const PiCardholderFill: IconType;
export declare const PiCardsFill: IconType;
export declare const PiCardsThreeFill: IconType;
export declare const PiCaretCircleDoubleDownFill: IconType;
export declare const PiCaretCircleDoubleLeftFill: IconType;
export declare const PiCaretCircleDoubleRightFill: IconType;
export declare const PiCaretCircleDoubleUpFill: IconType;
export declare const PiCaretCircleDownFill: IconType;
export declare const PiCaretCircleLeftFill: IconType;
export declare const PiCaretCircleRightFill: IconType;
export declare const PiCaretCircleUpDownFill: IconType;
export declare const PiCaretCircleUpFill: IconType;
export declare const PiCaretDoubleDownFill: IconType;
export declare const PiCaretDoubleLeftFill: IconType;
export declare const PiCaretDoubleRightFill: IconType;
export declare const PiCaretDoubleUpFill: IconType;
export declare const PiCaretDownFill: IconType;
export declare const PiCaretLeftFill: IconType;
export declare const PiCaretLineDownFill: IconType;
export declare const PiCaretLineLeftFill: IconType;
export declare const PiCaretLineRightFill: IconType;
export declare const PiCaretLineUpFill: IconType;
export declare const PiCaretRightFill: IconType;
export declare const PiCaretUpDownFill: IconType;
export declare const PiCaretUpFill: IconType;
export declare const PiCarrotFill: IconType;
export declare const PiCashRegisterFill: IconType;
export declare const PiCassetteTapeFill: IconType;
export declare const PiCastleTurretFill: IconType;
export declare const PiCatFill: IconType;
export declare const PiCellSignalFullFill: IconType;
export declare const PiCellSignalHighFill: IconType;
export declare const PiCellSignalLowFill: IconType;
export declare const PiCellSignalMediumFill: IconType;
export declare const PiCellSignalNoneFill: IconType;
export declare const PiCellSignalSlashFill: IconType;
export declare const PiCellSignalXFill: IconType;
export declare const PiCellTowerFill: IconType;
export declare const PiCertificateFill: IconType;
export declare const PiChairFill: IconType;
export declare const PiChalkboardFill: IconType;
export declare const PiChalkboardSimpleFill: IconType;
export declare const PiChalkboardTeacherFill: IconType;
export declare const PiChampagneFill: IconType;
export declare const PiChargingStationFill: IconType;
export declare const PiChartBarFill: IconType;
export declare const PiChartBarHorizontalFill: IconType;
export declare const PiChartDonutFill: IconType;
export declare const PiChartLineDownFill: IconType;
export declare const PiChartLineFill: IconType;
export declare const PiChartLineUpFill: IconType;
export declare const PiChartPieFill: IconType;
export declare const PiChartPieSliceFill: IconType;
export declare const PiChartPolarFill: IconType;
export declare const PiChartScatterFill: IconType;
export declare const PiChatCenteredDotsFill: IconType;
export declare const PiChatCenteredFill: IconType;
export declare const PiChatCenteredSlashFill: IconType;
export declare const PiChatCenteredTextFill: IconType;
export declare const PiChatCircleDotsFill: IconType;
export declare const PiChatCircleFill: IconType;
export declare const PiChatCircleSlashFill: IconType;
export declare const PiChatCircleTextFill: IconType;
export declare const PiChatDotsFill: IconType;
export declare const PiChatFill: IconType;
export declare const PiChatSlashFill: IconType;
export declare const PiChatTeardropDotsFill: IconType;
export declare const PiChatTeardropFill: IconType;
export declare const PiChatTeardropSlashFill: IconType;
export declare const PiChatTeardropTextFill: IconType;
export declare const PiChatTextFill: IconType;
export declare const PiChatsCircleFill: IconType;
export declare const PiChatsFill: IconType;
export declare const PiChatsTeardropFill: IconType;
export declare const PiCheckCircleFill: IconType;
export declare const PiCheckFatFill: IconType;
export declare const PiCheckFill: IconType;
export declare const PiCheckSquareFill: IconType;
export declare const PiCheckSquareOffsetFill: IconType;
export declare const PiCheckerboardFill: IconType;
export declare const PiChecksFill: IconType;
export declare const PiCheersFill: IconType;
export declare const PiCheeseFill: IconType;
export declare const PiChefHatFill: IconType;
export declare const PiCherriesFill: IconType;
export declare const PiChurchFill: IconType;
export declare const PiCigaretteFill: IconType;
export declare const PiCigaretteSlashFill: IconType;
export declare const PiCircleDashedFill: IconType;
export declare const PiCircleFill: IconType;
export declare const PiCircleHalfFill: IconType;
export declare const PiCircleHalfTiltFill: IconType;
export declare const PiCircleNotchFill: IconType;
export declare const PiCirclesFourFill: IconType;
export declare const PiCirclesThreeFill: IconType;
export declare const PiCirclesThreePlusFill: IconType;
export declare const PiCircuitryFill: IconType;
export declare const PiCityFill: IconType;
export declare const PiClipboardFill: IconType;
export declare const PiClipboardTextFill: IconType;
export declare const PiClockAfternoonFill: IconType;
export declare const PiClockClockwiseFill: IconType;
export declare const PiClockCountdownFill: IconType;
export declare const PiClockCounterClockwiseFill: IconType;
export declare const PiClockFill: IconType;
export declare const PiClockUserFill: IconType;
export declare const PiClosedCaptioningFill: IconType;
export declare const PiCloudArrowDownFill: IconType;
export declare const PiCloudArrowUpFill: IconType;
export declare const PiCloudCheckFill: IconType;
export declare const PiCloudFill: IconType;
export declare const PiCloudFogFill: IconType;
export declare const PiCloudLightningFill: IconType;
export declare const PiCloudMoonFill: IconType;
export declare const PiCloudRainFill: IconType;
export declare const PiCloudSlashFill: IconType;
export declare const PiCloudSnowFill: IconType;
export declare const PiCloudSunFill: IconType;
export declare const PiCloudWarningFill: IconType;
export declare const PiCloudXFill: IconType;
export declare const PiCloverFill: IconType;
export declare const PiClubFill: IconType;
export declare const PiCoatHangerFill: IconType;
export declare const PiCodaLogoFill: IconType;
export declare const PiCodeBlockFill: IconType;
export declare const PiCodeFill: IconType;
export declare const PiCodeSimpleFill: IconType;
export declare const PiCodepenLogoFill: IconType;
export declare const PiCodesandboxLogoFill: IconType;
export declare const PiCoffeeBeanFill: IconType;
export declare const PiCoffeeFill: IconType;
export declare const PiCoinFill: IconType;
export declare const PiCoinVerticalFill: IconType;
export declare const PiCoinsFill: IconType;
export declare const PiColumnsFill: IconType;
export declare const PiColumnsPlusLeftFill: IconType;
export declare const PiColumnsPlusRightFill: IconType;
export declare const PiCommandFill: IconType;
export declare const PiCompassFill: IconType;
export declare const PiCompassRoseFill: IconType;
export declare const PiCompassToolFill: IconType;
export declare const PiComputerTowerFill: IconType;
export declare const PiConfettiFill: IconType;
export declare const PiContactlessPaymentFill: IconType;
export declare const PiControlFill: IconType;
export declare const PiCookieFill: IconType;
export declare const PiCookingPotFill: IconType;
export declare const PiCopyFill: IconType;
export declare const PiCopySimpleFill: IconType;
export declare const PiCopyleftFill: IconType;
export declare const PiCopyrightFill: IconType;
export declare const PiCornersInFill: IconType;
export declare const PiCornersOutFill: IconType;
export declare const PiCouchFill: IconType;
export declare const PiCourtBasketballFill: IconType;
export declare const PiCowFill: IconType;
export declare const PiCowboyHatFill: IconType;
export declare const PiCpuFill: IconType;
export declare const PiCraneFill: IconType;
export declare const PiCraneTowerFill: IconType;
export declare const PiCreditCardFill: IconType;
export declare const PiCricketFill: IconType;
export declare const PiCropFill: IconType;
export declare const PiCrossFill: IconType;
export declare const PiCrosshairFill: IconType;
export declare const PiCrosshairSimpleFill: IconType;
export declare const PiCrownCrossFill: IconType;
export declare const PiCrownFill: IconType;
export declare const PiCrownSimpleFill: IconType;
export declare const PiCubeFill: IconType;
export declare const PiCubeFocusFill: IconType;
export declare const PiCubeTransparentFill: IconType;
export declare const PiCurrencyBtcFill: IconType;
export declare const PiCurrencyCircleDollarFill: IconType;
export declare const PiCurrencyCnyFill: IconType;
export declare const PiCurrencyDollarFill: IconType;
export declare const PiCurrencyDollarSimpleFill: IconType;
export declare const PiCurrencyEthFill: IconType;
export declare const PiCurrencyEurFill: IconType;
export declare const PiCurrencyGbpFill: IconType;
export declare const PiCurrencyInrFill: IconType;
export declare const PiCurrencyJpyFill: IconType;
export declare const PiCurrencyKrwFill: IconType;
export declare const PiCurrencyKztFill: IconType;
export declare const PiCurrencyNgnFill: IconType;
export declare const PiCurrencyRubFill: IconType;
export declare const PiCursorClickFill: IconType;
export declare const PiCursorFill: IconType;
export declare const PiCursorTextFill: IconType;
export declare const PiCylinderFill: IconType;
export declare const PiDatabaseFill: IconType;
export declare const PiDeskFill: IconType;
export declare const PiDesktopFill: IconType;
export declare const PiDesktopTowerFill: IconType;
export declare const PiDetectiveFill: IconType;
export declare const PiDevToLogoFill: IconType;
export declare const PiDeviceMobileCameraFill: IconType;
export declare const PiDeviceMobileFill: IconType;
export declare const PiDeviceMobileSlashFill: IconType;
export declare const PiDeviceMobileSpeakerFill: IconType;
export declare const PiDeviceRotateFill: IconType;
export declare const PiDeviceTabletCameraFill: IconType;
export declare const PiDeviceTabletFill: IconType;
export declare const PiDeviceTabletSpeakerFill: IconType;
export declare const PiDevicesFill: IconType;
export declare const PiDiamondFill: IconType;
export declare const PiDiamondsFourFill: IconType;
export declare const PiDiceFiveFill: IconType;
export declare const PiDiceFourFill: IconType;
export declare const PiDiceOneFill: IconType;
export declare const PiDiceSixFill: IconType;
export declare const PiDiceThreeFill: IconType;
export declare const PiDiceTwoFill: IconType;
export declare const PiDiscFill: IconType;
export declare const PiDiscoBallFill: IconType;
export declare const PiDiscordLogoFill: IconType;
export declare const PiDivideFill: IconType;
export declare const PiDnaFill: IconType;
export declare const PiDogFill: IconType;
export declare const PiDoorFill: IconType;
export declare const PiDoorOpenFill: IconType;
export declare const PiDotFill: IconType;
export declare const PiDotOutlineFill: IconType;
export declare const PiDotsNineFill: IconType;
export declare const PiDotsSixFill: IconType;
export declare const PiDotsSixVerticalFill: IconType;
export declare const PiDotsThreeCircleFill: IconType;
export declare const PiDotsThreeCircleVerticalFill: IconType;
export declare const PiDotsThreeFill: IconType;
export declare const PiDotsThreeOutlineFill: IconType;
export declare const PiDotsThreeOutlineVerticalFill: IconType;
export declare const PiDotsThreeVerticalFill: IconType;
export declare const PiDownloadFill: IconType;
export declare const PiDownloadSimpleFill: IconType;
export declare const PiDressFill: IconType;
export declare const PiDresserFill: IconType;
export declare const PiDribbbleLogoFill: IconType;
export declare const PiDroneFill: IconType;
export declare const PiDropFill: IconType;
export declare const PiDropHalfBottomFill: IconType;
export declare const PiDropHalfFill: IconType;
export declare const PiDropSimpleFill: IconType;
export declare const PiDropSlashFill: IconType;
export declare const PiDropboxLogoFill: IconType;
export declare const PiEarFill: IconType;
export declare const PiEarSlashFill: IconType;
export declare const PiEggCrackFill: IconType;
export declare const PiEggFill: IconType;
export declare const PiEjectFill: IconType;
export declare const PiEjectSimpleFill: IconType;
export declare const PiElevatorFill: IconType;
export declare const PiEmptyFill: IconType;
export declare const PiEngineFill: IconType;
export declare const PiEnvelopeFill: IconType;
export declare const PiEnvelopeOpenFill: IconType;
export declare const PiEnvelopeSimpleFill: IconType;
export declare const PiEnvelopeSimpleOpenFill: IconType;
export declare const PiEqualizerFill: IconType;
export declare const PiEqualsFill: IconType;
export declare const PiEraserFill: IconType;
export declare const PiEscalatorDownFill: IconType;
export declare const PiEscalatorUpFill: IconType;
export declare const PiExamFill: IconType;
export declare const PiExclamationMarkFill: IconType;
export declare const PiExcludeFill: IconType;
export declare const PiExcludeSquareFill: IconType;
export declare const PiExportFill: IconType;
export declare const PiEyeClosedFill: IconType;
export declare const PiEyeFill: IconType;
export declare const PiEyeSlashFill: IconType;
export declare const PiEyedropperFill: IconType;
export declare const PiEyedropperSampleFill: IconType;
export declare const PiEyeglassesFill: IconType;
export declare const PiEyesFill: IconType;
export declare const PiFaceMaskFill: IconType;
export declare const PiFacebookLogoFill: IconType;
export declare const PiFactoryFill: IconType;
export declare const PiFadersFill: IconType;
export declare const PiFadersHorizontalFill: IconType;
export declare const PiFalloutShelterFill: IconType;
export declare const PiFanFill: IconType;
export declare const PiFarmFill: IconType;
export declare const PiFastForwardCircleFill: IconType;
export declare const PiFastForwardFill: IconType;
export declare const PiFeatherFill: IconType;
export declare const PiFediverseLogoFill: IconType;
export declare const PiFigmaLogoFill: IconType;
export declare const PiFileArchiveFill: IconType;
export declare const PiFileArrowDownFill: IconType;
export declare const PiFileArrowUpFill: IconType;
export declare const PiFileAudioFill: IconType;
export declare const PiFileCFill: IconType;
export declare const PiFileCSharpFill: IconType;
export declare const PiFileCloudFill: IconType;
export declare const PiFileCodeFill: IconType;
export declare const PiFileCppFill: IconType;
export declare const PiFileCssFill: IconType;
export declare const PiFileCsvFill: IconType;
export declare const PiFileDashedFill: IconType;
export declare const PiFileDocFill: IconType;
export declare const PiFileFill: IconType;
export declare const PiFileHtmlFill: IconType;
export declare const PiFileImageFill: IconType;
export declare const PiFileIniFill: IconType;
export declare const PiFileJpgFill: IconType;
export declare const PiFileJsFill: IconType;
export declare const PiFileJsxFill: IconType;
export declare const PiFileLockFill: IconType;
export declare const PiFileMagnifyingGlassFill: IconType;
export declare const PiFileMdFill: IconType;
export declare const PiFileMinusFill: IconType;
export declare const PiFilePdfFill: IconType;
export declare const PiFilePlusFill: IconType;
export declare const PiFilePngFill: IconType;
export declare const PiFilePptFill: IconType;
export declare const PiFilePyFill: IconType;
export declare const PiFileRsFill: IconType;
export declare const PiFileSqlFill: IconType;
export declare const PiFileSvgFill: IconType;
export declare const PiFileTextFill: IconType;
export declare const PiFileTsFill: IconType;
export declare const PiFileTsxFill: IconType;
export declare const PiFileTxtFill: IconType;
export declare const PiFileVideoFill: IconType;
export declare const PiFileVueFill: IconType;
export declare const PiFileXFill: IconType;
export declare const PiFileXlsFill: IconType;
export declare const PiFileZipFill: IconType;
export declare const PiFilesFill: IconType;
export declare const PiFilmReelFill: IconType;
export declare const PiFilmScriptFill: IconType;
export declare const PiFilmSlateFill: IconType;
export declare const PiFilmStripFill: IconType;
export declare const PiFingerprintFill: IconType;
export declare const PiFingerprintSimpleFill: IconType;
export declare const PiFinnTheHumanFill: IconType;
export declare const PiFireExtinguisherFill: IconType;
export declare const PiFireFill: IconType;
export declare const PiFireSimpleFill: IconType;
export declare const PiFireTruckFill: IconType;
export declare const PiFirstAidFill: IconType;
export declare const PiFirstAidKitFill: IconType;
export declare const PiFishFill: IconType;
export declare const PiFishSimpleFill: IconType;
export declare const PiFlagBannerFill: IconType;
export declare const PiFlagBannerFoldFill: IconType;
export declare const PiFlagCheckeredFill: IconType;
export declare const PiFlagFill: IconType;
export declare const PiFlagPennantFill: IconType;
export declare const PiFlameFill: IconType;
export declare const PiFlashlightFill: IconType;
export declare const PiFlaskFill: IconType;
export declare const PiFlipHorizontalFill: IconType;
export declare const PiFlipVerticalFill: IconType;
export declare const PiFloppyDiskBackFill: IconType;
export declare const PiFloppyDiskFill: IconType;
export declare const PiFlowArrowFill: IconType;
export declare const PiFlowerFill: IconType;
export declare const PiFlowerLotusFill: IconType;
export declare const PiFlowerTulipFill: IconType;
export declare const PiFlyingSaucerFill: IconType;
export declare const PiFolderDashedFill: IconType;
export declare const PiFolderFill: IconType;
export declare const PiFolderLockFill: IconType;
export declare const PiFolderMinusFill: IconType;
export declare const PiFolderOpenFill: IconType;
export declare const PiFolderPlusFill: IconType;
export declare const PiFolderSimpleDashedFill: IconType;
export declare const PiFolderSimpleFill: IconType;
export declare const PiFolderSimpleLockFill: IconType;
export declare const PiFolderSimpleMinusFill: IconType;
export declare const PiFolderSimplePlusFill: IconType;
export declare const PiFolderSimpleStarFill: IconType;
export declare const PiFolderSimpleUserFill: IconType;
export declare const PiFolderStarFill: IconType;
export declare const PiFolderUserFill: IconType;
export declare const PiFoldersFill: IconType;
export declare const PiFootballFill: IconType;
export declare const PiFootballHelmetFill: IconType;
export declare const PiFootprintsFill: IconType;
export declare const PiForkKnifeFill: IconType;
export declare const PiFourKFill: IconType;
export declare const PiFrameCornersFill: IconType;
export declare const PiFramerLogoFill: IconType;
export declare const PiFunctionFill: IconType;
export declare const PiFunnelFill: IconType;
export declare const PiFunnelSimpleFill: IconType;
export declare const PiFunnelSimpleXFill: IconType;
export declare const PiFunnelXFill: IconType;
export declare const PiGameControllerFill: IconType;
export declare const PiGarageFill: IconType;
export declare const PiGasCanFill: IconType;
export declare const PiGasPumpFill: IconType;
export declare const PiGaugeFill: IconType;
export declare const PiGavelFill: IconType;
export declare const PiGearFill: IconType;
export declare const PiGearFineFill: IconType;
export declare const PiGearSixFill: IconType;
export declare const PiGenderFemaleFill: IconType;
export declare const PiGenderIntersexFill: IconType;
export declare const PiGenderMaleFill: IconType;
export declare const PiGenderNeuterFill: IconType;
export declare const PiGenderNonbinaryFill: IconType;
export declare const PiGenderTransgenderFill: IconType;
export declare const PiGhostFill: IconType;
export declare const PiGifFill: IconType;
export declare const PiGiftFill: IconType;
export declare const PiGitBranchFill: IconType;
export declare const PiGitCommitFill: IconType;
export declare const PiGitDiffFill: IconType;
export declare const PiGitForkFill: IconType;
export declare const PiGitMergeFill: IconType;
export declare const PiGitPullRequestFill: IconType;
export declare const PiGithubLogoFill: IconType;
export declare const PiGitlabLogoFill: IconType;
export declare const PiGitlabLogoSimpleFill: IconType;
export declare const PiGlobeFill: IconType;
export declare const PiGlobeHemisphereEastFill: IconType;
export declare const PiGlobeHemisphereWestFill: IconType;
export declare const PiGlobeSimpleFill: IconType;
export declare const PiGlobeSimpleXFill: IconType;
export declare const PiGlobeStandFill: IconType;
export declare const PiGlobeXFill: IconType;
export declare const PiGogglesFill: IconType;
export declare const PiGolfFill: IconType;
export declare const PiGoodreadsLogoFill: IconType;
export declare const PiGoogleCardboardLogoFill: IconType;
export declare const PiGoogleChromeLogoFill: IconType;
export declare const PiGoogleDriveLogoFill: IconType;
export declare const PiGoogleLogoFill: IconType;
export declare const PiGooglePhotosLogoFill: IconType;
export declare const PiGooglePlayLogoFill: IconType;
export declare const PiGooglePodcastsLogoFill: IconType;
export declare const PiGpsFill: IconType;
export declare const PiGpsFixFill: IconType;
export declare const PiGpsSlashFill: IconType;
export declare const PiGradientFill: IconType;
export declare const PiGraduationCapFill: IconType;
export declare const PiGrainsFill: IconType;
export declare const PiGrainsSlashFill: IconType;
export declare const PiGraphFill: IconType;
export declare const PiGraphicsCardFill: IconType;
export declare const PiGreaterThanFill: IconType;
export declare const PiGreaterThanOrEqualFill: IconType;
export declare const PiGridFourFill: IconType;
export declare const PiGridNineFill: IconType;
export declare const PiGuitarFill: IconType;
export declare const PiHairDryerFill: IconType;
export declare const PiHamburgerFill: IconType;
export declare const PiHammerFill: IconType;
export declare const PiHandArrowDownFill: IconType;
export declare const PiHandArrowUpFill: IconType;
export declare const PiHandCoinsFill: IconType;
export declare const PiHandDepositFill: IconType;
export declare const PiHandEyeFill: IconType;
export declare const PiHandFill: IconType;
export declare const PiHandFistFill: IconType;
export declare const PiHandGrabbingFill: IconType;
export declare const PiHandHeartFill: IconType;
export declare const PiHandPalmFill: IconType;
export declare const PiHandPeaceFill: IconType;
export declare const PiHandPointingFill: IconType;
export declare const PiHandSoapFill: IconType;
export declare const PiHandSwipeLeftFill: IconType;
export declare const PiHandSwipeRightFill: IconType;
export declare const PiHandTapFill: IconType;
export declare const PiHandWavingFill: IconType;
export declare const PiHandWithdrawFill: IconType;
export declare const PiHandbagFill: IconType;
export declare const PiHandbagSimpleFill: IconType;
export declare const PiHandsClappingFill: IconType;
export declare const PiHandsPrayingFill: IconType;
export declare const PiHandshakeFill: IconType;
export declare const PiHardDriveFill: IconType;
export declare const PiHardDrivesFill: IconType;
export declare const PiHardHatFill: IconType;
export declare const PiHashFill: IconType;
export declare const PiHashStraightFill: IconType;
export declare const PiHeadCircuitFill: IconType;
export declare const PiHeadlightsFill: IconType;
export declare const PiHeadphonesFill: IconType;
export declare const PiHeadsetFill: IconType;
export declare const PiHeartBreakFill: IconType;
export declare const PiHeartFill: IconType;
export declare const PiHeartHalfFill: IconType;
export declare const PiHeartStraightBreakFill: IconType;
export declare const PiHeartStraightFill: IconType;
export declare const PiHeartbeatFill: IconType;
export declare const PiHexagonFill: IconType;
export declare const PiHighDefinitionFill: IconType;
export declare const PiHighHeelFill: IconType;
export declare const PiHighlighterCircleFill: IconType;
export declare const PiHighlighterFill: IconType;
export declare const PiHockeyFill: IconType;
export declare const PiHoodieFill: IconType;
export declare const PiHorseFill: IconType;
export declare const PiHospitalFill: IconType;
export declare const PiHourglassFill: IconType;
export declare const PiHourglassHighFill: IconType;
export declare const PiHourglassLowFill: IconType;
export declare const PiHourglassMediumFill: IconType;
export declare const PiHourglassSimpleFill: IconType;
export declare const PiHourglassSimpleHighFill: IconType;
export declare const PiHourglassSimpleLowFill: IconType;
export declare const PiHourglassSimpleMediumFill: IconType;
export declare const PiHouseFill: IconType;
export declare const PiHouseLineFill: IconType;
export declare const PiHouseSimpleFill: IconType;
export declare const PiHurricaneFill: IconType;
export declare const PiIceCreamFill: IconType;
export declare const PiIdentificationBadgeFill: IconType;
export declare const PiIdentificationCardFill: IconType;
export declare const PiImageBrokenFill: IconType;
export declare const PiImageFill: IconType;
export declare const PiImageSquareFill: IconType;
export declare const PiImagesFill: IconType;
export declare const PiImagesSquareFill: IconType;
export declare const PiInfinityFill: IconType;
export declare const PiInfoFill: IconType;
export declare const PiInstagramLogoFill: IconType;
export declare const PiIntersectFill: IconType;
export declare const PiIntersectSquareFill: IconType;
export declare const PiIntersectThreeFill: IconType;
export declare const PiIntersectionFill: IconType;
export declare const PiInvoiceFill: IconType;
export declare const PiIslandFill: IconType;
export declare const PiJarFill: IconType;
export declare const PiJarLabelFill: IconType;
export declare const PiJeepFill: IconType;
export declare const PiJoystickFill: IconType;
export declare const PiKanbanFill: IconType;
export declare const PiKeyFill: IconType;
export declare const PiKeyReturnFill: IconType;
export declare const PiKeyboardFill: IconType;
export declare const PiKeyholeFill: IconType;
export declare const PiKnifeFill: IconType;
export declare const PiLadderFill: IconType;
export declare const PiLadderSimpleFill: IconType;
export declare const PiLampFill: IconType;
export declare const PiLampPendantFill: IconType;
export declare const PiLaptopFill: IconType;
export declare const PiLassoFill: IconType;
export declare const PiLastfmLogoFill: IconType;
export declare const PiLayoutFill: IconType;
export declare const PiLeafFill: IconType;
export declare const PiLecternFill: IconType;
export declare const PiLegoFill: IconType;
export declare const PiLegoSmileyFill: IconType;
export declare const PiLessThanFill: IconType;
export declare const PiLessThanOrEqualFill: IconType;
export declare const PiLetterCircleHFill: IconType;
export declare const PiLetterCirclePFill: IconType;
export declare const PiLetterCircleVFill: IconType;
export declare const PiLifebuoyFill: IconType;
export declare const PiLightbulbFilamentFill: IconType;
export declare const PiLightbulbFill: IconType;
export declare const PiLighthouseFill: IconType;
export declare const PiLightningAFill: IconType;
export declare const PiLightningFill: IconType;
export declare const PiLightningSlashFill: IconType;
export declare const PiLineSegmentFill: IconType;
export declare const PiLineSegmentsFill: IconType;
export declare const PiLineVerticalFill: IconType;
export declare const PiLinkBreakFill: IconType;
export declare const PiLinkFill: IconType;
export declare const PiLinkSimpleBreakFill: IconType;
export declare const PiLinkSimpleFill: IconType;
export declare const PiLinkSimpleHorizontalBreakFill: IconType;
export declare const PiLinkSimpleHorizontalFill: IconType;
export declare const PiLinkedinLogoFill: IconType;
export declare const PiLinktreeLogoFill: IconType;
export declare const PiLinuxLogoFill: IconType;
export declare const PiListBulletsFill: IconType;
export declare const PiListChecksFill: IconType;
export declare const PiListDashesFill: IconType;
export declare const PiListFill: IconType;
export declare const PiListHeartFill: IconType;
export declare const PiListMagnifyingGlassFill: IconType;
export declare const PiListNumbersFill: IconType;
export declare const PiListPlusFill: IconType;
export declare const PiListStarFill: IconType;
export declare const PiLockFill: IconType;
export declare const PiLockKeyFill: IconType;
export declare const PiLockKeyOpenFill: IconType;
export declare const PiLockLaminatedFill: IconType;
export declare const PiLockLaminatedOpenFill: IconType;
export declare const PiLockOpenFill: IconType;
export declare const PiLockSimpleFill: IconType;
export declare const PiLockSimpleOpenFill: IconType;
export declare const PiLockersFill: IconType;
export declare const PiLogFill: IconType;
export declare const PiMagicWandFill: IconType;
export declare const PiMagnetFill: IconType;
export declare const PiMagnetStraightFill: IconType;
export declare const PiMagnifyingGlassFill: IconType;
export declare const PiMagnifyingGlassMinusFill: IconType;
export declare const PiMagnifyingGlassPlusFill: IconType;
export declare const PiMailboxFill: IconType;
export declare const PiMapPinAreaFill: IconType;
export declare const PiMapPinFill: IconType;
export declare const PiMapPinLineFill: IconType;
export declare const PiMapPinPlusFill: IconType;
export declare const PiMapPinSimpleAreaFill: IconType;
export declare const PiMapPinSimpleFill: IconType;
export declare const PiMapPinSimpleLineFill: IconType;
export declare const PiMapTrifoldFill: IconType;
export declare const PiMarkdownLogoFill: IconType;
export declare const PiMarkerCircleFill: IconType;
export declare const PiMartiniFill: IconType;
export declare const PiMaskHappyFill: IconType;
export declare const PiMaskSadFill: IconType;
export declare const PiMastodonLogoFill: IconType;
export declare const PiMathOperationsFill: IconType;
export declare const PiMatrixLogoFill: IconType;
export declare const PiMedalFill: IconType;
export declare const PiMedalMilitaryFill: IconType;
export declare const PiMediumLogoFill: IconType;
export declare const PiMegaphoneFill: IconType;
export declare const PiMegaphoneSimpleFill: IconType;
export declare const PiMemberOfFill: IconType;
export declare const PiMemoryFill: IconType;
export declare const PiMessengerLogoFill: IconType;
export declare const PiMetaLogoFill: IconType;
export declare const PiMeteorFill: IconType;
export declare const PiMetronomeFill: IconType;
export declare const PiMicrophoneFill: IconType;
export declare const PiMicrophoneSlashFill: IconType;
export declare const PiMicrophoneStageFill: IconType;
export declare const PiMicroscopeFill: IconType;
export declare const PiMicrosoftExcelLogoFill: IconType;
export declare const PiMicrosoftOutlookLogoFill: IconType;
export declare const PiMicrosoftPowerpointLogoFill: IconType;
export declare const PiMicrosoftTeamsLogoFill: IconType;
export declare const PiMicrosoftWordLogoFill: IconType;
export declare const PiMinusCircleFill: IconType;
export declare const PiMinusFill: IconType;
export declare const PiMinusSquareFill: IconType;
export declare const PiMoneyFill: IconType;
export declare const PiMoneyWavyFill: IconType;
export declare const PiMonitorArrowUpFill: IconType;
export declare const PiMonitorFill: IconType;
export declare const PiMonitorPlayFill: IconType;
export declare const PiMoonFill: IconType;
export declare const PiMoonStarsFill: IconType;
export declare const PiMopedFill: IconType;
export declare const PiMopedFrontFill: IconType;
export declare const PiMosqueFill: IconType;
export declare const PiMotorcycleFill: IconType;
export declare const PiMountainsFill: IconType;
export declare const PiMouseFill: IconType;
export declare const PiMouseLeftClickFill: IconType;
export declare const PiMouseMiddleClickFill: IconType;
export declare const PiMouseRightClickFill: IconType;
export declare const PiMouseScrollFill: IconType;
export declare const PiMouseSimpleFill: IconType;
export declare const PiMusicNoteFill: IconType;
export declare const PiMusicNoteSimpleFill: IconType;
export declare const PiMusicNotesFill: IconType;
export declare const PiMusicNotesMinusFill: IconType;
export declare const PiMusicNotesPlusFill: IconType;
export declare const PiMusicNotesSimpleFill: IconType;
export declare const PiNavigationArrowFill: IconType;
export declare const PiNeedleFill: IconType;
export declare const PiNetworkFill: IconType;
export declare const PiNetworkSlashFill: IconType;
export declare const PiNetworkXFill: IconType;
export declare const PiNewspaperClippingFill: IconType;
export declare const PiNewspaperFill: IconType;
export declare const PiNotEqualsFill: IconType;
export declare const PiNotMemberOfFill: IconType;
export declare const PiNotSubsetOfFill: IconType;
export declare const PiNotSupersetOfFill: IconType;
export declare const PiNotchesFill: IconType;
export declare const PiNoteBlankFill: IconType;
export declare const PiNoteFill: IconType;
export declare const PiNotePencilFill: IconType;
export declare const PiNotebookFill: IconType;
export declare const PiNotepadFill: IconType;
export declare const PiNotificationFill: IconType;
export declare const PiNotionLogoFill: IconType;
export declare const PiNuclearPlantFill: IconType;
export declare const PiNumberCircleEightFill: IconType;
export declare const PiNumberCircleFiveFill: IconType;
export declare const PiNumberCircleFourFill: IconType;
export declare const PiNumberCircleNineFill: IconType;
export declare const PiNumberCircleOneFill: IconType;
export declare const PiNumberCircleSevenFill: IconType;
export declare const PiNumberCircleSixFill: IconType;
export declare const PiNumberCircleThreeFill: IconType;
export declare const PiNumberCircleTwoFill: IconType;
export declare const PiNumberCircleZeroFill: IconType;
export declare const PiNumberEightFill: IconType;
export declare const PiNumberFiveFill: IconType;
export declare const PiNumberFourFill: IconType;
export declare const PiNumberNineFill: IconType;
export declare const PiNumberOneFill: IconType;
export declare const PiNumberSevenFill: IconType;
export declare const PiNumberSixFill: IconType;
export declare const PiNumberSquareEightFill: IconType;
export declare const PiNumberSquareFiveFill: IconType;
export declare const PiNumberSquareFourFill: IconType;
export declare const PiNumberSquareNineFill: IconType;
export declare const PiNumberSquareOneFill: IconType;
export declare const PiNumberSquareSevenFill: IconType;
export declare const PiNumberSquareSixFill: IconType;
export declare const PiNumberSquareThreeFill: IconType;
export declare const PiNumberSquareTwoFill: IconType;
export declare const PiNumberSquareZeroFill: IconType;
export declare const PiNumberThreeFill: IconType;
export declare const PiNumberTwoFill: IconType;
export declare const PiNumberZeroFill: IconType;
export declare const PiNumpadFill: IconType;
export declare const PiNutFill: IconType;
export declare const PiNyTimesLogoFill: IconType;
export declare const PiOctagonFill: IconType;
export declare const PiOfficeChairFill: IconType;
export declare const PiOnigiriFill: IconType;
export declare const PiOpenAiLogoFill: IconType;
export declare const PiOptionFill: IconType;
export declare const PiOrangeFill: IconType;
export declare const PiOrangeSliceFill: IconType;
export declare const PiOvenFill: IconType;
export declare const PiPackageFill: IconType;
export declare const PiPaintBrushBroadFill: IconType;
export declare const PiPaintBrushFill: IconType;
export declare const PiPaintBrushHouseholdFill: IconType;
export declare const PiPaintBucketFill: IconType;
export declare const PiPaintRollerFill: IconType;
export declare const PiPaletteFill: IconType;
export declare const PiPanoramaFill: IconType;
export declare const PiPantsFill: IconType;
export declare const PiPaperPlaneFill: IconType;
export declare const PiPaperPlaneRightFill: IconType;
export declare const PiPaperPlaneTiltFill: IconType;
export declare const PiPaperclipFill: IconType;
export declare const PiPaperclipHorizontalFill: IconType;
export declare const PiParachuteFill: IconType;
export declare const PiParagraphFill: IconType;
export declare const PiParallelogramFill: IconType;
export declare const PiParkFill: IconType;
export declare const PiPasswordFill: IconType;
export declare const PiPathFill: IconType;
export declare const PiPatreonLogoFill: IconType;
export declare const PiPauseCircleFill: IconType;
export declare const PiPauseFill: IconType;
export declare const PiPawPrintFill: IconType;
export declare const PiPaypalLogoFill: IconType;
export declare const PiPeaceFill: IconType;
export declare const PiPenFill: IconType;
export declare const PiPenNibFill: IconType;
export declare const PiPenNibStraightFill: IconType;
export declare const PiPencilCircleFill: IconType;
export declare const PiPencilFill: IconType;
export declare const PiPencilLineFill: IconType;
export declare const PiPencilRulerFill: IconType;
export declare const PiPencilSimpleFill: IconType;
export declare const PiPencilSimpleLineFill: IconType;
export declare const PiPencilSimpleSlashFill: IconType;
export declare const PiPencilSlashFill: IconType;
export declare const PiPentagonFill: IconType;
export declare const PiPentagramFill: IconType;
export declare const PiPepperFill: IconType;
export declare const PiPercentFill: IconType;
export declare const PiPersonArmsSpreadFill: IconType;
export declare const PiPersonFill: IconType;
export declare const PiPersonSimpleBikeFill: IconType;
export declare const PiPersonSimpleCircleFill: IconType;
export declare const PiPersonSimpleFill: IconType;
export declare const PiPersonSimpleHikeFill: IconType;
export declare const PiPersonSimpleRunFill: IconType;
export declare const PiPersonSimpleSkiFill: IconType;
export declare const PiPersonSimpleSnowboardFill: IconType;
export declare const PiPersonSimpleSwimFill: IconType;
export declare const PiPersonSimpleTaiChiFill: IconType;
export declare const PiPersonSimpleThrowFill: IconType;
export declare const PiPersonSimpleWalkFill: IconType;
export declare const PiPerspectiveFill: IconType;
export declare const PiPhoneCallFill: IconType;
export declare const PiPhoneDisconnectFill: IconType;
export declare const PiPhoneFill: IconType;
export declare const PiPhoneIncomingFill: IconType;
export declare const PiPhoneListFill: IconType;
export declare const PiPhoneOutgoingFill: IconType;
export declare const PiPhonePauseFill: IconType;
export declare const PiPhonePlusFill: IconType;
export declare const PiPhoneSlashFill: IconType;
export declare const PiPhoneTransferFill: IconType;
export declare const PiPhoneXFill: IconType;
export declare const PiPhosphorLogoFill: IconType;
export declare const PiPiFill: IconType;
export declare const PiPianoKeysFill: IconType;
export declare const PiPicnicTableFill: IconType;
export declare const PiPictureInPictureFill: IconType;
export declare const PiPiggyBankFill: IconType;
export declare const PiPillFill: IconType;
export declare const PiPingPongFill: IconType;
export declare const PiPintGlassFill: IconType;
export declare const PiPinterestLogoFill: IconType;
export declare const PiPinwheelFill: IconType;
export declare const PiPipeFill: IconType;
export declare const PiPipeWrenchFill: IconType;
export declare const PiPixLogoFill: IconType;
export declare const PiPizzaFill: IconType;
export declare const PiPlaceholderFill: IconType;
export declare const PiPlanetFill: IconType;
export declare const PiPlantFill: IconType;
export declare const PiPlayCircleFill: IconType;
export declare const PiPlayFill: IconType;
export declare const PiPlayPauseFill: IconType;
export declare const PiPlaylistFill: IconType;
export declare const PiPlugChargingFill: IconType;
export declare const PiPlugFill: IconType;
export declare const PiPlugsConnectedFill: IconType;
export declare const PiPlugsFill: IconType;
export declare const PiPlusCircleFill: IconType;
export declare const PiPlusFill: IconType;
export declare const PiPlusMinusFill: IconType;
export declare const PiPlusSquareFill: IconType;
export declare const PiPokerChipFill: IconType;
export declare const PiPoliceCarFill: IconType;
export declare const PiPolygonFill: IconType;
export declare const PiPopcornFill: IconType;
export declare const PiPopsicleFill: IconType;
export declare const PiPottedPlantFill: IconType;
export declare const PiPowerFill: IconType;
export declare const PiPrescriptionFill: IconType;
export declare const PiPresentationChartFill: IconType;
export declare const PiPresentationFill: IconType;
export declare const PiPrinterFill: IconType;
export declare const PiProhibitFill: IconType;
export declare const PiProhibitInsetFill: IconType;
export declare const PiProjectorScreenChartFill: IconType;
export declare const PiProjectorScreenFill: IconType;
export declare const PiPulseFill: IconType;
export declare const PiPushPinFill: IconType;
export declare const PiPushPinSimpleFill: IconType;
export declare const PiPushPinSimpleSlashFill: IconType;
export declare const PiPushPinSlashFill: IconType;
export declare const PiPuzzlePieceFill: IconType;
export declare const PiQrCodeFill: IconType;
export declare const PiQuestionFill: IconType;
export declare const PiQuestionMarkFill: IconType;
export declare const PiQueueFill: IconType;
export declare const PiQuotesFill: IconType;
export declare const PiRabbitFill: IconType;
export declare const PiRacquetFill: IconType;
export declare const PiRadicalFill: IconType;
export declare const PiRadioButtonFill: IconType;
export declare const PiRadioFill: IconType;
export declare const PiRadioactiveFill: IconType;
export declare const PiRainbowCloudFill: IconType;
export declare const PiRainbowFill: IconType;
export declare const PiRankingFill: IconType;
export declare const PiReadCvLogoFill: IconType;
export declare const PiReceiptFill: IconType;
export declare const PiReceiptXFill: IconType;
export declare const PiRecordFill: IconType;
export declare const PiRectangleDashedFill: IconType;
export declare const PiRectangleFill: IconType;
export declare const PiRecycleFill: IconType;
export declare const PiRedditLogoFill: IconType;
export declare const PiRepeatFill: IconType;
export declare const PiRepeatOnceFill: IconType;
export declare const PiReplitLogoFill: IconType;
export declare const PiResizeFill: IconType;
export declare const PiRewindCircleFill: IconType;
export declare const PiRewindFill: IconType;
export declare const PiRoadHorizonFill: IconType;
export declare const PiRobotFill: IconType;
export declare const PiRocketFill: IconType;
export declare const PiRocketLaunchFill: IconType;
export declare const PiRowsFill: IconType;
export declare const PiRowsPlusBottomFill: IconType;
export declare const PiRowsPlusTopFill: IconType;
export declare const PiRssFill: IconType;
export declare const PiRssSimpleFill: IconType;
export declare const PiRugFill: IconType;
export declare const PiRulerFill: IconType;
export declare const PiSailboatFill: IconType;
export declare const PiScalesFill: IconType;
export declare const PiScanFill: IconType;
export declare const PiScanSmileyFill: IconType;
export declare const PiScissorsFill: IconType;
export declare const PiScooterFill: IconType;
export declare const PiScreencastFill: IconType;
export declare const PiScrewdriverFill: IconType;
export declare const PiScribbleFill: IconType;
export declare const PiScribbleLoopFill: IconType;
export declare const PiScrollFill: IconType;
export declare const PiSealCheckFill: IconType;
export declare const PiSealFill: IconType;
export declare const PiSealPercentFill: IconType;
export declare const PiSealQuestionFill: IconType;
export declare const PiSealWarningFill: IconType;
export declare const PiSeatFill: IconType;
export declare const PiSeatbeltFill: IconType;
export declare const PiSecurityCameraFill: IconType;
export declare const PiSelectionAllFill: IconType;
export declare const PiSelectionBackgroundFill: IconType;
export declare const PiSelectionFill: IconType;
export declare const PiSelectionForegroundFill: IconType;
export declare const PiSelectionInverseFill: IconType;
export declare const PiSelectionPlusFill: IconType;
export declare const PiSelectionSlashFill: IconType;
export declare const PiShapesFill: IconType;
export declare const PiShareFatFill: IconType;
export declare const PiShareFill: IconType;
export declare const PiShareNetworkFill: IconType;
export declare const PiShieldCheckFill: IconType;
export declare const PiShieldCheckeredFill: IconType;
export declare const PiShieldChevronFill: IconType;
export declare const PiShieldFill: IconType;
export declare const PiShieldPlusFill: IconType;
export declare const PiShieldSlashFill: IconType;
export declare const PiShieldStarFill: IconType;
export declare const PiShieldWarningFill: IconType;
export declare const PiShippingContainerFill: IconType;
export declare const PiShirtFoldedFill: IconType;
export declare const PiShootingStarFill: IconType;
export declare const PiShoppingBagFill: IconType;
export declare const PiShoppingBagOpenFill: IconType;
export declare const PiShoppingCartFill: IconType;
export declare const PiShoppingCartSimpleFill: IconType;
export declare const PiShovelFill: IconType;
export declare const PiShowerFill: IconType;
export declare const PiShrimpFill: IconType;
export declare const PiShuffleAngularFill: IconType;
export declare const PiShuffleFill: IconType;
export declare const PiShuffleSimpleFill: IconType;
export declare const PiSidebarFill: IconType;
export declare const PiSidebarSimpleFill: IconType;
export declare const PiSigmaFill: IconType;
export declare const PiSignInFill: IconType;
export declare const PiSignOutFill: IconType;
export declare const PiSignatureFill: IconType;
export declare const PiSignpostFill: IconType;
export declare const PiSimCardFill: IconType;
export declare const PiSirenFill: IconType;
export declare const PiSketchLogoFill: IconType;
export declare const PiSkipBackCircleFill: IconType;
export declare const PiSkipBackFill: IconType;
export declare const PiSkipForwardCircleFill: IconType;
export declare const PiSkipForwardFill: IconType;
export declare const PiSkullFill: IconType;
export declare const PiSkypeLogoFill: IconType;
export declare const PiSlackLogoFill: IconType;
export declare const PiSlidersFill: IconType;
export declare const PiSlidersHorizontalFill: IconType;
export declare const PiSlideshowFill: IconType;
export declare const PiSmileyAngryFill: IconType;
export declare const PiSmileyBlankFill: IconType;
export declare const PiSmileyFill: IconType;
export declare const PiSmileyMehFill: IconType;
export declare const PiSmileyMeltingFill: IconType;
export declare const PiSmileyNervousFill: IconType;
export declare const PiSmileySadFill: IconType;
export declare const PiSmileyStickerFill: IconType;
export declare const PiSmileyWinkFill: IconType;
export declare const PiSmileyXEyesFill: IconType;
export declare const PiSnapchatLogoFill: IconType;
export declare const PiSneakerFill: IconType;
export declare const PiSneakerMoveFill: IconType;
export declare const PiSnowflakeFill: IconType;
export declare const PiSoccerBallFill: IconType;
export declare const PiSockFill: IconType;
export declare const PiSolarPanelFill: IconType;
export declare const PiSolarRoofFill: IconType;
export declare const PiSortAscendingFill: IconType;
export declare const PiSortDescendingFill: IconType;
export declare const PiSoundcloudLogoFill: IconType;
export declare const PiSpadeFill: IconType;
export declare const PiSparkleFill: IconType;
export declare const PiSpeakerHifiFill: IconType;
export declare const PiSpeakerHighFill: IconType;
export declare const PiSpeakerLowFill: IconType;
export declare const PiSpeakerNoneFill: IconType;
export declare const PiSpeakerSimpleHighFill: IconType;
export declare const PiSpeakerSimpleLowFill: IconType;
export declare const PiSpeakerSimpleNoneFill: IconType;
export declare const PiSpeakerSimpleSlashFill: IconType;
export declare const PiSpeakerSimpleXFill: IconType;
export declare const PiSpeakerSlashFill: IconType;
export declare const PiSpeakerXFill: IconType;
export declare const PiSpeedometerFill: IconType;
export declare const PiSphereFill: IconType;
export declare const PiSpinnerBallFill: IconType;
export declare const PiSpinnerFill: IconType;
export declare const PiSpinnerGapFill: IconType;
export declare const PiSpiralFill: IconType;
export declare const PiSplitHorizontalFill: IconType;
export declare const PiSplitVerticalFill: IconType;
export declare const PiSpotifyLogoFill: IconType;
export declare const PiSprayBottleFill: IconType;
export declare const PiSquareFill: IconType;
export declare const PiSquareHalfBottomFill: IconType;
export declare const PiSquareHalfFill: IconType;
export declare const PiSquareLogoFill: IconType;
export declare const PiSquareSplitHorizontalFill: IconType;
export declare const PiSquareSplitVerticalFill: IconType;
export declare const PiSquaresFourFill: IconType;
export declare const PiStackFill: IconType;
export declare const PiStackMinusFill: IconType;
export declare const PiStackOverflowLogoFill: IconType;
export declare const PiStackPlusFill: IconType;
export declare const PiStackSimpleFill: IconType;
export declare const PiStairsFill: IconType;
export declare const PiStampFill: IconType;
export declare const PiStandardDefinitionFill: IconType;
export declare const PiStarAndCrescentFill: IconType;
export declare const PiStarFill: IconType;
export declare const PiStarFourFill: IconType;
export declare const PiStarHalfFill: IconType;
export declare const PiStarOfDavidFill: IconType;
export declare const PiSteamLogoFill: IconType;
export declare const PiSteeringWheelFill: IconType;
export declare const PiStepsFill: IconType;
export declare const PiStethoscopeFill: IconType;
export declare const PiStickerFill: IconType;
export declare const PiStoolFill: IconType;
export declare const PiStopCircleFill: IconType;
export declare const PiStopFill: IconType;
export declare const PiStorefrontFill: IconType;
export declare const PiStrategyFill: IconType;
export declare const PiStripeLogoFill: IconType;
export declare const PiStudentFill: IconType;
export declare const PiSubsetOfFill: IconType;
export declare const PiSubsetProperOfFill: IconType;
export declare const PiSubtitlesFill: IconType;
export declare const PiSubtitlesSlashFill: IconType;
export declare const PiSubtractFill: IconType;
export declare const PiSubtractSquareFill: IconType;
export declare const PiSubwayFill: IconType;
export declare const PiSuitcaseFill: IconType;
export declare const PiSuitcaseRollingFill: IconType;
export declare const PiSuitcaseSimpleFill: IconType;
export declare const PiSunDimFill: IconType;
export declare const PiSunFill: IconType;
export declare const PiSunHorizonFill: IconType;
export declare const PiSunglassesFill: IconType;
export declare const PiSupersetOfFill: IconType;
export declare const PiSupersetProperOfFill: IconType;
export declare const PiSwapFill: IconType;
export declare const PiSwatchesFill: IconType;
export declare const PiSwimmingPoolFill: IconType;
export declare const PiSwordFill: IconType;
export declare const PiSynagogueFill: IconType;
export declare const PiSyringeFill: IconType;
export declare const PiTShirtFill: IconType;
export declare const PiTableFill: IconType;
export declare const PiTabsFill: IconType;
export declare const PiTagChevronFill: IconType;
export declare const PiTagFill: IconType;
export declare const PiTagSimpleFill: IconType;
export declare const PiTargetFill: IconType;
export declare const PiTaxiFill: IconType;
export declare const PiTeaBagFill: IconType;
export declare const PiTelegramLogoFill: IconType;
export declare const PiTelevisionFill: IconType;
export declare const PiTelevisionSimpleFill: IconType;
export declare const PiTennisBallFill: IconType;
export declare const PiTentFill: IconType;
export declare const PiTerminalFill: IconType;
export declare const PiTerminalWindowFill: IconType;
export declare const PiTestTubeFill: IconType;
export declare const PiTextAUnderlineFill: IconType;
export declare const PiTextAaFill: IconType;
export declare const PiTextAlignCenterFill: IconType;
export declare const PiTextAlignJustifyFill: IconType;
export declare const PiTextAlignLeftFill: IconType;
export declare const PiTextAlignRightFill: IconType;
export declare const PiTextBFill: IconType;
export declare const PiTextColumnsFill: IconType;
export declare const PiTextHFill: IconType;
export declare const PiTextHFiveFill: IconType;
export declare const PiTextHFourFill: IconType;
export declare const PiTextHOneFill: IconType;
export declare const PiTextHSixFill: IconType;
export declare const PiTextHThreeFill: IconType;
export declare const PiTextHTwoFill: IconType;
export declare const PiTextIndentFill: IconType;
export declare const PiTextItalicFill: IconType;
export declare const PiTextOutdentFill: IconType;
export declare const PiTextStrikethroughFill: IconType;
export declare const PiTextSubscriptFill: IconType;
export declare const PiTextSuperscriptFill: IconType;
export declare const PiTextTFill: IconType;
export declare const PiTextTSlashFill: IconType;
export declare const PiTextUnderlineFill: IconType;
export declare const PiTextboxFill: IconType;
export declare const PiThermometerColdFill: IconType;
export declare const PiThermometerFill: IconType;
export declare const PiThermometerHotFill: IconType;
export declare const PiThermometerSimpleFill: IconType;
export declare const PiThreadsLogoFill: IconType;
export declare const PiThreeDFill: IconType;
export declare const PiThumbsDownFill: IconType;
export declare const PiThumbsUpFill: IconType;
export declare const PiTicketFill: IconType;
export declare const PiTidalLogoFill: IconType;
export declare const PiTiktokLogoFill: IconType;
export declare const PiTildeFill: IconType;
export declare const PiTimerFill: IconType;
export declare const PiTipJarFill: IconType;
export declare const PiTipiFill: IconType;
export declare const PiTireFill: IconType;
export declare const PiToggleLeftFill: IconType;
export declare const PiToggleRightFill: IconType;
export declare const PiToiletFill: IconType;
export declare const PiToiletPaperFill: IconType;
export declare const PiToolboxFill: IconType;
export declare const PiToothFill: IconType;
export declare const PiTornadoFill: IconType;
export declare const PiToteFill: IconType;
export declare const PiToteSimpleFill: IconType;
export declare const PiTowelFill: IconType;
export declare const PiTractorFill: IconType;
export declare const PiTrademarkFill: IconType;
export declare const PiTrademarkRegisteredFill: IconType;
export declare const PiTrafficConeFill: IconType;
export declare const PiTrafficSignFill: IconType;
export declare const PiTrafficSignalFill: IconType;
export declare const PiTrainFill: IconType;
export declare const PiTrainRegionalFill: IconType;
export declare const PiTrainSimpleFill: IconType;
export declare const PiTramFill: IconType;
export declare const PiTranslateFill: IconType;
export declare const PiTrashFill: IconType;
export declare const PiTrashSimpleFill: IconType;
export declare const PiTrayArrowDownFill: IconType;
export declare const PiTrayArrowUpFill: IconType;
export declare const PiTrayFill: IconType;
export declare const PiTreasureChestFill: IconType;
export declare const PiTreeEvergreenFill: IconType;
export declare const PiTreeFill: IconType;
export declare const PiTreePalmFill: IconType;
export declare const PiTreeStructureFill: IconType;
export declare const PiTreeViewFill: IconType;
export declare const PiTrendDownFill: IconType;
export declare const PiTrendUpFill: IconType;
export declare const PiTriangleDashedFill: IconType;
export declare const PiTriangleFill: IconType;
export declare const PiTrolleyFill: IconType;
export declare const PiTrolleySuitcaseFill: IconType;
export declare const PiTrophyFill: IconType;
export declare const PiTruckFill: IconType;
export declare const PiTruckTrailerFill: IconType;
export declare const PiTumblrLogoFill: IconType;
export declare const PiTwitchLogoFill: IconType;
export declare const PiTwitterLogoFill: IconType;
export declare const PiUmbrellaFill: IconType;
export declare const PiUmbrellaSimpleFill: IconType;
export declare const PiUnionFill: IconType;
export declare const PiUniteFill: IconType;
export declare const PiUniteSquareFill: IconType;
export declare const PiUploadFill: IconType;
export declare const PiUploadSimpleFill: IconType;
export declare const PiUsbFill: IconType;
export declare const PiUserCheckFill: IconType;
export declare const PiUserCircleCheckFill: IconType;
export declare const PiUserCircleDashedFill: IconType;
export declare const PiUserCircleFill: IconType;
export declare const PiUserCircleGearFill: IconType;
export declare const PiUserCircleMinusFill: IconType;
export declare const PiUserCirclePlusFill: IconType;
export declare const PiUserFill: IconType;
export declare const PiUserFocusFill: IconType;
export declare const PiUserGearFill: IconType;
export declare const PiUserListFill: IconType;
export declare const PiUserMinusFill: IconType;
export declare const PiUserPlusFill: IconType;
export declare const PiUserRectangleFill: IconType;
export declare const PiUserSoundFill: IconType;
export declare const PiUserSquareFill: IconType;
export declare const PiUserSwitchFill: IconType;
export declare const PiUsersFill: IconType;
export declare const PiUsersFourFill: IconType;
export declare const PiUsersThreeFill: IconType;
export declare const PiVanFill: IconType;
export declare const PiVaultFill: IconType;
export declare const PiVectorThreeFill: IconType;
export declare const PiVectorTwoFill: IconType;
export declare const PiVibrateFill: IconType;
export declare const PiVideoCameraFill: IconType;
export declare const PiVideoCameraSlashFill: IconType;
export declare const PiVideoConferenceFill: IconType;
export declare const PiVideoFill: IconType;
export declare const PiVignetteFill: IconType;
export declare const PiVinylRecordFill: IconType;
export declare const PiVirtualRealityFill: IconType;
export declare const PiVirusFill: IconType;
export declare const PiVisorFill: IconType;
export declare const PiVoicemailFill: IconType;
export declare const PiVolleyballFill: IconType;
export declare const PiWallFill: IconType;
export declare const PiWalletFill: IconType;
export declare const PiWarehouseFill: IconType;
export declare const PiWarningCircleFill: IconType;
export declare const PiWarningDiamondFill: IconType;
export declare const PiWarningFill: IconType;
export declare const PiWarningOctagonFill: IconType;
export declare const PiWashingMachineFill: IconType;
export declare const PiWatchFill: IconType;
export declare const PiWaveSawtoothFill: IconType;
export declare const PiWaveSineFill: IconType;
export declare const PiWaveSquareFill: IconType;
export declare const PiWaveTriangleFill: IconType;
export declare const PiWaveformFill: IconType;
export declare const PiWaveformSlashFill: IconType;
export declare const PiWavesFill: IconType;
export declare const PiWebcamFill: IconType;
export declare const PiWebcamSlashFill: IconType;
export declare const PiWebhooksLogoFill: IconType;
export declare const PiWechatLogoFill: IconType;
export declare const PiWhatsappLogoFill: IconType;
export declare const PiWheelchairFill: IconType;
export declare const PiWheelchairMotionFill: IconType;
export declare const PiWifiHighFill: IconType;
export declare const PiWifiLowFill: IconType;
export declare const PiWifiMediumFill: IconType;
export declare const PiWifiNoneFill: IconType;
export declare const PiWifiSlashFill: IconType;
export declare const PiWifiXFill: IconType;
export declare const PiWindFill: IconType;
export declare const PiWindmillFill: IconType;
export declare const PiWindowsLogoFill: IconType;
export declare const PiWineFill: IconType;
export declare const PiWrenchFill: IconType;
export declare const PiXCircleFill: IconType;
export declare const PiXFill: IconType;
export declare const PiXLogoFill: IconType;
export declare const PiXSquareFill: IconType;
export declare const PiYarnFill: IconType;
export declare const PiYinYangFill: IconType;
export declare const PiYoutubeLogoFill: IconType;
export declare const PiAcornLight: IconType;
export declare const PiAddressBookLight: IconType;
export declare const PiAddressBookTabsLight: IconType;
export declare const PiAirTrafficControlLight: IconType;
export declare const PiAirplaneInFlightLight: IconType;
export declare const PiAirplaneLandingLight: IconType;
export declare const PiAirplaneLight: IconType;
export declare const PiAirplaneTakeoffLight: IconType;
export declare const PiAirplaneTaxiingLight: IconType;
export declare const PiAirplaneTiltLight: IconType;
export declare const PiAirplayLight: IconType;
export declare const PiAlarmLight: IconType;
export declare const PiAlienLight: IconType;
export declare const PiAlignBottomLight: IconType;
export declare const PiAlignBottomSimpleLight: IconType;
export declare const PiAlignCenterHorizontalLight: IconType;
export declare const PiAlignCenterHorizontalSimpleLight: IconType;
export declare const PiAlignCenterVerticalLight: IconType;
export declare const PiAlignCenterVerticalSimpleLight: IconType;
export declare const PiAlignLeftLight: IconType;
export declare const PiAlignLeftSimpleLight: IconType;
export declare const PiAlignRightLight: IconType;
export declare const PiAlignRightSimpleLight: IconType;
export declare const PiAlignTopLight: IconType;
export declare const PiAlignTopSimpleLight: IconType;
export declare const PiAmazonLogoLight: IconType;
export declare const PiAmbulanceLight: IconType;
export declare const PiAnchorLight: IconType;
export declare const PiAnchorSimpleLight: IconType;
export declare const PiAndroidLogoLight: IconType;
export declare const PiAngleLight: IconType;
export declare const PiAngularLogoLight: IconType;
export declare const PiApertureLight: IconType;
export declare const PiAppStoreLogoLight: IconType;
export declare const PiAppWindowLight: IconType;
export declare const PiAppleLogoLight: IconType;
export declare const PiApplePodcastsLogoLight: IconType;
export declare const PiApproximateEqualsLight: IconType;
export declare const PiArchiveLight: IconType;
export declare const PiArmchairLight: IconType;
export declare const PiArrowArcLeftLight: IconType;
export declare const PiArrowArcRightLight: IconType;
export declare const PiArrowBendDoubleUpLeftLight: IconType;
export declare const PiArrowBendDoubleUpRightLight: IconType;
export declare const PiArrowBendDownLeftLight: IconType;
export declare const PiArrowBendDownRightLight: IconType;
export declare const PiArrowBendLeftDownLight: IconType;
export declare const PiArrowBendLeftUpLight: IconType;
export declare const PiArrowBendRightDownLight: IconType;
export declare const PiArrowBendRightUpLight: IconType;
export declare const PiArrowBendUpLeftLight: IconType;
export declare const PiArrowBendUpRightLight: IconType;
export declare const PiArrowCircleDownLeftLight: IconType;
export declare const PiArrowCircleDownLight: IconType;
export declare const PiArrowCircleDownRightLight: IconType;
export declare const PiArrowCircleLeftLight: IconType;
export declare const PiArrowCircleRightLight: IconType;
export declare const PiArrowCircleUpLeftLight: IconType;
export declare const PiArrowCircleUpLight: IconType;
export declare const PiArrowCircleUpRightLight: IconType;
export declare const PiArrowClockwiseLight: IconType;
export declare const PiArrowCounterClockwiseLight: IconType;
export declare const PiArrowDownLeftLight: IconType;
export declare const PiArrowDownLight: IconType;
export declare const PiArrowDownRightLight: IconType;
export declare const PiArrowElbowDownLeftLight: IconType;
export declare const PiArrowElbowDownRightLight: IconType;
export declare const PiArrowElbowLeftDownLight: IconType;
export declare const PiArrowElbowLeftLight: IconType;
export declare const PiArrowElbowLeftUpLight: IconType;
export declare const PiArrowElbowRightDownLight: IconType;
export declare const PiArrowElbowRightLight: IconType;
export declare const PiArrowElbowRightUpLight: IconType;
export declare const PiArrowElbowUpLeftLight: IconType;
export declare const PiArrowElbowUpRightLight: IconType;
export declare const PiArrowFatDownLight: IconType;
export declare const PiArrowFatLeftLight: IconType;
export declare const PiArrowFatLineDownLight: IconType;
export declare const PiArrowFatLineLeftLight: IconType;
export declare const PiArrowFatLineRightLight: IconType;
export declare const PiArrowFatLineUpLight: IconType;
export declare const PiArrowFatLinesDownLight: IconType;
export declare const PiArrowFatLinesLeftLight: IconType;
export declare const PiArrowFatLinesRightLight: IconType;
export declare const PiArrowFatLinesUpLight: IconType;
export declare const PiArrowFatRightLight: IconType;
export declare const PiArrowFatUpLight: IconType;
export declare const PiArrowLeftLight: IconType;
export declare const PiArrowLineDownLeftLight: IconType;
export declare const PiArrowLineDownLight: IconType;
export declare const PiArrowLineDownRightLight: IconType;
export declare const PiArrowLineLeftLight: IconType;
export declare const PiArrowLineRightLight: IconType;
export declare const PiArrowLineUpLeftLight: IconType;
export declare const PiArrowLineUpLight: IconType;
export declare const PiArrowLineUpRightLight: IconType;
export declare const PiArrowRightLight: IconType;
export declare const PiArrowSquareDownLeftLight: IconType;
export declare const PiArrowSquareDownLight: IconType;
export declare const PiArrowSquareDownRightLight: IconType;
export declare const PiArrowSquareInLight: IconType;
export declare const PiArrowSquareLeftLight: IconType;
export declare const PiArrowSquareOutLight: IconType;
export declare const PiArrowSquareRightLight: IconType;
export declare const PiArrowSquareUpLeftLight: IconType;
export declare const PiArrowSquareUpLight: IconType;
export declare const PiArrowSquareUpRightLight: IconType;
export declare const PiArrowUDownLeftLight: IconType;
export declare const PiArrowUDownRightLight: IconType;
export declare const PiArrowULeftDownLight: IconType;
export declare const PiArrowULeftUpLight: IconType;
export declare const PiArrowURightDownLight: IconType;
export declare const PiArrowURightUpLight: IconType;
export declare const PiArrowUUpLeftLight: IconType;
export declare const PiArrowUUpRightLight: IconType;
export declare const PiArrowUpLeftLight: IconType;
export declare const PiArrowUpLight: IconType;
export declare const PiArrowUpRightLight: IconType;
export declare const PiArrowsClockwiseLight: IconType;
export declare const PiArrowsCounterClockwiseLight: IconType;
export declare const PiArrowsDownUpLight: IconType;
export declare const PiArrowsHorizontalLight: IconType;
export declare const PiArrowsInCardinalLight: IconType;
export declare const PiArrowsInLight: IconType;
export declare const PiArrowsInLineHorizontalLight: IconType;
export declare const PiArrowsInLineVerticalLight: IconType;
export declare const PiArrowsInSimpleLight: IconType;
export declare const PiArrowsLeftRightLight: IconType;
export declare const PiArrowsMergeLight: IconType;
export declare const PiArrowsOutCardinalLight: IconType;
export declare const PiArrowsOutLight: IconType;
export declare const PiArrowsOutLineHorizontalLight: IconType;
export declare const PiArrowsOutLineVerticalLight: IconType;
export declare const PiArrowsOutSimpleLight: IconType;
export declare const PiArrowsSplitLight: IconType;
export declare const PiArrowsVerticalLight: IconType;
export declare const PiArticleLight: IconType;
export declare const PiArticleMediumLight: IconType;
export declare const PiArticleNyTimesLight: IconType;
export declare const PiAsclepiusLight: IconType;
export declare const PiAsteriskLight: IconType;
export declare const PiAsteriskSimpleLight: IconType;
export declare const PiAtLight: IconType;
export declare const PiAtomLight: IconType;
export declare const PiAvocadoLight: IconType;
export declare const PiAxeLight: IconType;
export declare const PiBabyCarriageLight: IconType;
export declare const PiBabyLight: IconType;
export declare const PiBackpackLight: IconType;
export declare const PiBackspaceLight: IconType;
export declare const PiBagLight: IconType;
export declare const PiBagSimpleLight: IconType;
export declare const PiBalloonLight: IconType;
export declare const PiBandaidsLight: IconType;
export declare const PiBankLight: IconType;
export declare const PiBarbellLight: IconType;
export declare const PiBarcodeLight: IconType;
export declare const PiBarnLight: IconType;
export declare const PiBarricadeLight: IconType;
export declare const PiBaseballCapLight: IconType;
export declare const PiBaseballHelmetLight: IconType;
export declare const PiBaseballLight: IconType;
export declare const PiBasketLight: IconType;
export declare const PiBasketballLight: IconType;
export declare const PiBathtubLight: IconType;
export declare const PiBatteryChargingLight: IconType;
export declare const PiBatteryChargingVerticalLight: IconType;
export declare const PiBatteryEmptyLight: IconType;
export declare const PiBatteryFullLight: IconType;
export declare const PiBatteryHighLight: IconType;
export declare const PiBatteryLowLight: IconType;
export declare const PiBatteryMediumLight: IconType;
export declare const PiBatteryPlusLight: IconType;
export declare const PiBatteryPlusVerticalLight: IconType;
export declare const PiBatteryVerticalEmptyLight: IconType;
export declare const PiBatteryVerticalFullLight: IconType;
export declare const PiBatteryVerticalHighLight: IconType;
export declare const PiBatteryVerticalLowLight: IconType;
export declare const PiBatteryVerticalMediumLight: IconType;
export declare const PiBatteryWarningLight: IconType;
export declare const PiBatteryWarningVerticalLight: IconType;
export declare const PiBeachBallLight: IconType;
export declare const PiBeanieLight: IconType;
export declare const PiBedLight: IconType;
export declare const PiBeerBottleLight: IconType;
export declare const PiBeerSteinLight: IconType;
export declare const PiBehanceLogoLight: IconType;
export declare const PiBellLight: IconType;
export declare const PiBellRingingLight: IconType;
export declare const PiBellSimpleLight: IconType;
export declare const PiBellSimpleRingingLight: IconType;
export declare const PiBellSimpleSlashLight: IconType;
export declare const PiBellSimpleZLight: IconType;
export declare const PiBellSlashLight: IconType;
export declare const PiBellZLight: IconType;
export declare const PiBeltLight: IconType;
export declare const PiBezierCurveLight: IconType;
export declare const PiBicycleLight: IconType;
export declare const PiBinaryLight: IconType;
export declare const PiBinocularsLight: IconType;
export declare const PiBiohazardLight: IconType;
export declare const PiBirdLight: IconType;
export declare const PiBlueprintLight: IconType;
export declare const PiBluetoothConnectedLight: IconType;
export declare const PiBluetoothLight: IconType;
export declare const PiBluetoothSlashLight: IconType;
export declare const PiBluetoothXLight: IconType;
export declare const PiBoatLight: IconType;
export declare const PiBombLight: IconType;
export declare const PiBoneLight: IconType;
export declare const PiBookBookmarkLight: IconType;
export declare const PiBookLight: IconType;
export declare const PiBookOpenLight: IconType;
export declare const PiBookOpenTextLight: IconType;
export declare const PiBookOpenUserLight: IconType;
export declare const PiBookmarkLight: IconType;
export declare const PiBookmarkSimpleLight: IconType;
export declare const PiBookmarksLight: IconType;
export declare const PiBookmarksSimpleLight: IconType;
export declare const PiBooksLight: IconType;
export declare const PiBootLight: IconType;
export declare const PiBoulesLight: IconType;
export declare const PiBoundingBoxLight: IconType;
export declare const PiBowlFoodLight: IconType;
export declare const PiBowlSteamLight: IconType;
export declare const PiBowlingBallLight: IconType;
export declare const PiBoxArrowDownLight: IconType;
export declare const PiBoxArrowUpLight: IconType;
export declare const PiBoxingGloveLight: IconType;
export declare const PiBracketsAngleLight: IconType;
export declare const PiBracketsCurlyLight: IconType;
export declare const PiBracketsRoundLight: IconType;
export declare const PiBracketsSquareLight: IconType;
export declare const PiBrainLight: IconType;
export declare const PiBrandyLight: IconType;
export declare const PiBreadLight: IconType;
export declare const PiBridgeLight: IconType;
export declare const PiBriefcaseLight: IconType;
export declare const PiBriefcaseMetalLight: IconType;
export declare const PiBroadcastLight: IconType;
export declare const PiBroomLight: IconType;
export declare const PiBrowserLight: IconType;
export declare const PiBrowsersLight: IconType;
export declare const PiBugBeetleLight: IconType;
export declare const PiBugDroidLight: IconType;
export declare const PiBugLight: IconType;
export declare const PiBuildingApartmentLight: IconType;
export declare const PiBuildingLight: IconType;
export declare const PiBuildingOfficeLight: IconType;
export declare const PiBuildingsLight: IconType;
export declare const PiBulldozerLight: IconType;
export declare const PiBusLight: IconType;
export declare const PiButterflyLight: IconType;
export declare const PiCableCarLight: IconType;
export declare const PiCactusLight: IconType;
export declare const PiCakeLight: IconType;
export declare const PiCalculatorLight: IconType;
export declare const PiCalendarBlankLight: IconType;
export declare const PiCalendarCheckLight: IconType;
export declare const PiCalendarDotLight: IconType;
export declare const PiCalendarDotsLight: IconType;
export declare const PiCalendarHeartLight: IconType;
export declare const PiCalendarLight: IconType;
export declare const PiCalendarMinusLight: IconType;
export declare const PiCalendarPlusLight: IconType;
export declare const PiCalendarSlashLight: IconType;
export declare const PiCalendarStarLight: IconType;
export declare const PiCalendarXLight: IconType;
export declare const PiCallBellLight: IconType;
export declare const PiCameraLight: IconType;
export declare const PiCameraPlusLight: IconType;
export declare const PiCameraRotateLight: IconType;
export declare const PiCameraSlashLight: IconType;
export declare const PiCampfireLight: IconType;
export declare const PiCarBatteryLight: IconType;
export declare const PiCarLight: IconType;
export declare const PiCarProfileLight: IconType;
export declare const PiCarSimpleLight: IconType;
export declare const PiCardholderLight: IconType;
export declare const PiCardsLight: IconType;
export declare const PiCardsThreeLight: IconType;
export declare const PiCaretCircleDoubleDownLight: IconType;
export declare const PiCaretCircleDoubleLeftLight: IconType;
export declare const PiCaretCircleDoubleRightLight: IconType;
export declare const PiCaretCircleDoubleUpLight: IconType;
export declare const PiCaretCircleDownLight: IconType;
export declare const PiCaretCircleLeftLight: IconType;
export declare const PiCaretCircleRightLight: IconType;
export declare const PiCaretCircleUpDownLight: IconType;
export declare const PiCaretCircleUpLight: IconType;
export declare const PiCaretDoubleDownLight: IconType;
export declare const PiCaretDoubleLeftLight: IconType;
export declare const PiCaretDoubleRightLight: IconType;
export declare const PiCaretDoubleUpLight: IconType;
export declare const PiCaretDownLight: IconType;
export declare const PiCaretLeftLight: IconType;
export declare const PiCaretLineDownLight: IconType;
export declare const PiCaretLineLeftLight: IconType;
export declare const PiCaretLineRightLight: IconType;
export declare const PiCaretLineUpLight: IconType;
export declare const PiCaretRightLight: IconType;
export declare const PiCaretUpDownLight: IconType;
export declare const PiCaretUpLight: IconType;
export declare const PiCarrotLight: IconType;
export declare const PiCashRegisterLight: IconType;
export declare const PiCassetteTapeLight: IconType;
export declare const PiCastleTurretLight: IconType;
export declare const PiCatLight: IconType;
export declare const PiCellSignalFullLight: IconType;
export declare const PiCellSignalHighLight: IconType;
export declare const PiCellSignalLowLight: IconType;
export declare const PiCellSignalMediumLight: IconType;
export declare const PiCellSignalNoneLight: IconType;
export declare const PiCellSignalSlashLight: IconType;
export declare const PiCellSignalXLight: IconType;
export declare const PiCellTowerLight: IconType;
export declare const PiCertificateLight: IconType;
export declare const PiChairLight: IconType;
export declare const PiChalkboardLight: IconType;
export declare const PiChalkboardSimpleLight: IconType;
export declare const PiChalkboardTeacherLight: IconType;
export declare const PiChampagneLight: IconType;
export declare const PiChargingStationLight: IconType;
export declare const PiChartBarHorizontalLight: IconType;
export declare const PiChartBarLight: IconType;
export declare const PiChartDonutLight: IconType;
export declare const PiChartLineDownLight: IconType;
export declare const PiChartLineLight: IconType;
export declare const PiChartLineUpLight: IconType;
export declare const PiChartPieLight: IconType;
export declare const PiChartPieSliceLight: IconType;
export declare const PiChartPolarLight: IconType;
export declare const PiChartScatterLight: IconType;
export declare const PiChatCenteredDotsLight: IconType;
export declare const PiChatCenteredLight: IconType;
export declare const PiChatCenteredSlashLight: IconType;
export declare const PiChatCenteredTextLight: IconType;
export declare const PiChatCircleDotsLight: IconType;
export declare const PiChatCircleLight: IconType;
export declare const PiChatCircleSlashLight: IconType;
export declare const PiChatCircleTextLight: IconType;
export declare const PiChatDotsLight: IconType;
export declare const PiChatLight: IconType;
export declare const PiChatSlashLight: IconType;
export declare const PiChatTeardropDotsLight: IconType;
export declare const PiChatTeardropLight: IconType;
export declare const PiChatTeardropSlashLight: IconType;
export declare const PiChatTeardropTextLight: IconType;
export declare const PiChatTextLight: IconType;
export declare const PiChatsCircleLight: IconType;
export declare const PiChatsLight: IconType;
export declare const PiChatsTeardropLight: IconType;
export declare const PiCheckCircleLight: IconType;
export declare const PiCheckFatLight: IconType;
export declare const PiCheckLight: IconType;
export declare const PiCheckSquareLight: IconType;
export declare const PiCheckSquareOffsetLight: IconType;
export declare const PiCheckerboardLight: IconType;
export declare const PiChecksLight: IconType;
export declare const PiCheersLight: IconType;
export declare const PiCheeseLight: IconType;
export declare const PiChefHatLight: IconType;
export declare const PiCherriesLight: IconType;
export declare const PiChurchLight: IconType;
export declare const PiCigaretteLight: IconType;
export declare const PiCigaretteSlashLight: IconType;
export declare const PiCircleDashedLight: IconType;
export declare const PiCircleHalfLight: IconType;
export declare const PiCircleHalfTiltLight: IconType;
export declare const PiCircleLight: IconType;
export declare const PiCircleNotchLight: IconType;
export declare const PiCirclesFourLight: IconType;
export declare const PiCirclesThreeLight: IconType;
export declare const PiCirclesThreePlusLight: IconType;
export declare const PiCircuitryLight: IconType;
export declare const PiCityLight: IconType;
export declare const PiClipboardLight: IconType;
export declare const PiClipboardTextLight: IconType;
export declare const PiClockAfternoonLight: IconType;
export declare const PiClockClockwiseLight: IconType;
export declare const PiClockCountdownLight: IconType;
export declare const PiClockCounterClockwiseLight: IconType;
export declare const PiClockLight: IconType;
export declare const PiClockUserLight: IconType;
export declare const PiClosedCaptioningLight: IconType;
export declare const PiCloudArrowDownLight: IconType;
export declare const PiCloudArrowUpLight: IconType;
export declare const PiCloudCheckLight: IconType;
export declare const PiCloudFogLight: IconType;
export declare const PiCloudLight: IconType;
export declare const PiCloudLightningLight: IconType;
export declare const PiCloudMoonLight: IconType;
export declare const PiCloudRainLight: IconType;
export declare const PiCloudSlashLight: IconType;
export declare const PiCloudSnowLight: IconType;
export declare const PiCloudSunLight: IconType;
export declare const PiCloudWarningLight: IconType;
export declare const PiCloudXLight: IconType;
export declare const PiCloverLight: IconType;
export declare const PiClubLight: IconType;
export declare const PiCoatHangerLight: IconType;
export declare const PiCodaLogoLight: IconType;
export declare const PiCodeBlockLight: IconType;
export declare const PiCodeLight: IconType;
export declare const PiCodeSimpleLight: IconType;
export declare const PiCodepenLogoLight: IconType;
export declare const PiCodesandboxLogoLight: IconType;
export declare const PiCoffeeBeanLight: IconType;
export declare const PiCoffeeLight: IconType;
export declare const PiCoinLight: IconType;
export declare const PiCoinVerticalLight: IconType;
export declare const PiCoinsLight: IconType;
export declare const PiColumnsLight: IconType;
export declare const PiColumnsPlusLeftLight: IconType;
export declare const PiColumnsPlusRightLight: IconType;
export declare const PiCommandLight: IconType;
export declare const PiCompassLight: IconType;
export declare const PiCompassRoseLight: IconType;
export declare const PiCompassToolLight: IconType;
export declare const PiComputerTowerLight: IconType;
export declare const PiConfettiLight: IconType;
export declare const PiContactlessPaymentLight: IconType;
export declare const PiControlLight: IconType;
export declare const PiCookieLight: IconType;
export declare const PiCookingPotLight: IconType;
export declare const PiCopyLight: IconType;
export declare const PiCopySimpleLight: IconType;
export declare const PiCopyleftLight: IconType;
export declare const PiCopyrightLight: IconType;
export declare const PiCornersInLight: IconType;
export declare const PiCornersOutLight: IconType;
export declare const PiCouchLight: IconType;
export declare const PiCourtBasketballLight: IconType;
export declare const PiCowLight: IconType;
export declare const PiCowboyHatLight: IconType;
export declare const PiCpuLight: IconType;
export declare const PiCraneLight: IconType;
export declare const PiCraneTowerLight: IconType;
export declare const PiCreditCardLight: IconType;
export declare const PiCricketLight: IconType;
export declare const PiCropLight: IconType;
export declare const PiCrossLight: IconType;
export declare const PiCrosshairLight: IconType;
export declare const PiCrosshairSimpleLight: IconType;
export declare const PiCrownCrossLight: IconType;
export declare const PiCrownLight: IconType;
export declare const PiCrownSimpleLight: IconType;
export declare const PiCubeFocusLight: IconType;
export declare const PiCubeLight: IconType;
export declare const PiCubeTransparentLight: IconType;
export declare const PiCurrencyBtcLight: IconType;
export declare const PiCurrencyCircleDollarLight: IconType;
export declare const PiCurrencyCnyLight: IconType;
export declare const PiCurrencyDollarLight: IconType;
export declare const PiCurrencyDollarSimpleLight: IconType;
export declare const PiCurrencyEthLight: IconType;
export declare const PiCurrencyEurLight: IconType;
export declare const PiCurrencyGbpLight: IconType;
export declare const PiCurrencyInrLight: IconType;
export declare const PiCurrencyJpyLight: IconType;
export declare const PiCurrencyKrwLight: IconType;
export declare const PiCurrencyKztLight: IconType;
export declare const PiCurrencyNgnLight: IconType;
export declare const PiCurrencyRubLight: IconType;
export declare const PiCursorClickLight: IconType;
export declare const PiCursorLight: IconType;
export declare const PiCursorTextLight: IconType;
export declare const PiCylinderLight: IconType;
export declare const PiDatabaseLight: IconType;
export declare const PiDeskLight: IconType;
export declare const PiDesktopLight: IconType;
export declare const PiDesktopTowerLight: IconType;
export declare const PiDetectiveLight: IconType;
export declare const PiDevToLogoLight: IconType;
export declare const PiDeviceMobileCameraLight: IconType;
export declare const PiDeviceMobileLight: IconType;
export declare const PiDeviceMobileSlashLight: IconType;
export declare const PiDeviceMobileSpeakerLight: IconType;
export declare const PiDeviceRotateLight: IconType;
export declare const PiDeviceTabletCameraLight: IconType;
export declare const PiDeviceTabletLight: IconType;
export declare const PiDeviceTabletSpeakerLight: IconType;
export declare const PiDevicesLight: IconType;
export declare const PiDiamondLight: IconType;
export declare const PiDiamondsFourLight: IconType;
export declare const PiDiceFiveLight: IconType;
export declare const PiDiceFourLight: IconType;
export declare const PiDiceOneLight: IconType;
export declare const PiDiceSixLight: IconType;
export declare const PiDiceThreeLight: IconType;
export declare const PiDiceTwoLight: IconType;
export declare const PiDiscLight: IconType;
export declare const PiDiscoBallLight: IconType;
export declare const PiDiscordLogoLight: IconType;
export declare const PiDivideLight: IconType;
export declare const PiDnaLight: IconType;
export declare const PiDogLight: IconType;
export declare const PiDoorLight: IconType;
export declare const PiDoorOpenLight: IconType;
export declare const PiDotLight: IconType;
export declare const PiDotOutlineLight: IconType;
export declare const PiDotsNineLight: IconType;
export declare const PiDotsSixLight: IconType;
export declare const PiDotsSixVerticalLight: IconType;
export declare const PiDotsThreeCircleLight: IconType;
export declare const PiDotsThreeCircleVerticalLight: IconType;
export declare const PiDotsThreeLight: IconType;
export declare const PiDotsThreeOutlineLight: IconType;
export declare const PiDotsThreeOutlineVerticalLight: IconType;
export declare const PiDotsThreeVerticalLight: IconType;
export declare const PiDownloadLight: IconType;
export declare const PiDownloadSimpleLight: IconType;
export declare const PiDressLight: IconType;
export declare const PiDresserLight: IconType;
export declare const PiDribbbleLogoLight: IconType;
export declare const PiDroneLight: IconType;
export declare const PiDropHalfBottomLight: IconType;
export declare const PiDropHalfLight: IconType;
export declare const PiDropLight: IconType;
export declare const PiDropSimpleLight: IconType;
export declare const PiDropSlashLight: IconType;
export declare const PiDropboxLogoLight: IconType;
export declare const PiEarLight: IconType;
export declare const PiEarSlashLight: IconType;
export declare const PiEggCrackLight: IconType;
export declare const PiEggLight: IconType;
export declare const PiEjectLight: IconType;
export declare const PiEjectSimpleLight: IconType;
export declare const PiElevatorLight: IconType;
export declare const PiEmptyLight: IconType;
export declare const PiEngineLight: IconType;
export declare const PiEnvelopeLight: IconType;
export declare const PiEnvelopeOpenLight: IconType;
export declare const PiEnvelopeSimpleLight: IconType;
export declare const PiEnvelopeSimpleOpenLight: IconType;
export declare const PiEqualizerLight: IconType;
export declare const PiEqualsLight: IconType;
export declare const PiEraserLight: IconType;
export declare const ********************: IconType;
export declare const PiEscalatorUpLight: IconType;
export declare const PiExamLight: IconType;
export declare const PiExclamationMarkLight: IconType;
export declare const PiExcludeLight: IconType;
export declare const PiExcludeSquareLight: IconType;
export declare const PiExportLight: IconType;
export declare const PiEyeClosedLight: IconType;
export declare const PiEyeLight: IconType;
export declare const PiEyeSlashLight: IconType;
export declare const PiEyedropperLight: IconType;
export declare const PiEyedropperSampleLight: IconType;
export declare const PiEyeglassesLight: IconType;
export declare const PiEyesLight: IconType;
export declare const PiFaceMaskLight: IconType;
export declare const PiFacebookLogoLight: IconType;
export declare const PiFactoryLight: IconType;
export declare const PiFadersHorizontalLight: IconType;
export declare const PiFadersLight: IconType;
export declare const PiFalloutShelterLight: IconType;
export declare const PiFanLight: IconType;
export declare const PiFarmLight: IconType;
export declare const PiFastForwardCircleLight: IconType;
export declare const PiFastForwardLight: IconType;
export declare const PiFeatherLight: IconType;
export declare const PiFediverseLogoLight: IconType;
export declare const PiFigmaLogoLight: IconType;
export declare const PiFileArchiveLight: IconType;
export declare const PiFileArrowDownLight: IconType;
export declare const PiFileArrowUpLight: IconType;
export declare const PiFileAudioLight: IconType;
export declare const PiFileCLight: IconType;
export declare const PiFileCSharpLight: IconType;
export declare const PiFileCloudLight: IconType;
export declare const PiFileCodeLight: IconType;
export declare const PiFileCppLight: IconType;
export declare const PiFileCssLight: IconType;
export declare const PiFileCsvLight: IconType;
export declare const PiFileDashedLight: IconType;
export declare const PiFileDocLight: IconType;
export declare const PiFileHtmlLight: IconType;
export declare const PiFileImageLight: IconType;
export declare const PiFileIniLight: IconType;
export declare const PiFileJpgLight: IconType;
export declare const PiFileJsLight: IconType;
export declare const PiFileJsxLight: IconType;
export declare const PiFileLight: IconType;
export declare const PiFileLockLight: IconType;
export declare const PiFileMagnifyingGlassLight: IconType;
export declare const PiFileMdLight: IconType;
export declare const PiFileMinusLight: IconType;
export declare const PiFilePdfLight: IconType;
export declare const PiFilePlusLight: IconType;
export declare const PiFilePngLight: IconType;
export declare const PiFilePptLight: IconType;
export declare const PiFilePyLight: IconType;
export declare const PiFileRsLight: IconType;
export declare const PiFileSqlLight: IconType;
export declare const PiFileSvgLight: IconType;
export declare const PiFileTextLight: IconType;
export declare const PiFileTsLight: IconType;
export declare const PiFileTsxLight: IconType;
export declare const PiFileTxtLight: IconType;
export declare const PiFileVideoLight: IconType;
export declare const PiFileVueLight: IconType;
export declare const PiFileXLight: IconType;
export declare const PiFileXlsLight: IconType;
export declare const PiFileZipLight: IconType;
export declare const PiFilesLight: IconType;
export declare const PiFilmReelLight: IconType;
export declare const PiFilmScriptLight: IconType;
export declare const PiFilmSlateLight: IconType;
export declare const PiFilmStripLight: IconType;
export declare const PiFingerprintLight: IconType;
export declare const PiFingerprintSimpleLight: IconType;
export declare const PiFinnTheHumanLight: IconType;
export declare const PiFireExtinguisherLight: IconType;
export declare const PiFireLight: IconType;
export declare const PiFireSimpleLight: IconType;
export declare const PiFireTruckLight: IconType;
export declare const PiFirstAidKitLight: IconType;
export declare const PiFirstAidLight: IconType;
export declare const PiFishLight: IconType;
export declare const PiFishSimpleLight: IconType;
export declare const PiFlagBannerFoldLight: IconType;
export declare const PiFlagBannerLight: IconType;
export declare const PiFlagCheckeredLight: IconType;
export declare const PiFlagLight: IconType;
export declare const PiFlagPennantLight: IconType;
export declare const PiFlameLight: IconType;
export declare const PiFlashlightLight: IconType;
export declare const PiFlaskLight: IconType;
export declare const PiFlipHorizontalLight: IconType;
export declare const PiFlipVerticalLight: IconType;
export declare const PiFloppyDiskBackLight: IconType;
export declare const PiFloppyDiskLight: IconType;
export declare const PiFlowArrowLight: IconType;
export declare const PiFlowerLight: IconType;
export declare const PiFlowerLotusLight: IconType;
export declare const PiFlowerTulipLight: IconType;
export declare const PiFlyingSaucerLight: IconType;
export declare const PiFolderDashedLight: IconType;
export declare const PiFolderLight: IconType;
export declare const PiFolderLockLight: IconType;
export declare const PiFolderMinusLight: IconType;
export declare const PiFolderOpenLight: IconType;
export declare const PiFolderPlusLight: IconType;
export declare const PiFolderSimpleDashedLight: IconType;
export declare const PiFolderSimpleLight: IconType;
export declare const PiFolderSimpleLockLight: IconType;
export declare const PiFolderSimpleMinusLight: IconType;
export declare const PiFolderSimplePlusLight: IconType;
export declare const PiFolderSimpleStarLight: IconType;
export declare const PiFolderSimpleUserLight: IconType;
export declare const PiFolderStarLight: IconType;
export declare const PiFolderUserLight: IconType;
export declare const PiFoldersLight: IconType;
export declare const PiFootballHelmetLight: IconType;
export declare const PiFootballLight: IconType;
export declare const PiFootprintsLight: IconType;
export declare const PiForkKnifeLight: IconType;
export declare const PiFourKLight: IconType;
export declare const PiFrameCornersLight: IconType;
export declare const PiFramerLogoLight: IconType;
export declare const PiFunctionLight: IconType;
export declare const PiFunnelLight: IconType;
export declare const PiFunnelSimpleLight: IconType;
export declare const PiFunnelSimpleXLight: IconType;
export declare const PiFunnelXLight: IconType;
export declare const PiGameControllerLight: IconType;
export declare const PiGarageLight: IconType;
export declare const PiGasCanLight: IconType;
export declare const PiGasPumpLight: IconType;
export declare const PiGaugeLight: IconType;
export declare const PiGavelLight: IconType;
export declare const PiGearFineLight: IconType;
export declare const PiGearLight: IconType;
export declare const PiGearSixLight: IconType;
export declare const PiGenderFemaleLight: IconType;
export declare const PiGenderIntersexLight: IconType;
export declare const PiGenderMaleLight: IconType;
export declare const PiGenderNeuterLight: IconType;
export declare const PiGenderNonbinaryLight: IconType;
export declare const PiGenderTransgenderLight: IconType;
export declare const PiGhostLight: IconType;
export declare const PiGifLight: IconType;
export declare const PiGiftLight: IconType;
export declare const PiGitBranchLight: IconType;
export declare const PiGitCommitLight: IconType;
export declare const PiGitDiffLight: IconType;
export declare const PiGitForkLight: IconType;
export declare const PiGitMergeLight: IconType;
export declare const PiGitPullRequestLight: IconType;
export declare const PiGithubLogoLight: IconType;
export declare const PiGitlabLogoLight: IconType;
export declare const PiGitlabLogoSimpleLight: IconType;
export declare const PiGlobeHemisphereEastLight: IconType;
export declare const PiGlobeHemisphereWestLight: IconType;
export declare const PiGlobeLight: IconType;
export declare const PiGlobeSimpleLight: IconType;
export declare const PiGlobeSimpleXLight: IconType;
export declare const PiGlobeStandLight: IconType;
export declare const PiGlobeXLight: IconType;
export declare const PiGogglesLight: IconType;
export declare const PiGolfLight: IconType;
export declare const PiGoodreadsLogoLight: IconType;
export declare const PiGoogleCardboardLogoLight: IconType;
export declare const PiGoogleChromeLogoLight: IconType;
export declare const PiGoogleDriveLogoLight: IconType;
export declare const PiGoogleLogoLight: IconType;
export declare const PiGooglePhotosLogoLight: IconType;
export declare const PiGooglePlayLogoLight: IconType;
export declare const PiGooglePodcastsLogoLight: IconType;
export declare const PiGpsFixLight: IconType;
export declare const PiGpsLight: IconType;
export declare const PiGpsSlashLight: IconType;
export declare const PiGradientLight: IconType;
export declare const PiGraduationCapLight: IconType;
export declare const PiGrainsLight: IconType;
export declare const PiGrainsSlashLight: IconType;
export declare const PiGraphLight: IconType;
export declare const PiGraphicsCardLight: IconType;
export declare const PiGreaterThanLight: IconType;
export declare const PiGreaterThanOrEqualLight: IconType;
export declare const PiGridFourLight: IconType;
export declare const PiGridNineLight: IconType;
export declare const PiGuitarLight: IconType;
export declare const PiHairDryerLight: IconType;
export declare const PiHamburgerLight: IconType;
export declare const PiHammerLight: IconType;
export declare const PiHandArrowDownLight: IconType;
export declare const PiHandArrowUpLight: IconType;
export declare const PiHandCoinsLight: IconType;
export declare const PiHandDepositLight: IconType;
export declare const PiHandEyeLight: IconType;
export declare const PiHandFistLight: IconType;
export declare const PiHandGrabbingLight: IconType;
export declare const PiHandHeartLight: IconType;
export declare const PiHandLight: IconType;
export declare const PiHandPalmLight: IconType;
export declare const PiHandPeaceLight: IconType;
export declare const PiHandPointingLight: IconType;
export declare const PiHandSoapLight: IconType;
export declare const PiHandSwipeLeftLight: IconType;
export declare const PiHandSwipeRightLight: IconType;
export declare const PiHandTapLight: IconType;
export declare const PiHandWavingLight: IconType;
export declare const PiHandWithdrawLight: IconType;
export declare const PiHandbagLight: IconType;
export declare const PiHandbagSimpleLight: IconType;
export declare const PiHandsClappingLight: IconType;
export declare const PiHandsPrayingLight: IconType;
export declare const PiHandshakeLight: IconType;
export declare const PiHardDriveLight: IconType;
export declare const PiHardDrivesLight: IconType;
export declare const PiHardHatLight: IconType;
export declare const PiHashLight: IconType;
export declare const PiHashStraightLight: IconType;
export declare const PiHeadCircuitLight: IconType;
export declare const PiHeadlightsLight: IconType;
export declare const PiHeadphonesLight: IconType;
export declare const PiHeadsetLight: IconType;
export declare const PiHeartBreakLight: IconType;
export declare const PiHeartHalfLight: IconType;
export declare const PiHeartLight: IconType;
export declare const PiHeartStraightBreakLight: IconType;
export declare const PiHeartStraightLight: IconType;
export declare const PiHeartbeatLight: IconType;
export declare const PiHexagonLight: IconType;
export declare const PiHighDefinitionLight: IconType;
export declare const PiHighHeelLight: IconType;
export declare const PiHighlighterCircleLight: IconType;
export declare const PiHighlighterLight: IconType;
export declare const PiHockeyLight: IconType;
export declare const PiHoodieLight: IconType;
export declare const PiHorseLight: IconType;
export declare const PiHospitalLight: IconType;
export declare const PiHourglassHighLight: IconType;
export declare const PiHourglassLight: IconType;
export declare const PiHourglassLowLight: IconType;
export declare const PiHourglassMediumLight: IconType;
export declare const PiHourglassSimpleHighLight: IconType;
export declare const PiHourglassSimpleLight: IconType;
export declare const PiHourglassSimpleLowLight: IconType;
export declare const PiHourglassSimpleMediumLight: IconType;
export declare const PiHouseLight: IconType;
export declare const PiHouseLineLight: IconType;
export declare const PiHouseSimpleLight: IconType;
export declare const PiHurricaneLight: IconType;
export declare const PiIceCreamLight: IconType;
export declare const PiIdentificationBadgeLight: IconType;
export declare const PiIdentificationCardLight: IconType;
export declare const PiImageBrokenLight: IconType;
export declare const PiImageLight: IconType;
export declare const PiImageSquareLight: IconType;
export declare const PiImagesLight: IconType;
export declare const PiImagesSquareLight: IconType;
export declare const PiInfinityLight: IconType;
export declare const PiInfoLight: IconType;
export declare const PiInstagramLogoLight: IconType;
export declare const PiIntersectLight: IconType;
export declare const PiIntersectSquareLight: IconType;
export declare const PiIntersectThreeLight: IconType;
export declare const PiIntersectionLight: IconType;
export declare const PiInvoiceLight: IconType;
export declare const PiIslandLight: IconType;
export declare const PiJarLabelLight: IconType;
export declare const PiJarLight: IconType;
export declare const PiJeepLight: IconType;
export declare const PiJoystickLight: IconType;
export declare const PiKanbanLight: IconType;
export declare const PiKeyLight: IconType;
export declare const PiKeyReturnLight: IconType;
export declare const PiKeyboardLight: IconType;
export declare const PiKeyholeLight: IconType;
export declare const PiKnifeLight: IconType;
export declare const PiLadderLight: IconType;
export declare const PiLadderSimpleLight: IconType;
export declare const PiLampLight: IconType;
export declare const PiLampPendantLight: IconType;
export declare const PiLaptopLight: IconType;
export declare const PiLassoLight: IconType;
export declare const PiLastfmLogoLight: IconType;
export declare const PiLayoutLight: IconType;
export declare const PiLeafLight: IconType;
export declare const PiLecternLight: IconType;
export declare const PiLegoLight: IconType;
export declare const PiLegoSmileyLight: IconType;
export declare const PiLessThanLight: IconType;
export declare const PiLessThanOrEqualLight: IconType;
export declare const PiLetterCircleHLight: IconType;
export declare const PiLetterCirclePLight: IconType;
export declare const PiLetterCircleVLight: IconType;
export declare const PiLifebuoyLight: IconType;
export declare const PiLightbulbFilamentLight: IconType;
export declare const PiLightbulbLight: IconType;
export declare const PiLighthouseLight: IconType;
export declare const PiLightningALight: IconType;
export declare const PiLightningLight: IconType;
export declare const PiLightningSlashLight: IconType;
export declare const PiLineSegmentLight: IconType;
export declare const PiLineSegmentsLight: IconType;
export declare const PiLineVerticalLight: IconType;
export declare const PiLinkBreakLight: IconType;
export declare const PiLinkLight: IconType;
export declare const PiLinkSimpleBreakLight: IconType;
export declare const PiLinkSimpleHorizontalBreakLight: IconType;
export declare const PiLinkSimpleHorizontalLight: IconType;
export declare const PiLinkSimpleLight: IconType;
export declare const PiLinkedinLogoLight: IconType;
export declare const PiLinktreeLogoLight: IconType;
export declare const PiLinuxLogoLight: IconType;
export declare const PiListBulletsLight: IconType;
export declare const PiListChecksLight: IconType;
export declare const PiListDashesLight: IconType;
export declare const PiListHeartLight: IconType;
export declare const PiListLight: IconType;
export declare const PiListMagnifyingGlassLight: IconType;
export declare const PiListNumbersLight: IconType;
export declare const PiListPlusLight: IconType;
export declare const PiListStarLight: IconType;
export declare const PiLockKeyLight: IconType;
export declare const PiLockKeyOpenLight: IconType;
export declare const PiLockLaminatedLight: IconType;
export declare const PiLockLaminatedOpenLight: IconType;
export declare const PiLockLight: IconType;
export declare const PiLockOpenLight: IconType;
export declare const PiLockSimpleLight: IconType;
export declare const PiLockSimpleOpenLight: IconType;
export declare const PiLockersLight: IconType;
export declare const PiLogLight: IconType;
export declare const PiMagicWandLight: IconType;
export declare const PiMagnetLight: IconType;
export declare const PiMagnetStraightLight: IconType;
export declare const PiMagnifyingGlassLight: IconType;
export declare const PiMagnifyingGlassMinusLight: IconType;
export declare const PiMagnifyingGlassPlusLight: IconType;
export declare const PiMailboxLight: IconType;
export declare const PiMapPinAreaLight: IconType;
export declare const PiMapPinLight: IconType;
export declare const PiMapPinLineLight: IconType;
export declare const PiMapPinPlusLight: IconType;
export declare const PiMapPinSimpleAreaLight: IconType;
export declare const PiMapPinSimpleLight: IconType;
export declare const PiMapPinSimpleLineLight: IconType;
export declare const PiMapTrifoldLight: IconType;
export declare const PiMarkdownLogoLight: IconType;
export declare const PiMarkerCircleLight: IconType;
export declare const PiMartiniLight: IconType;
export declare const PiMaskHappyLight: IconType;
export declare const PiMaskSadLight: IconType;
export declare const PiMastodonLogoLight: IconType;
export declare const PiMathOperationsLight: IconType;
export declare const PiMatrixLogoLight: IconType;
export declare const PiMedalLight: IconType;
export declare const PiMedalMilitaryLight: IconType;
export declare const PiMediumLogoLight: IconType;
export declare const PiMegaphoneLight: IconType;
export declare const PiMegaphoneSimpleLight: IconType;
export declare const PiMemberOfLight: IconType;
export declare const PiMemoryLight: IconType;
export declare const PiMessengerLogoLight: IconType;
export declare const PiMetaLogoLight: IconType;
export declare const PiMeteorLight: IconType;
export declare const PiMetronomeLight: IconType;
export declare const PiMicrophoneLight: IconType;
export declare const PiMicrophoneSlashLight: IconType;
export declare const PiMicrophoneStageLight: IconType;
export declare const PiMicroscopeLight: IconType;
export declare const PiMicrosoftExcelLogoLight: IconType;
export declare const PiMicrosoftOutlookLogoLight: IconType;
export declare const PiMicrosoftPowerpointLogoLight: IconType;
export declare const PiMicrosoftTeamsLogoLight: IconType;
export declare const PiMicrosoftWordLogoLight: IconType;
export declare const PiMinusCircleLight: IconType;
export declare const PiMinusLight: IconType;
export declare const PiMinusSquareLight: IconType;
export declare const PiMoneyLight: IconType;
export declare const PiMoneyWavyLight: IconType;
export declare const PiMonitorArrowUpLight: IconType;
export declare const PiMonitorLight: IconType;
export declare const PiMonitorPlayLight: IconType;
export declare const PiMoonLight: IconType;
export declare const PiMoonStarsLight: IconType;
export declare const PiMopedFrontLight: IconType;
export declare const PiMopedLight: IconType;
export declare const PiMosqueLight: IconType;
export declare const PiMotorcycleLight: IconType;
export declare const PiMountainsLight: IconType;
export declare const PiMouseLeftClickLight: IconType;
export declare const PiMouseLight: IconType;
export declare const PiMouseMiddleClickLight: IconType;
export declare const PiMouseRightClickLight: IconType;
export declare const PiMouseScrollLight: IconType;
export declare const PiMouseSimpleLight: IconType;
export declare const PiMusicNoteLight: IconType;
export declare const PiMusicNoteSimpleLight: IconType;
export declare const PiMusicNotesLight: IconType;
export declare const PiMusicNotesMinusLight: IconType;
export declare const PiMusicNotesPlusLight: IconType;
export declare const PiMusicNotesSimpleLight: IconType;
export declare const PiNavigationArrowLight: IconType;
export declare const PiNeedleLight: IconType;
export declare const PiNetworkLight: IconType;
export declare const PiNetworkSlashLight: IconType;
export declare const PiNetworkXLight: IconType;
export declare const PiNewspaperClippingLight: IconType;
export declare const PiNewspaperLight: IconType;
export declare const PiNotEqualsLight: IconType;
export declare const PiNotMemberOfLight: IconType;
export declare const PiNotSubsetOfLight: IconType;
export declare const PiNotSupersetOfLight: IconType;
export declare const PiNotchesLight: IconType;
export declare const PiNoteBlankLight: IconType;
export declare const PiNoteLight: IconType;
export declare const PiNotePencilLight: IconType;
export declare const PiNotebookLight: IconType;
export declare const PiNotepadLight: IconType;
export declare const PiNotificationLight: IconType;
export declare const PiNotionLogoLight: IconType;
export declare const PiNuclearPlantLight: IconType;
export declare const PiNumberCircleEightLight: IconType;
export declare const PiNumberCircleFiveLight: IconType;
export declare const PiNumberCircleFourLight: IconType;
export declare const PiNumberCircleNineLight: IconType;
export declare const PiNumberCircleOneLight: IconType;
export declare const PiNumberCircleSevenLight: IconType;
export declare const PiNumberCircleSixLight: IconType;
export declare const PiNumberCircleThreeLight: IconType;
export declare const PiNumberCircleTwoLight: IconType;
export declare const PiNumberCircleZeroLight: IconType;
export declare const PiNumberEightLight: IconType;
export declare const PiNumberFiveLight: IconType;
export declare const PiNumberFourLight: IconType;
export declare const PiNumberNineLight: IconType;
export declare const PiNumberOneLight: IconType;
export declare const PiNumberSevenLight: IconType;
export declare const PiNumberSixLight: IconType;
export declare const PiNumberSquareEightLight: IconType;
export declare const PiNumberSquareFiveLight: IconType;
export declare const PiNumberSquareFourLight: IconType;
export declare const PiNumberSquareNineLight: IconType;
export declare const PiNumberSquareOneLight: IconType;
export declare const PiNumberSquareSevenLight: IconType;
export declare const PiNumberSquareSixLight: IconType;
export declare const PiNumberSquareThreeLight: IconType;
export declare const PiNumberSquareTwoLight: IconType;
export declare const PiNumberSquareZeroLight: IconType;
export declare const PiNumberThreeLight: IconType;
export declare const PiNumberTwoLight: IconType;
export declare const PiNumberZeroLight: IconType;
export declare const PiNumpadLight: IconType;
export declare const PiNutLight: IconType;
export declare const PiNyTimesLogoLight: IconType;
export declare const PiOctagonLight: IconType;
export declare const PiOfficeChairLight: IconType;
export declare const PiOnigiriLight: IconType;
export declare const PiOpenAiLogoLight: IconType;
export declare const PiOptionLight: IconType;
export declare const PiOrangeLight: IconType;
export declare const PiOrangeSliceLight: IconType;
export declare const PiOvenLight: IconType;
export declare const PiPackageLight: IconType;
export declare const PiPaintBrushBroadLight: IconType;
export declare const PiPaintBrushHouseholdLight: IconType;
export declare const PiPaintBrushLight: IconType;
export declare const PiPaintBucketLight: IconType;
export declare const PiPaintRollerLight: IconType;
export declare const PiPaletteLight: IconType;
export declare const PiPanoramaLight: IconType;
export declare const PiPantsLight: IconType;
export declare const PiPaperPlaneLight: IconType;
export declare const PiPaperPlaneRightLight: IconType;
export declare const PiPaperPlaneTiltLight: IconType;
export declare const PiPaperclipHorizontalLight: IconType;
export declare const PiPaperclipLight: IconType;
export declare const PiParachuteLight: IconType;
export declare const PiParagraphLight: IconType;
export declare const PiParallelogramLight: IconType;
export declare const PiParkLight: IconType;
export declare const PiPasswordLight: IconType;
export declare const PiPathLight: IconType;
export declare const PiPatreonLogoLight: IconType;
export declare const PiPauseCircleLight: IconType;
export declare const PiPauseLight: IconType;
export declare const PiPawPrintLight: IconType;
export declare const PiPaypalLogoLight: IconType;
export declare const PiPeaceLight: IconType;
export declare const PiPenLight: IconType;
export declare const PiPenNibLight: IconType;
export declare const PiPenNibStraightLight: IconType;
export declare const PiPencilCircleLight: IconType;
export declare const PiPencilLight: IconType;
export declare const PiPencilLineLight: IconType;
export declare const PiPencilRulerLight: IconType;
export declare const PiPencilSimpleLight: IconType;
export declare const PiPencilSimpleLineLight: IconType;
export declare const PiPencilSimpleSlashLight: IconType;
export declare const PiPencilSlashLight: IconType;
export declare const PiPentagonLight: IconType;
export declare const PiPentagramLight: IconType;
export declare const PiPepperLight: IconType;
export declare const PiPercentLight: IconType;
export declare const PiPersonArmsSpreadLight: IconType;
export declare const PiPersonLight: IconType;
export declare const PiPersonSimpleBikeLight: IconType;
export declare const PiPersonSimpleCircleLight: IconType;
export declare const PiPersonSimpleHikeLight: IconType;
export declare const PiPersonSimpleLight: IconType;
export declare const PiPersonSimpleRunLight: IconType;
export declare const PiPersonSimpleSkiLight: IconType;
export declare const PiPersonSimpleSnowboardLight: IconType;
export declare const PiPersonSimpleSwimLight: IconType;
export declare const PiPersonSimpleTaiChiLight: IconType;
export declare const PiPersonSimpleThrowLight: IconType;
export declare const PiPersonSimpleWalkLight: IconType;
export declare const PiPerspectiveLight: IconType;
export declare const PiPhoneCallLight: IconType;
export declare const PiPhoneDisconnectLight: IconType;
export declare const PiPhoneIncomingLight: IconType;
export declare const PiPhoneLight: IconType;
export declare const PiPhoneListLight: IconType;
export declare const PiPhoneOutgoingLight: IconType;
export declare const PiPhonePauseLight: IconType;
export declare const PiPhonePlusLight: IconType;
export declare const PiPhoneSlashLight: IconType;
export declare const PiPhoneTransferLight: IconType;
export declare const PiPhoneXLight: IconType;
export declare const PiPhosphorLogoLight: IconType;
export declare const PiPiLight: IconType;
export declare const PiPianoKeysLight: IconType;
export declare const PiPicnicTableLight: IconType;
export declare const PiPictureInPictureLight: IconType;
export declare const PiPiggyBankLight: IconType;
export declare const PiPillLight: IconType;
export declare const PiPingPongLight: IconType;
export declare const PiPintGlassLight: IconType;
export declare const PiPinterestLogoLight: IconType;
export declare const PiPinwheelLight: IconType;
export declare const PiPipeLight: IconType;
export declare const PiPipeWrenchLight: IconType;
export declare const PiPixLogoLight: IconType;
export declare const PiPizzaLight: IconType;
export declare const PiPlaceholderLight: IconType;
export declare const PiPlanetLight: IconType;
export declare const PiPlantLight: IconType;
export declare const PiPlayCircleLight: IconType;
export declare const PiPlayLight: IconType;
export declare const PiPlayPauseLight: IconType;
export declare const PiPlaylistLight: IconType;
export declare const PiPlugChargingLight: IconType;
export declare const PiPlugLight: IconType;
export declare const PiPlugsConnectedLight: IconType;
export declare const PiPlugsLight: IconType;
export declare const PiPlusCircleLight: IconType;
export declare const PiPlusLight: IconType;
export declare const PiPlusMinusLight: IconType;
export declare const PiPlusSquareLight: IconType;
export declare const PiPokerChipLight: IconType;
export declare const PiPoliceCarLight: IconType;
export declare const PiPolygonLight: IconType;
export declare const PiPopcornLight: IconType;
export declare const PiPopsicleLight: IconType;
export declare const PiPottedPlantLight: IconType;
export declare const PiPowerLight: IconType;
export declare const PiPrescriptionLight: IconType;
export declare const PiPresentationChartLight: IconType;
export declare const PiPresentationLight: IconType;
export declare const PiPrinterLight: IconType;
export declare const PiProhibitInsetLight: IconType;
export declare const PiProhibitLight: IconType;
export declare const PiProjectorScreenChartLight: IconType;
export declare const PiProjectorScreenLight: IconType;
export declare const PiPulseLight: IconType;
export declare const PiPushPinLight: IconType;
export declare const PiPushPinSimpleLight: IconType;
export declare const PiPushPinSimpleSlashLight: IconType;
export declare const PiPushPinSlashLight: IconType;
export declare const PiPuzzlePieceLight: IconType;
export declare const PiQrCodeLight: IconType;
export declare const PiQuestionLight: IconType;
export declare const PiQuestionMarkLight: IconType;
export declare const PiQueueLight: IconType;
export declare const PiQuotesLight: IconType;
export declare const PiRabbitLight: IconType;
export declare const PiRacquetLight: IconType;
export declare const PiRadicalLight: IconType;
export declare const PiRadioButtonLight: IconType;
export declare const PiRadioLight: IconType;
export declare const PiRadioactiveLight: IconType;
export declare const PiRainbowCloudLight: IconType;
export declare const PiRainbowLight: IconType;
export declare const PiRankingLight: IconType;
export declare const PiReadCvLogoLight: IconType;
export declare const PiReceiptLight: IconType;
export declare const PiReceiptXLight: IconType;
export declare const PiRecordLight: IconType;
export declare const PiRectangleDashedLight: IconType;
export declare const PiRectangleLight: IconType;
export declare const PiRecycleLight: IconType;
export declare const PiRedditLogoLight: IconType;
export declare const PiRepeatLight: IconType;
export declare const PiRepeatOnceLight: IconType;
export declare const PiReplitLogoLight: IconType;
export declare const PiResizeLight: IconType;
export declare const PiRewindCircleLight: IconType;
export declare const PiRewindLight: IconType;
export declare const PiRoadHorizonLight: IconType;
export declare const PiRobotLight: IconType;
export declare const PiRocketLaunchLight: IconType;
export declare const PiRocketLight: IconType;
export declare const PiRowsLight: IconType;
export declare const PiRowsPlusBottomLight: IconType;
export declare const PiRowsPlusTopLight: IconType;
export declare const PiRssLight: IconType;
export declare const PiRssSimpleLight: IconType;
export declare const PiRugLight: IconType;
export declare const PiRulerLight: IconType;
export declare const PiSailboatLight: IconType;
export declare const PiScalesLight: IconType;
export declare const PiScanLight: IconType;
export declare const PiScanSmileyLight: IconType;
export declare const PiScissorsLight: IconType;
export declare const PiScooterLight: IconType;
export declare const PiScreencastLight: IconType;
export declare const PiScrewdriverLight: IconType;
export declare const PiScribbleLight: IconType;
export declare const PiScribbleLoopLight: IconType;
export declare const PiScrollLight: IconType;
export declare const PiSealCheckLight: IconType;
export declare const PiSealLight: IconType;
export declare const PiSealPercentLight: IconType;
export declare const PiSealQuestionLight: IconType;
export declare const PiSealWarningLight: IconType;
export declare const PiSeatLight: IconType;
export declare const PiSeatbeltLight: IconType;
export declare const PiSecurityCameraLight: IconType;
export declare const PiSelectionAllLight: IconType;
export declare const PiSelectionBackgroundLight: IconType;
export declare const PiSelectionForegroundLight: IconType;
export declare const PiSelectionInverseLight: IconType;
export declare const PiSelectionLight: IconType;
export declare const PiSelectionPlusLight: IconType;
export declare const PiSelectionSlashLight: IconType;
export declare const PiShapesLight: IconType;
export declare const PiShareFatLight: IconType;
export declare const PiShareLight: IconType;
export declare const PiShareNetworkLight: IconType;
export declare const PiShieldCheckLight: IconType;
export declare const PiShieldCheckeredLight: IconType;
export declare const PiShieldChevronLight: IconType;
export declare const PiShieldLight: IconType;
export declare const PiShieldPlusLight: IconType;
export declare const PiShieldSlashLight: IconType;
export declare const PiShieldStarLight: IconType;
export declare const PiShieldWarningLight: IconType;
export declare const PiShippingContainerLight: IconType;
export declare const PiShirtFoldedLight: IconType;
export declare const PiShootingStarLight: IconType;
export declare const PiShoppingBagLight: IconType;
export declare const PiShoppingBagOpenLight: IconType;
export declare const PiShoppingCartLight: IconType;
export declare const PiShoppingCartSimpleLight: IconType;
export declare const PiShovelLight: IconType;
export declare const PiShowerLight: IconType;
export declare const PiShrimpLight: IconType;
export declare const PiShuffleAngularLight: IconType;
export declare const PiShuffleLight: IconType;
export declare const PiShuffleSimpleLight: IconType;
export declare const PiSidebarLight: IconType;
export declare const PiSidebarSimpleLight: IconType;
export declare const PiSigmaLight: IconType;
export declare const PiSignInLight: IconType;
export declare const PiSignOutLight: IconType;
export declare const PiSignatureLight: IconType;
export declare const PiSignpostLight: IconType;
export declare const PiSimCardLight: IconType;
export declare const PiSirenLight: IconType;
export declare const PiSketchLogoLight: IconType;
export declare const PiSkipBackCircleLight: IconType;
export declare const PiSkipBackLight: IconType;
export declare const PiSkipForwardCircleLight: IconType;
export declare const PiSkipForwardLight: IconType;
export declare const PiSkullLight: IconType;
export declare const PiSkypeLogoLight: IconType;
export declare const PiSlackLogoLight: IconType;
export declare const PiSlidersHorizontalLight: IconType;
export declare const PiSlidersLight: IconType;
export declare const PiSlideshowLight: IconType;
export declare const PiSmileyAngryLight: IconType;
export declare const PiSmileyBlankLight: IconType;
export declare const PiSmileyLight: IconType;
export declare const PiSmileyMehLight: IconType;
export declare const PiSmileyMeltingLight: IconType;
export declare const PiSmileyNervousLight: IconType;
export declare const PiSmileySadLight: IconType;
export declare const PiSmileyStickerLight: IconType;
export declare const PiSmileyWinkLight: IconType;
export declare const PiSmileyXEyesLight: IconType;
export declare const PiSnapchatLogoLight: IconType;
export declare const PiSneakerLight: IconType;
export declare const PiSneakerMoveLight: IconType;
export declare const PiSnowflakeLight: IconType;
export declare const PiSoccerBallLight: IconType;
export declare const PiSockLight: IconType;
export declare const PiSolarPanelLight: IconType;
export declare const PiSolarRoofLight: IconType;
export declare const PiSortAscendingLight: IconType;
export declare const PiSortDescendingLight: IconType;
export declare const PiSoundcloudLogoLight: IconType;
export declare const PiSpadeLight: IconType;
export declare const PiSparkleLight: IconType;
export declare const PiSpeakerHifiLight: IconType;
export declare const PiSpeakerHighLight: IconType;
export declare const PiSpeakerLowLight: IconType;
export declare const PiSpeakerNoneLight: IconType;
export declare const PiSpeakerSimpleHighLight: IconType;
export declare const PiSpeakerSimpleLowLight: IconType;
export declare const PiSpeakerSimpleNoneLight: IconType;
export declare const PiSpeakerSimpleSlashLight: IconType;
export declare const PiSpeakerSimpleXLight: IconType;
export declare const PiSpeakerSlashLight: IconType;
export declare const PiSpeakerXLight: IconType;
export declare const PiSpeedometerLight: IconType;
export declare const PiSphereLight: IconType;
export declare const PiSpinnerBallLight: IconType;
export declare const PiSpinnerGapLight: IconType;
export declare const PiSpinnerLight: IconType;
export declare const PiSpiralLight: IconType;
export declare const PiSplitHorizontalLight: IconType;
export declare const PiSplitVerticalLight: IconType;
export declare const PiSpotifyLogoLight: IconType;
export declare const PiSprayBottleLight: IconType;
export declare const PiSquareHalfBottomLight: IconType;
export declare const PiSquareHalfLight: IconType;
export declare const PiSquareLight: IconType;
export declare const PiSquareLogoLight: IconType;
export declare const PiSquareSplitHorizontalLight: IconType;
export declare const PiSquareSplitVerticalLight: IconType;
export declare const PiSquaresFourLight: IconType;
export declare const PiStackLight: IconType;
export declare const PiStackMinusLight: IconType;
export declare const PiStackOverflowLogoLight: IconType;
export declare const PiStackPlusLight: IconType;
export declare const PiStackSimpleLight: IconType;
export declare const PiStairsLight: IconType;
export declare const PiStampLight: IconType;
export declare const PiStandardDefinitionLight: IconType;
export declare const PiStarAndCrescentLight: IconType;
export declare const PiStarFourLight: IconType;
export declare const PiStarHalfLight: IconType;
export declare const PiStarLight: IconType;
export declare const PiStarOfDavidLight: IconType;
export declare const PiSteamLogoLight: IconType;
export declare const PiSteeringWheelLight: IconType;
export declare const PiStepsLight: IconType;
export declare const PiStethoscopeLight: IconType;
export declare const PiStickerLight: IconType;
export declare const PiStoolLight: IconType;
export declare const PiStopCircleLight: IconType;
export declare const PiStopLight: IconType;
export declare const PiStorefrontLight: IconType;
export declare const PiStrategyLight: IconType;
export declare const PiStripeLogoLight: IconType;
export declare const PiStudentLight: IconType;
export declare const PiSubsetOfLight: IconType;
export declare const PiSubsetProperOfLight: IconType;
export declare const PiSubtitlesLight: IconType;
export declare const PiSubtitlesSlashLight: IconType;
export declare const PiSubtractLight: IconType;
export declare const PiSubtractSquareLight: IconType;
export declare const PiSubwayLight: IconType;
export declare const PiSuitcaseLight: IconType;
export declare const PiSuitcaseRollingLight: IconType;
export declare const PiSuitcaseSimpleLight: IconType;
export declare const PiSunDimLight: IconType;
export declare const PiSunHorizonLight: IconType;
export declare const PiSunLight: IconType;
export declare const PiSunglassesLight: IconType;
export declare const PiSupersetOfLight: IconType;
export declare const PiSupersetProperOfLight: IconType;
export declare const PiSwapLight: IconType;
export declare const PiSwatchesLight: IconType;
export declare const PiSwimmingPoolLight: IconType;
export declare const PiSwordLight: IconType;
export declare const PiSynagogueLight: IconType;
export declare const PiSyringeLight: IconType;
export declare const PiTShirtLight: IconType;
export declare const PiTableLight: IconType;
export declare const PiTabsLight: IconType;
export declare const PiTagChevronLight: IconType;
export declare const PiTagLight: IconType;
export declare const PiTagSimpleLight: IconType;
export declare const PiTargetLight: IconType;
export declare const PiTaxiLight: IconType;
export declare const PiTeaBagLight: IconType;
export declare const PiTelegramLogoLight: IconType;
export declare const PiTelevisionLight: IconType;
export declare const PiTelevisionSimpleLight: IconType;
export declare const PiTennisBallLight: IconType;
export declare const PiTentLight: IconType;
export declare const PiTerminalLight: IconType;
export declare const PiTerminalWindowLight: IconType;
export declare const PiTestTubeLight: IconType;
export declare const PiTextAUnderlineLight: IconType;
export declare const PiTextAaLight: IconType;
export declare const PiTextAlignCenterLight: IconType;
export declare const PiTextAlignJustifyLight: IconType;
export declare const PiTextAlignLeftLight: IconType;
export declare const PiTextAlignRightLight: IconType;
export declare const PiTextBLight: IconType;
export declare const PiTextColumnsLight: IconType;
export declare const PiTextHFiveLight: IconType;
export declare const PiTextHFourLight: IconType;
export declare const PiTextHLight: IconType;
export declare const PiTextHOneLight: IconType;
export declare const PiTextHSixLight: IconType;
export declare const PiTextHThreeLight: IconType;
export declare const PiTextHTwoLight: IconType;
export declare const PiTextIndentLight: IconType;
export declare const PiTextItalicLight: IconType;
export declare const PiTextOutdentLight: IconType;
export declare const PiTextStrikethroughLight: IconType;
export declare const PiTextSubscriptLight: IconType;
export declare const PiTextSuperscriptLight: IconType;
export declare const PiTextTLight: IconType;
export declare const PiTextTSlashLight: IconType;
export declare const PiTextUnderlineLight: IconType;
export declare const PiTextboxLight: IconType;
export declare const PiThermometerColdLight: IconType;
export declare const PiThermometerHotLight: IconType;
export declare const PiThermometerLight: IconType;
export declare const PiThermometerSimpleLight: IconType;
export declare const PiThreadsLogoLight: IconType;
export declare const PiThreeDLight: IconType;
export declare const PiThumbsDownLight: IconType;
export declare const PiThumbsUpLight: IconType;
export declare const PiTicketLight: IconType;
export declare const PiTidalLogoLight: IconType;
export declare const PiTiktokLogoLight: IconType;
export declare const PiTildeLight: IconType;
export declare const PiTimerLight: IconType;
export declare const PiTipJarLight: IconType;
export declare const PiTipiLight: IconType;
export declare const PiTireLight: IconType;
export declare const PiToggleLeftLight: IconType;
export declare const PiToggleRightLight: IconType;
export declare const PiToiletLight: IconType;
export declare const PiToiletPaperLight: IconType;
export declare const PiToolboxLight: IconType;
export declare const PiToothLight: IconType;
export declare const PiTornadoLight: IconType;
export declare const PiToteLight: IconType;
export declare const PiToteSimpleLight: IconType;
export declare const PiTowelLight: IconType;
export declare const PiTractorLight: IconType;
export declare const PiTrademarkLight: IconType;
export declare const PiTrademarkRegisteredLight: IconType;
export declare const PiTrafficConeLight: IconType;
export declare const PiTrafficSignLight: IconType;
export declare const PiTrafficSignalLight: IconType;
export declare const PiTrainLight: IconType;
export declare const PiTrainRegionalLight: IconType;
export declare const PiTrainSimpleLight: IconType;
export declare const PiTramLight: IconType;
export declare const PiTranslateLight: IconType;
export declare const PiTrashLight: IconType;
export declare const PiTrashSimpleLight: IconType;
export declare const PiTrayArrowDownLight: IconType;
export declare const PiTrayArrowUpLight: IconType;
export declare const PiTrayLight: IconType;
export declare const PiTreasureChestLight: IconType;
export declare const PiTreeEvergreenLight: IconType;
export declare const PiTreeLight: IconType;
export declare const PiTreePalmLight: IconType;
export declare const PiTreeStructureLight: IconType;
export declare const PiTreeViewLight: IconType;
export declare const PiTrendDownLight: IconType;
export declare const PiTrendUpLight: IconType;
export declare const PiTriangleDashedLight: IconType;
export declare const PiTriangleLight: IconType;
export declare const PiTrolleyLight: IconType;
export declare const PiTrolleySuitcaseLight: IconType;
export declare const PiTrophyLight: IconType;
export declare const PiTruckLight: IconType;
export declare const PiTruckTrailerLight: IconType;
export declare const PiTumblrLogoLight: IconType;
export declare const PiTwitchLogoLight: IconType;
export declare const PiTwitterLogoLight: IconType;
export declare const PiUmbrellaLight: IconType;
export declare const PiUmbrellaSimpleLight: IconType;
export declare const PiUnionLight: IconType;
export declare const PiUniteLight: IconType;
export declare const PiUniteSquareLight: IconType;
export declare const PiUploadLight: IconType;
export declare const PiUploadSimpleLight: IconType;
export declare const PiUsbLight: IconType;
export declare const PiUserCheckLight: IconType;
export declare const PiUserCircleCheckLight: IconType;
export declare const PiUserCircleDashedLight: IconType;
export declare const PiUserCircleGearLight: IconType;
export declare const PiUserCircleLight: IconType;
export declare const PiUserCircleMinusLight: IconType;
export declare const PiUserCirclePlusLight: IconType;
export declare const PiUserFocusLight: IconType;
export declare const PiUserGearLight: IconType;
export declare const PiUserLight: IconType;
export declare const PiUserListLight: IconType;
export declare const PiUserMinusLight: IconType;
export declare const PiUserPlusLight: IconType;
export declare const PiUserRectangleLight: IconType;
export declare const PiUserSoundLight: IconType;
export declare const PiUserSquareLight: IconType;
export declare const PiUserSwitchLight: IconType;
export declare const PiUsersFourLight: IconType;
export declare const PiUsersLight: IconType;
export declare const PiUsersThreeLight: IconType;
export declare const PiVanLight: IconType;
export declare const PiVaultLight: IconType;
export declare const PiVectorThreeLight: IconType;
export declare const PiVectorTwoLight: IconType;
export declare const PiVibrateLight: IconType;
export declare const PiVideoCameraLight: IconType;
export declare const PiVideoCameraSlashLight: IconType;
export declare const PiVideoConferenceLight: IconType;
export declare const PiVideoLight: IconType;
export declare const PiVignetteLight: IconType;
export declare const PiVinylRecordLight: IconType;
export declare const PiVirtualRealityLight: IconType;
export declare const PiVirusLight: IconType;
export declare const PiVisorLight: IconType;
export declare const PiVoicemailLight: IconType;
export declare const PiVolleyballLight: IconType;
export declare const PiWallLight: IconType;
export declare const PiWalletLight: IconType;
export declare const PiWarehouseLight: IconType;
export declare const PiWarningCircleLight: IconType;
export declare const PiWarningDiamondLight: IconType;
export declare const PiWarningLight: IconType;
export declare const PiWarningOctagonLight: IconType;
export declare const PiWashingMachineLight: IconType;
export declare const PiWatchLight: IconType;
export declare const PiWaveSawtoothLight: IconType;
export declare const PiWaveSineLight: IconType;
export declare const PiWaveSquareLight: IconType;
export declare const PiWaveTriangleLight: IconType;
export declare const PiWaveformLight: IconType;
export declare const PiWaveformSlashLight: IconType;
export declare const PiWavesLight: IconType;
export declare const PiWebcamLight: IconType;
export declare const PiWebcamSlashLight: IconType;
export declare const PiWebhooksLogoLight: IconType;
export declare const PiWechatLogoLight: IconType;
export declare const PiWhatsappLogoLight: IconType;
export declare const PiWheelchairLight: IconType;
export declare const PiWheelchairMotionLight: IconType;
export declare const PiWifiHighLight: IconType;
export declare const PiWifiLowLight: IconType;
export declare const PiWifiMediumLight: IconType;
export declare const PiWifiNoneLight: IconType;
export declare const PiWifiSlashLight: IconType;
export declare const PiWifiXLight: IconType;
export declare const PiWindLight: IconType;
export declare const PiWindmillLight: IconType;
export declare const PiWindowsLogoLight: IconType;
export declare const PiWineLight: IconType;
export declare const PiWrenchLight: IconType;
export declare const PiXCircleLight: IconType;
export declare const PiXLight: IconType;
export declare const PiXLogoLight: IconType;
export declare const PiXSquareLight: IconType;
export declare const PiYarnLight: IconType;
export declare const PiYinYangLight: IconType;
export declare const PiYoutubeLogoLight: IconType;
export declare const PiAcorn: IconType;
export declare const PiAddressBookTabs: IconType;
export declare const PiAddressBook: IconType;
export declare const PiAirTrafficControl: IconType;
export declare const PiAirplaneInFlight: IconType;
export declare const PiAirplaneLanding: IconType;
export declare const PiAirplaneTakeoff: IconType;
export declare const PiAirplaneTaxiing: IconType;
export declare const PiAirplaneTilt: IconType;
export declare const PiAirplane: IconType;
export declare const PiAirplay: IconType;
export declare const PiAlarm: IconType;
export declare const PiAlien: IconType;
export declare const PiAlignBottomSimple: IconType;
export declare const PiAlignBottom: IconType;
export declare const PiAlignCenterHorizontalSimple: IconType;
export declare const PiAlignCenterHorizontal: IconType;
export declare const PiAlignCenterVerticalSimple: IconType;
export declare const PiAlignCenterVertical: IconType;
export declare const PiAlignLeftSimple: IconType;
export declare const PiAlignLeft: IconType;
export declare const PiAlignRightSimple: IconType;
export declare const PiAlignRight: IconType;
export declare const PiAlignTopSimple: IconType;
export declare const PiAlignTop: IconType;
export declare const PiAmazonLogo: IconType;
export declare const PiAmbulance: IconType;
export declare const PiAnchorSimple: IconType;
export declare const PiAnchor: IconType;
export declare const PiAndroidLogo: IconType;
export declare const PiAngle: IconType;
export declare const PiAngularLogo: IconType;
export declare const PiAperture: IconType;
export declare const PiAppStoreLogo: IconType;
export declare const PiAppWindow: IconType;
export declare const PiAppleLogo: IconType;
export declare const PiApplePodcastsLogo: IconType;
export declare const PiApproximateEquals: IconType;
export declare const PiArchive: IconType;
export declare const PiArmchair: IconType;
export declare const PiArrowArcLeft: IconType;
export declare const PiArrowArcRight: IconType;
export declare const PiArrowBendDoubleUpLeft: IconType;
export declare const PiArrowBendDoubleUpRight: IconType;
export declare const PiArrowBendDownLeft: IconType;
export declare const PiArrowBendDownRight: IconType;
export declare const PiArrowBendLeftDown: IconType;
export declare const PiArrowBendLeftUp: IconType;
export declare const PiArrowBendRightDown: IconType;
export declare const PiArrowBendRightUp: IconType;
export declare const PiArrowBendUpLeft: IconType;
export declare const PiArrowBendUpRight: IconType;
export declare const PiArrowCircleDownLeft: IconType;
export declare const PiArrowCircleDownRight: IconType;
export declare const PiArrowCircleDown: IconType;
export declare const PiArrowCircleLeft: IconType;
export declare const PiArrowCircleRight: IconType;
export declare const PiArrowCircleUpLeft: IconType;
export declare const PiArrowCircleUpRight: IconType;
export declare const PiArrowCircleUp: IconType;
export declare const PiArrowClockwise: IconType;
export declare const PiArrowCounterClockwise: IconType;
export declare const PiArrowDownLeft: IconType;
export declare const PiArrowDownRight: IconType;
export declare const PiArrowDown: IconType;
export declare const PiArrowElbowDownLeft: IconType;
export declare const PiArrowElbowDownRight: IconType;
export declare const PiArrowElbowLeftDown: IconType;
export declare const PiArrowElbowLeftUp: IconType;
export declare const PiArrowElbowLeft: IconType;
export declare const PiArrowElbowRightDown: IconType;
export declare const PiArrowElbowRightUp: IconType;
export declare const PiArrowElbowRight: IconType;
export declare const PiArrowElbowUpLeft: IconType;
export declare const PiArrowElbowUpRight: IconType;
export declare const PiArrowFatDown: IconType;
export declare const PiArrowFatLeft: IconType;
export declare const PiArrowFatLineDown: IconType;
export declare const PiArrowFatLineLeft: IconType;
export declare const PiArrowFatLineRight: IconType;
export declare const PiArrowFatLineUp: IconType;
export declare const PiArrowFatLinesDown: IconType;
export declare const PiArrowFatLinesLeft: IconType;
export declare const PiArrowFatLinesRight: IconType;
export declare const PiArrowFatLinesUp: IconType;
export declare const PiArrowFatRight: IconType;
export declare const PiArrowFatUp: IconType;
export declare const PiArrowLeft: IconType;
export declare const PiArrowLineDownLeft: IconType;
export declare const PiArrowLineDownRight: IconType;
export declare const PiArrowLineDown: IconType;
export declare const PiArrowLineLeft: IconType;
export declare const PiArrowLineRight: IconType;
export declare const PiArrowLineUpLeft: IconType;
export declare const PiArrowLineUpRight: IconType;
export declare const PiArrowLineUp: IconType;
export declare const PiArrowRight: IconType;
export declare const PiArrowSquareDownLeft: IconType;
export declare const PiArrowSquareDownRight: IconType;
export declare const PiArrowSquareDown: IconType;
export declare const PiArrowSquareIn: IconType;
export declare const PiArrowSquareLeft: IconType;
export declare const PiArrowSquareOut: IconType;
export declare const PiArrowSquareRight: IconType;
export declare const PiArrowSquareUpLeft: IconType;
export declare const PiArrowSquareUpRight: IconType;
export declare const PiArrowSquareUp: IconType;
export declare const PiArrowUDownLeft: IconType;
export declare const PiArrowUDownRight: IconType;
export declare const PiArrowULeftDown: IconType;
export declare const PiArrowULeftUp: IconType;
export declare const PiArrowURightDown: IconType;
export declare const PiArrowURightUp: IconType;
export declare const PiArrowUUpLeft: IconType;
export declare const PiArrowUUpRight: IconType;
export declare const PiArrowUpLeft: IconType;
export declare const PiArrowUpRight: IconType;
export declare const PiArrowUp: IconType;
export declare const PiArrowsClockwise: IconType;
export declare const PiArrowsCounterClockwise: IconType;
export declare const PiArrowsDownUp: IconType;
export declare const PiArrowsHorizontal: IconType;
export declare const PiArrowsInCardinal: IconType;
export declare const PiArrowsInLineHorizontal: IconType;
export declare const PiArrowsInLineVertical: IconType;
export declare const PiArrowsInSimple: IconType;
export declare const PiArrowsIn: IconType;
export declare const PiArrowsLeftRight: IconType;
export declare const PiArrowsMerge: IconType;
export declare const PiArrowsOutCardinal: IconType;
export declare const PiArrowsOutLineHorizontal: IconType;
export declare const PiArrowsOutLineVertical: IconType;
export declare const PiArrowsOutSimple: IconType;
export declare const PiArrowsOut: IconType;
export declare const PiArrowsSplit: IconType;
export declare const PiArrowsVertical: IconType;
export declare const PiArticleMedium: IconType;
export declare const PiArticleNyTimes: IconType;
export declare const PiArticle: IconType;
export declare const PiAsclepius: IconType;
export declare const PiAsteriskSimple: IconType;
export declare const PiAsterisk: IconType;
export declare const PiAt: IconType;
export declare const PiAtom: IconType;
export declare const PiAvocado: IconType;
export declare const PiAxe: IconType;
export declare const PiBabyCarriage: IconType;
export declare const PiBaby: IconType;
export declare const PiBackpack: IconType;
export declare const PiBackspace: IconType;
export declare const PiBagSimple: IconType;
export declare const PiBag: IconType;
export declare const PiBalloon: IconType;
export declare const PiBandaids: IconType;
export declare const PiBank: IconType;
export declare const PiBarbell: IconType;
export declare const PiBarcode: IconType;
export declare const PiBarn: IconType;
export declare const PiBarricade: IconType;
export declare const PiBaseballCap: IconType;
export declare const PiBaseballHelmet: IconType;
export declare const PiBaseball: IconType;
export declare const PiBasket: IconType;
export declare const PiBasketball: IconType;
export declare const PiBathtub: IconType;
export declare const PiBatteryChargingVertical: IconType;
export declare const PiBatteryCharging: IconType;
export declare const PiBatteryEmpty: IconType;
export declare const PiBatteryFull: IconType;
export declare const PiBatteryHigh: IconType;
export declare const PiBatteryLow: IconType;
export declare const PiBatteryMedium: IconType;
export declare const PiBatteryPlusVertical: IconType;
export declare const PiBatteryPlus: IconType;
export declare const PiBatteryVerticalEmpty: IconType;
export declare const PiBatteryVerticalFull: IconType;
export declare const PiBatteryVerticalHigh: IconType;
export declare const PiBatteryVerticalLow: IconType;
export declare const PiBatteryVerticalMedium: IconType;
export declare const PiBatteryWarningVertical: IconType;
export declare const PiBatteryWarning: IconType;
export declare const PiBeachBall: IconType;
export declare const PiBeanie: IconType;
export declare const PiBed: IconType;
export declare const PiBeerBottle: IconType;
export declare const PiBeerStein: IconType;
export declare const PiBehanceLogo: IconType;
export declare const PiBellRinging: IconType;
export declare const PiBellSimpleRinging: IconType;
export declare const PiBellSimpleSlash: IconType;
export declare const PiBellSimpleZ: IconType;
export declare const PiBellSimple: IconType;
export declare const PiBellSlash: IconType;
export declare const PiBellZ: IconType;
export declare const PiBell: IconType;
export declare const PiBelt: IconType;
export declare const PiBezierCurve: IconType;
export declare const PiBicycle: IconType;
export declare const PiBinary: IconType;
export declare const PiBinoculars: IconType;
export declare const PiBiohazard: IconType;
export declare const PiBird: IconType;
export declare const PiBlueprint: IconType;
export declare const PiBluetoothConnected: IconType;
export declare const PiBluetoothSlash: IconType;
export declare const PiBluetoothX: IconType;
export declare const PiBluetooth: IconType;
export declare const PiBoat: IconType;
export declare const PiBomb: IconType;
export declare const PiBone: IconType;
export declare const PiBookBookmark: IconType;
export declare const PiBookOpenText: IconType;
export declare const PiBookOpenUser: IconType;
export declare const PiBookOpen: IconType;
export declare const PiBook: IconType;
export declare const PiBookmarkSimple: IconType;
export declare const PiBookmark: IconType;
export declare const PiBookmarksSimple: IconType;
export declare const PiBookmarks: IconType;
export declare const PiBooks: IconType;
export declare const PiBoot: IconType;
export declare const PiBoules: IconType;
export declare const PiBoundingBox: IconType;
export declare const PiBowlFood: IconType;
export declare const PiBowlSteam: IconType;
export declare const PiBowlingBall: IconType;
export declare const PiBoxArrowDown: IconType;
export declare const PiBoxArrowUp: IconType;
export declare const PiBoxingGlove: IconType;
export declare const PiBracketsAngle: IconType;
export declare const PiBracketsCurly: IconType;
export declare const PiBracketsRound: IconType;
export declare const PiBracketsSquare: IconType;
export declare const PiBrain: IconType;
export declare const PiBrandy: IconType;
export declare const PiBread: IconType;
export declare const PiBridge: IconType;
export declare const PiBriefcaseMetal: IconType;
export declare const PiBriefcase: IconType;
export declare const PiBroadcast: IconType;
export declare const PiBroom: IconType;
export declare const PiBrowser: IconType;
export declare const PiBrowsers: IconType;
export declare const PiBugBeetle: IconType;
export declare const PiBugDroid: IconType;
export declare const PiBug: IconType;
export declare const PiBuildingApartment: IconType;
export declare const PiBuildingOffice: IconType;
export declare const PiBuilding: IconType;
export declare const PiBuildings: IconType;
export declare const PiBulldozer: IconType;
export declare const PiBus: IconType;
export declare const PiButterfly: IconType;
export declare const PiCableCar: IconType;
export declare const PiCactus: IconType;
export declare const PiCake: IconType;
export declare const PiCalculator: IconType;
export declare const PiCalendarBlank: IconType;
export declare const PiCalendarCheck: IconType;
export declare const PiCalendarDot: IconType;
export declare const PiCalendarDots: IconType;
export declare const PiCalendarHeart: IconType;
export declare const PiCalendarMinus: IconType;
export declare const PiCalendarPlus: IconType;
export declare const PiCalendarSlash: IconType;
export declare const PiCalendarStar: IconType;
export declare const PiCalendarX: IconType;
export declare const PiCalendar: IconType;
export declare const PiCallBell: IconType;
export declare const PiCameraPlus: IconType;
export declare const PiCameraRotate: IconType;
export declare const PiCameraSlash: IconType;
export declare const PiCamera: IconType;
export declare const PiCampfire: IconType;
export declare const PiCarBattery: IconType;
export declare const PiCarProfile: IconType;
export declare const PiCarSimple: IconType;
export declare const PiCar: IconType;
export declare const PiCardholder: IconType;
export declare const PiCardsThree: IconType;
export declare const PiCards: IconType;
export declare const PiCaretCircleDoubleDown: IconType;
export declare const PiCaretCircleDoubleLeft: IconType;
export declare const PiCaretCircleDoubleRight: IconType;
export declare const PiCaretCircleDoubleUp: IconType;
export declare const PiCaretCircleDown: IconType;
export declare const PiCaretCircleLeft: IconType;
export declare const PiCaretCircleRight: IconType;
export declare const PiCaretCircleUpDown: IconType;
export declare const PiCaretCircleUp: IconType;
export declare const PiCaretDoubleDown: IconType;
export declare const PiCaretDoubleLeft: IconType;
export declare const PiCaretDoubleRight: IconType;
export declare const PiCaretDoubleUp: IconType;
export declare const PiCaretDown: IconType;
export declare const PiCaretLeft: IconType;
export declare const PiCaretLineDown: IconType;
export declare const PiCaretLineLeft: IconType;
export declare const PiCaretLineRight: IconType;
export declare const PiCaretLineUp: IconType;
export declare const PiCaretRight: IconType;
export declare const PiCaretUpDown: IconType;
export declare const PiCaretUp: IconType;
export declare const PiCarrot: IconType;
export declare const PiCashRegister: IconType;
export declare const PiCassetteTape: IconType;
export declare const PiCastleTurret: IconType;
export declare const PiCat: IconType;
export declare const PiCellSignalFull: IconType;
export declare const PiCellSignalHigh: IconType;
export declare const PiCellSignalLow: IconType;
export declare const PiCellSignalMedium: IconType;
export declare const PiCellSignalNone: IconType;
export declare const PiCellSignalSlash: IconType;
export declare const PiCellSignalX: IconType;
export declare const PiCellTower: IconType;
export declare const PiCertificate: IconType;
export declare const PiChair: IconType;
export declare const PiChalkboardSimple: IconType;
export declare const PiChalkboardTeacher: IconType;
export declare const PiChalkboard: IconType;
export declare const PiChampagne: IconType;
export declare const PiChargingStation: IconType;
export declare const PiChartBarHorizontal: IconType;
export declare const PiChartBar: IconType;
export declare const PiChartDonut: IconType;
export declare const PiChartLineDown: IconType;
export declare const PiChartLineUp: IconType;
export declare const PiChartLine: IconType;
export declare const PiChartPieSlice: IconType;
export declare const PiChartPie: IconType;
export declare const PiChartPolar: IconType;
export declare const PiChartScatter: IconType;
export declare const PiChatCenteredDots: IconType;
export declare const PiChatCenteredSlash: IconType;
export declare const PiChatCenteredText: IconType;
export declare const PiChatCentered: IconType;
export declare const PiChatCircleDots: IconType;
export declare const PiChatCircleSlash: IconType;
export declare const PiChatCircleText: IconType;
export declare const PiChatCircle: IconType;
export declare const PiChatDots: IconType;
export declare const PiChatSlash: IconType;
export declare const PiChatTeardropDots: IconType;
export declare const PiChatTeardropSlash: IconType;
export declare const PiChatTeardropText: IconType;
export declare const PiChatTeardrop: IconType;
export declare const PiChatText: IconType;
export declare const PiChat: IconType;
export declare const PiChatsCircle: IconType;
export declare const PiChatsTeardrop: IconType;
export declare const PiChats: IconType;
export declare const PiCheckCircle: IconType;
export declare const PiCheckFat: IconType;
export declare const PiCheckSquareOffset: IconType;
export declare const PiCheckSquare: IconType;
export declare const PiCheck: IconType;
export declare const PiCheckerboard: IconType;
export declare const PiChecks: IconType;
export declare const PiCheers: IconType;
export declare const PiCheese: IconType;
export declare const PiChefHat: IconType;
export declare const PiCherries: IconType;
export declare const PiChurch: IconType;
export declare const PiCigaretteSlash: IconType;
export declare const PiCigarette: IconType;
export declare const PiCircleDashed: IconType;
export declare const PiCircleHalfTilt: IconType;
export declare const PiCircleHalf: IconType;
export declare const PiCircleNotch: IconType;
export declare const PiCircle: IconType;
export declare const PiCirclesFour: IconType;
export declare const PiCirclesThreePlus: IconType;
export declare const PiCirclesThree: IconType;
export declare const PiCircuitry: IconType;
export declare const PiCity: IconType;
export declare const PiClipboardText: IconType;
export declare const PiClipboard: IconType;
export declare const PiClockAfternoon: IconType;
export declare const PiClockClockwise: IconType;
export declare const PiClockCountdown: IconType;
export declare const PiClockCounterClockwise: IconType;
export declare const PiClockUser: IconType;
export declare const PiClock: IconType;
export declare const PiClosedCaptioning: IconType;
export declare const PiCloudArrowDown: IconType;
export declare const PiCloudArrowUp: IconType;
export declare const PiCloudCheck: IconType;
export declare const PiCloudFog: IconType;
export declare const PiCloudLightning: IconType;
export declare const PiCloudMoon: IconType;
export declare const PiCloudRain: IconType;
export declare const PiCloudSlash: IconType;
export declare const PiCloudSnow: IconType;
export declare const PiCloudSun: IconType;
export declare const PiCloudWarning: IconType;
export declare const PiCloudX: IconType;
export declare const PiCloud: IconType;
export declare const PiClover: IconType;
export declare const PiClub: IconType;
export declare const PiCoatHanger: IconType;
export declare const PiCodaLogo: IconType;
export declare const PiCodeBlock: IconType;
export declare const PiCodeSimple: IconType;
export declare const PiCode: IconType;
export declare const PiCodepenLogo: IconType;
export declare const PiCodesandboxLogo: IconType;
export declare const PiCoffeeBean: IconType;
export declare const PiCoffee: IconType;
export declare const PiCoinVertical: IconType;
export declare const PiCoin: IconType;
export declare const PiCoins: IconType;
export declare const PiColumnsPlusLeft: IconType;
export declare const PiColumnsPlusRight: IconType;
export declare const PiColumns: IconType;
export declare const PiCommand: IconType;
export declare const PiCompassRose: IconType;
export declare const PiCompassTool: IconType;
export declare const PiCompass: IconType;
export declare const PiComputerTower: IconType;
export declare const PiConfetti: IconType;
export declare const PiContactlessPayment: IconType;
export declare const PiControl: IconType;
export declare const PiCookie: IconType;
export declare const PiCookingPot: IconType;
export declare const PiCopySimple: IconType;
export declare const PiCopy: IconType;
export declare const PiCopyleft: IconType;
export declare const PiCopyright: IconType;
export declare const PiCornersIn: IconType;
export declare const PiCornersOut: IconType;
export declare const PiCouch: IconType;
export declare const PiCourtBasketball: IconType;
export declare const PiCow: IconType;
export declare const PiCowboyHat: IconType;
export declare const PiCpu: IconType;
export declare const PiCraneTower: IconType;
export declare const PiCrane: IconType;
export declare const PiCreditCard: IconType;
export declare const PiCricket: IconType;
export declare const PiCrop: IconType;
export declare const PiCross: IconType;
export declare const PiCrosshairSimple: IconType;
export declare const PiCrosshair: IconType;
export declare const PiCrownCross: IconType;
export declare const PiCrownSimple: IconType;
export declare const PiCrown: IconType;
export declare const PiCubeFocus: IconType;
export declare const PiCubeTransparent: IconType;
export declare const PiCube: IconType;
export declare const PiCurrencyBtc: IconType;
export declare const PiCurrencyCircleDollar: IconType;
export declare const PiCurrencyCny: IconType;
export declare const PiCurrencyDollarSimple: IconType;
export declare const PiCurrencyDollar: IconType;
export declare const PiCurrencyEth: IconType;
export declare const PiCurrencyEur: IconType;
export declare const PiCurrencyGbp: IconType;
export declare const PiCurrencyInr: IconType;
export declare const PiCurrencyJpy: IconType;
export declare const PiCurrencyKrw: IconType;
export declare const PiCurrencyKzt: IconType;
export declare const PiCurrencyNgn: IconType;
export declare const PiCurrencyRub: IconType;
export declare const PiCursorClick: IconType;
export declare const PiCursorText: IconType;
export declare const PiCursor: IconType;
export declare const PiCylinder: IconType;
export declare const PiDatabase: IconType;
export declare const PiDesk: IconType;
export declare const PiDesktopTower: IconType;
export declare const PiDesktop: IconType;
export declare const PiDetective: IconType;
export declare const PiDevToLogo: IconType;
export declare const PiDeviceMobileCamera: IconType;
export declare const PiDeviceMobileSlash: IconType;
export declare const PiDeviceMobileSpeaker: IconType;
export declare const PiDeviceMobile: IconType;
export declare const PiDeviceRotate: IconType;
export declare const PiDeviceTabletCamera: IconType;
export declare const PiDeviceTabletSpeaker: IconType;
export declare const PiDeviceTablet: IconType;
export declare const PiDevices: IconType;
export declare const PiDiamond: IconType;
export declare const PiDiamondsFour: IconType;
export declare const PiDiceFive: IconType;
export declare const PiDiceFour: IconType;
export declare const PiDiceOne: IconType;
export declare const PiDiceSix: IconType;
export declare const PiDiceThree: IconType;
export declare const PiDiceTwo: IconType;
export declare const PiDisc: IconType;
export declare const PiDiscoBall: IconType;
export declare const PiDiscordLogo: IconType;
export declare const PiDivide: IconType;
export declare const PiDna: IconType;
export declare const PiDog: IconType;
export declare const PiDoorOpen: IconType;
export declare const PiDoor: IconType;
export declare const PiDotOutline: IconType;
export declare const PiDot: IconType;
export declare const PiDotsNine: IconType;
export declare const PiDotsSixVertical: IconType;
export declare const PiDotsSix: IconType;
export declare const PiDotsThreeCircleVertical: IconType;
export declare const PiDotsThreeCircle: IconType;
export declare const PiDotsThreeOutlineVertical: IconType;
export declare const PiDotsThreeOutline: IconType;
export declare const PiDotsThreeVertical: IconType;
export declare const PiDotsThree: IconType;
export declare const PiDownloadSimple: IconType;
export declare const PiDownload: IconType;
export declare const PiDress: IconType;
export declare const PiDresser: IconType;
export declare const PiDribbbleLogo: IconType;
export declare const PiDrone: IconType;
export declare const PiDropHalfBottom: IconType;
export declare const PiDropHalf: IconType;
export declare const PiDropSimple: IconType;
export declare const PiDropSlash: IconType;
export declare const PiDrop: IconType;
export declare const PiDropboxLogo: IconType;
export declare const PiEarSlash: IconType;
export declare const PiEar: IconType;
export declare const PiEggCrack: IconType;
export declare const PiEgg: IconType;
export declare const PiEjectSimple: IconType;
export declare const PiEject: IconType;
export declare const PiElevator: IconType;
export declare const PiEmpty: IconType;
export declare const PiEngine: IconType;
export declare const PiEnvelopeOpen: IconType;
export declare const PiEnvelopeSimpleOpen: IconType;
export declare const PiEnvelopeSimple: IconType;
export declare const PiEnvelope: IconType;
export declare const PiEqualizer: IconType;
export declare const PiEquals: IconType;
export declare const PiEraser: IconType;
export declare const PiEscalatorDown: IconType;
export declare const PiEscalatorUp: IconType;
export declare const PiExam: IconType;
export declare const PiExclamationMark: IconType;
export declare const PiExcludeSquare: IconType;
export declare const PiExclude: IconType;
export declare const PiExport: IconType;
export declare const PiEyeClosed: IconType;
export declare const PiEyeSlash: IconType;
export declare const PiEye: IconType;
export declare const PiEyedropperSample: IconType;
export declare const PiEyedropper: IconType;
export declare const PiEyeglasses: IconType;
export declare const PiEyes: IconType;
export declare const PiFaceMask: IconType;
export declare const PiFacebookLogo: IconType;
export declare const PiFactory: IconType;
export declare const PiFadersHorizontal: IconType;
export declare const PiFaders: IconType;
export declare const PiFalloutShelter: IconType;
export declare const PiFan: IconType;
export declare const PiFarm: IconType;
export declare const PiFastForwardCircle: IconType;
export declare const PiFastForward: IconType;
export declare const PiFeather: IconType;
export declare const PiFediverseLogo: IconType;
export declare const PiFigmaLogo: IconType;
export declare const PiFileArchive: IconType;
export declare const PiFileArrowDown: IconType;
export declare const PiFileArrowUp: IconType;
export declare const PiFileAudio: IconType;
export declare const PiFileCSharp: IconType;
export declare const PiFileC: IconType;
export declare const PiFileCloud: IconType;
export declare const PiFileCode: IconType;
export declare const PiFileCpp: IconType;
export declare const PiFileCss: IconType;
export declare const PiFileCsv: IconType;
export declare const PiFileDashed: IconType;
export declare const PiFileDoc: IconType;
export declare const PiFileHtml: IconType;
export declare const PiFileImage: IconType;
export declare const PiFileIni: IconType;
export declare const PiFileJpg: IconType;
export declare const PiFileJs: IconType;
export declare const PiFileJsx: IconType;
export declare const PiFileLock: IconType;
export declare const PiFileMagnifyingGlass: IconType;
export declare const PiFileMd: IconType;
export declare const PiFileMinus: IconType;
export declare const PiFilePdf: IconType;
export declare const PiFilePlus: IconType;
export declare const PiFilePng: IconType;
export declare const PiFilePpt: IconType;
export declare const PiFilePy: IconType;
export declare const PiFileRs: IconType;
export declare const PiFileSql: IconType;
export declare const PiFileSvg: IconType;
export declare const PiFileText: IconType;
export declare const PiFileTs: IconType;
export declare const PiFileTsx: IconType;
export declare const PiFileTxt: IconType;
export declare const PiFileVideo: IconType;
export declare const PiFileVue: IconType;
export declare const PiFileX: IconType;
export declare const PiFileXls: IconType;
export declare const PiFileZip: IconType;
export declare const PiFile: IconType;
export declare const PiFiles: IconType;
export declare const PiFilmReel: IconType;
export declare const PiFilmScript: IconType;
export declare const PiFilmSlate: IconType;
export declare const PiFilmStrip: IconType;
export declare const PiFingerprintSimple: IconType;
export declare const PiFingerprint: IconType;
export declare const PiFinnTheHuman: IconType;
export declare const PiFireExtinguisher: IconType;
export declare const PiFireSimple: IconType;
export declare const PiFireTruck: IconType;
export declare const PiFire: IconType;
export declare const PiFirstAidKit: IconType;
export declare const PiFirstAid: IconType;
export declare const PiFishSimple: IconType;
export declare const PiFish: IconType;
export declare const PiFlagBannerFold: IconType;
export declare const PiFlagBanner: IconType;
export declare const PiFlagCheckered: IconType;
export declare const PiFlagPennant: IconType;
export declare const PiFlag: IconType;
export declare const PiFlame: IconType;
export declare const PiFlashlight: IconType;
export declare const PiFlask: IconType;
export declare const PiFlipHorizontal: IconType;
export declare const PiFlipVertical: IconType;
export declare const PiFloppyDiskBack: IconType;
export declare const PiFloppyDisk: IconType;
export declare const PiFlowArrow: IconType;
export declare const PiFlowerLotus: IconType;
export declare const PiFlowerTulip: IconType;
export declare const PiFlower: IconType;
export declare const PiFlyingSaucer: IconType;
export declare const PiFolderDashed: IconType;
export declare const PiFolderLock: IconType;
export declare const PiFolderMinus: IconType;
export declare const PiFolderOpen: IconType;
export declare const PiFolderPlus: IconType;
export declare const PiFolderSimpleDashed: IconType;
export declare const PiFolderSimpleLock: IconType;
export declare const PiFolderSimpleMinus: IconType;
export declare const PiFolderSimplePlus: IconType;
export declare const PiFolderSimpleStar: IconType;
export declare const PiFolderSimpleUser: IconType;
export declare const PiFolderSimple: IconType;
export declare const PiFolderStar: IconType;
export declare const PiFolderUser: IconType;
export declare const PiFolder: IconType;
export declare const PiFolders: IconType;
export declare const PiFootballHelmet: IconType;
export declare const PiFootball: IconType;
export declare const PiFootprints: IconType;
export declare const PiForkKnife: IconType;
export declare const PiFourK: IconType;
export declare const PiFrameCorners: IconType;
export declare const PiFramerLogo: IconType;
export declare const PiFunction: IconType;
export declare const PiFunnelSimpleX: IconType;
export declare const PiFunnelSimple: IconType;
export declare const PiFunnelX: IconType;
export declare const PiFunnel: IconType;
export declare const PiGameController: IconType;
export declare const PiGarage: IconType;
export declare const PiGasCan: IconType;
export declare const PiGasPump: IconType;
export declare const PiGauge: IconType;
export declare const PiGavel: IconType;
export declare const PiGearFine: IconType;
export declare const PiGearSix: IconType;
export declare const PiGear: IconType;
export declare const PiGenderFemale: IconType;
export declare const PiGenderIntersex: IconType;
export declare const PiGenderMale: IconType;
export declare const PiGenderNeuter: IconType;
export declare const PiGenderNonbinary: IconType;
export declare const PiGenderTransgender: IconType;
export declare const PiGhost: IconType;
export declare const PiGif: IconType;
export declare const PiGift: IconType;
export declare const PiGitBranch: IconType;
export declare const PiGitCommit: IconType;
export declare const PiGitDiff: IconType;
export declare const PiGitFork: IconType;
export declare const PiGitMerge: IconType;
export declare const PiGitPullRequest: IconType;
export declare const PiGithubLogo: IconType;
export declare const PiGitlabLogoSimple: IconType;
export declare const PiGitlabLogo: IconType;
export declare const PiGlobeHemisphereEast: IconType;
export declare const PiGlobeHemisphereWest: IconType;
export declare const PiGlobeSimpleX: IconType;
export declare const PiGlobeSimple: IconType;
export declare const PiGlobeStand: IconType;
export declare const PiGlobeX: IconType;
export declare const PiGlobe: IconType;
export declare const PiGoggles: IconType;
export declare const PiGolf: IconType;
export declare const PiGoodreadsLogo: IconType;
export declare const PiGoogleCardboardLogo: IconType;
export declare const PiGoogleChromeLogo: IconType;
export declare const PiGoogleDriveLogo: IconType;
export declare const PiGoogleLogo: IconType;
export declare const PiGooglePhotosLogo: IconType;
export declare const PiGooglePlayLogo: IconType;
export declare const PiGooglePodcastsLogo: IconType;
export declare const PiGpsFix: IconType;
export declare const PiGpsSlash: IconType;
export declare const PiGps: IconType;
export declare const PiGradient: IconType;
export declare const PiGraduationCap: IconType;
export declare const PiGrainsSlash: IconType;
export declare const PiGrains: IconType;
export declare const PiGraph: IconType;
export declare const PiGraphicsCard: IconType;
export declare const PiGreaterThanOrEqual: IconType;
export declare const PiGreaterThan: IconType;
export declare const PiGridFour: IconType;
export declare const PiGridNine: IconType;
export declare const PiGuitar: IconType;
export declare const PiHairDryer: IconType;
export declare const PiHamburger: IconType;
export declare const PiHammer: IconType;
export declare const PiHandArrowDown: IconType;
export declare const PiHandArrowUp: IconType;
export declare const PiHandCoins: IconType;
export declare const PiHandDeposit: IconType;
export declare const PiHandEye: IconType;
export declare const PiHandFist: IconType;
export declare const PiHandGrabbing: IconType;
export declare const PiHandHeart: IconType;
export declare const PiHandPalm: IconType;
export declare const PiHandPeace: IconType;
export declare const PiHandPointing: IconType;
export declare const PiHandSoap: IconType;
export declare const PiHandSwipeLeft: IconType;
export declare const PiHandSwipeRight: IconType;
export declare const PiHandTap: IconType;
export declare const PiHandWaving: IconType;
export declare const PiHandWithdraw: IconType;
export declare const PiHand: IconType;
export declare const PiHandbagSimple: IconType;
export declare const PiHandbag: IconType;
export declare const PiHandsClapping: IconType;
export declare const PiHandsPraying: IconType;
export declare const PiHandshake: IconType;
export declare const PiHardDrive: IconType;
export declare const PiHardDrives: IconType;
export declare const PiHardHat: IconType;
export declare const PiHashStraight: IconType;
export declare const PiHash: IconType;
export declare const PiHeadCircuit: IconType;
export declare const PiHeadlights: IconType;
export declare const PiHeadphones: IconType;
export declare const PiHeadset: IconType;
export declare const PiHeartBreak: IconType;
export declare const PiHeartHalf: IconType;
export declare const PiHeartStraightBreak: IconType;
export declare const PiHeartStraight: IconType;
export declare const PiHeart: IconType;
export declare const PiHeartbeat: IconType;
export declare const PiHexagon: IconType;
export declare const PiHighDefinition: IconType;
export declare const PiHighHeel: IconType;
export declare const PiHighlighterCircle: IconType;
export declare const PiHighlighter: IconType;
export declare const PiHockey: IconType;
export declare const PiHoodie: IconType;
export declare const PiHorse: IconType;
export declare const PiHospital: IconType;
export declare const PiHourglassHigh: IconType;
export declare const PiHourglassLow: IconType;
export declare const PiHourglassMedium: IconType;
export declare const PiHourglassSimpleHigh: IconType;
export declare const PiHourglassSimpleLow: IconType;
export declare const PiHourglassSimpleMedium: IconType;
export declare const PiHourglassSimple: IconType;
export declare const PiHourglass: IconType;
export declare const PiHouseLine: IconType;
export declare const PiHouseSimple: IconType;
export declare const PiHouse: IconType;
export declare const PiHurricane: IconType;
export declare const PiIceCream: IconType;
export declare const PiIdentificationBadge: IconType;
export declare const PiIdentificationCard: IconType;
export declare const PiImageBroken: IconType;
export declare const PiImageSquare: IconType;
export declare const PiImage: IconType;
export declare const PiImagesSquare: IconType;
export declare const PiImages: IconType;
export declare const PiInfinity: IconType;
export declare const PiInfo: IconType;
export declare const PiInstagramLogo: IconType;
export declare const PiIntersectSquare: IconType;
export declare const PiIntersectThree: IconType;
export declare const PiIntersect: IconType;
export declare const PiIntersection: IconType;
export declare const PiInvoice: IconType;
export declare const PiIsland: IconType;
export declare const PiJarLabel: IconType;
export declare const PiJar: IconType;
export declare const PiJeep: IconType;
export declare const PiJoystick: IconType;
export declare const PiKanban: IconType;
export declare const PiKeyReturn: IconType;
export declare const PiKey: IconType;
export declare const PiKeyboard: IconType;
export declare const PiKeyhole: IconType;
export declare const PiKnife: IconType;
export declare const PiLadderSimple: IconType;
export declare const PiLadder: IconType;
export declare const PiLampPendant: IconType;
export declare const PiLamp: IconType;
export declare const PiLaptop: IconType;
export declare const PiLasso: IconType;
export declare const PiLastfmLogo: IconType;
export declare const PiLayout: IconType;
export declare const PiLeaf: IconType;
export declare const PiLectern: IconType;
export declare const PiLegoSmiley: IconType;
export declare const PiLego: IconType;
export declare const PiLessThanOrEqual: IconType;
export declare const PiLessThan: IconType;
export declare const PiLetterCircleH: IconType;
export declare const PiLetterCircleP: IconType;
export declare const PiLetterCircleV: IconType;
export declare const PiLifebuoy: IconType;
export declare const PiLightbulbFilament: IconType;
export declare const PiLightbulb: IconType;
export declare const PiLighthouse: IconType;
export declare const PiLightningA: IconType;
export declare const PiLightningSlash: IconType;
export declare const PiLightning: IconType;
export declare const PiLineSegment: IconType;
export declare const PiLineSegments: IconType;
export declare const PiLineVertical: IconType;
export declare const PiLinkBreak: IconType;
export declare const PiLinkSimpleBreak: IconType;
export declare const PiLinkSimpleHorizontalBreak: IconType;
export declare const PiLinkSimpleHorizontal: IconType;
export declare const PiLinkSimple: IconType;
export declare const PiLink: IconType;
export declare const PiLinkedinLogo: IconType;
export declare const PiLinktreeLogo: IconType;
export declare const PiLinuxLogo: IconType;
export declare const PiListBullets: IconType;
export declare const PiListChecks: IconType;
export declare const PiListDashes: IconType;
export declare const PiListHeart: IconType;
export declare const PiListMagnifyingGlass: IconType;
export declare const PiListNumbers: IconType;
export declare const PiListPlus: IconType;
export declare const PiListStar: IconType;
export declare const PiList: IconType;
export declare const PiLockKeyOpen: IconType;
export declare const PiLockKey: IconType;
export declare const PiLockLaminatedOpen: IconType;
export declare const PiLockLaminated: IconType;
export declare const PiLockOpen: IconType;
export declare const PiLockSimpleOpen: IconType;
export declare const PiLockSimple: IconType;
export declare const PiLock: IconType;
export declare const PiLockers: IconType;
export declare const PiLog: IconType;
export declare const PiMagicWand: IconType;
export declare const PiMagnetStraight: IconType;
export declare const PiMagnet: IconType;
export declare const PiMagnifyingGlassMinus: IconType;
export declare const PiMagnifyingGlassPlus: IconType;
export declare const PiMagnifyingGlass: IconType;
export declare const PiMailbox: IconType;
export declare const PiMapPinArea: IconType;
export declare const PiMapPinLine: IconType;
export declare const PiMapPinPlus: IconType;
export declare const PiMapPinSimpleArea: IconType;
export declare const PiMapPinSimpleLine: IconType;
export declare const PiMapPinSimple: IconType;
export declare const PiMapPin: IconType;
export declare const PiMapTrifold: IconType;
export declare const PiMarkdownLogo: IconType;
export declare const PiMarkerCircle: IconType;
export declare const PiMartini: IconType;
export declare const PiMaskHappy: IconType;
export declare const PiMaskSad: IconType;
export declare const PiMastodonLogo: IconType;
export declare const PiMathOperations: IconType;
export declare const PiMatrixLogo: IconType;
export declare const PiMedalMilitary: IconType;
export declare const PiMedal: IconType;
export declare const PiMediumLogo: IconType;
export declare const PiMegaphoneSimple: IconType;
export declare const PiMegaphone: IconType;
export declare const PiMemberOf: IconType;
export declare const PiMemory: IconType;
export declare const PiMessengerLogo: IconType;
export declare const PiMetaLogo: IconType;
export declare const PiMeteor: IconType;
export declare const PiMetronome: IconType;
export declare const PiMicrophoneSlash: IconType;
export declare const PiMicrophoneStage: IconType;
export declare const PiMicrophone: IconType;
export declare const PiMicroscope: IconType;
export declare const PiMicrosoftExcelLogo: IconType;
export declare const PiMicrosoftOutlookLogo: IconType;
export declare const PiMicrosoftPowerpointLogo: IconType;
export declare const PiMicrosoftTeamsLogo: IconType;
export declare const PiMicrosoftWordLogo: IconType;
export declare const PiMinusCircle: IconType;
export declare const PiMinusSquare: IconType;
export declare const PiMinus: IconType;
export declare const PiMoneyWavy: IconType;
export declare const PiMoney: IconType;
export declare const PiMonitorArrowUp: IconType;
export declare const PiMonitorPlay: IconType;
export declare const PiMonitor: IconType;
export declare const PiMoonStars: IconType;
export declare const PiMoon: IconType;
export declare const PiMopedFront: IconType;
export declare const PiMoped: IconType;
export declare const PiMosque: IconType;
export declare const PiMotorcycle: IconType;
export declare const PiMountains: IconType;
export declare const PiMouseLeftClick: IconType;
export declare const PiMouseMiddleClick: IconType;
export declare const PiMouseRightClick: IconType;
export declare const PiMouseScroll: IconType;
export declare const PiMouseSimple: IconType;
export declare const PiMouse: IconType;
export declare const PiMusicNoteSimple: IconType;
export declare const PiMusicNote: IconType;
export declare const PiMusicNotesMinus: IconType;
export declare const PiMusicNotesPlus: IconType;
export declare const PiMusicNotesSimple: IconType;
export declare const PiMusicNotes: IconType;
export declare const PiNavigationArrow: IconType;
export declare const PiNeedle: IconType;
export declare const PiNetworkSlash: IconType;
export declare const PiNetworkX: IconType;
export declare const PiNetwork: IconType;
export declare const PiNewspaperClipping: IconType;
export declare const PiNewspaper: IconType;
export declare const PiNotEquals: IconType;
export declare const PiNotMemberOf: IconType;
export declare const PiNotSubsetOf: IconType;
export declare const PiNotSupersetOf: IconType;
export declare const PiNotches: IconType;
export declare const PiNoteBlank: IconType;
export declare const PiNotePencil: IconType;
export declare const PiNote: IconType;
export declare const PiNotebook: IconType;
export declare const PiNotepad: IconType;
export declare const PiNotification: IconType;
export declare const PiNotionLogo: IconType;
export declare const PiNuclearPlant: IconType;
export declare const PiNumberCircleEight: IconType;
export declare const PiNumberCircleFive: IconType;
export declare const PiNumberCircleFour: IconType;
export declare const PiNumberCircleNine: IconType;
export declare const PiNumberCircleOne: IconType;
export declare const PiNumberCircleSeven: IconType;
export declare const PiNumberCircleSix: IconType;
export declare const PiNumberCircleThree: IconType;
export declare const PiNumberCircleTwo: IconType;
export declare const PiNumberCircleZero: IconType;
export declare const PiNumberEight: IconType;
export declare const PiNumberFive: IconType;
export declare const PiNumberFour: IconType;
export declare const PiNumberNine: IconType;
export declare const PiNumberOne: IconType;
export declare const PiNumberSeven: IconType;
export declare const PiNumberSix: IconType;
export declare const PiNumberSquareEight: IconType;
export declare const PiNumberSquareFive: IconType;
export declare const PiNumberSquareFour: IconType;
export declare const PiNumberSquareNine: IconType;
export declare const PiNumberSquareOne: IconType;
export declare const PiNumberSquareSeven: IconType;
export declare const PiNumberSquareSix: IconType;
export declare const PiNumberSquareThree: IconType;
export declare const PiNumberSquareTwo: IconType;
export declare const PiNumberSquareZero: IconType;
export declare const PiNumberThree: IconType;
export declare const PiNumberTwo: IconType;
export declare const PiNumberZero: IconType;
export declare const PiNumpad: IconType;
export declare const PiNut: IconType;
export declare const PiNyTimesLogo: IconType;
export declare const PiOctagon: IconType;
export declare const PiOfficeChair: IconType;
export declare const PiOnigiri: IconType;
export declare const PiOpenAiLogo: IconType;
export declare const PiOption: IconType;
export declare const PiOrangeSlice: IconType;
export declare const PiOrange: IconType;
export declare const PiOven: IconType;
export declare const PiPackage: IconType;
export declare const PiPaintBrushBroad: IconType;
export declare const PiPaintBrushHousehold: IconType;
export declare const PiPaintBrush: IconType;
export declare const PiPaintBucket: IconType;
export declare const PiPaintRoller: IconType;
export declare const PiPalette: IconType;
export declare const PiPanorama: IconType;
export declare const PiPants: IconType;
export declare const PiPaperPlaneRight: IconType;
export declare const PiPaperPlaneTilt: IconType;
export declare const PiPaperPlane: IconType;
export declare const PiPaperclipHorizontal: IconType;
export declare const PiPaperclip: IconType;
export declare const PiParachute: IconType;
export declare const PiParagraph: IconType;
export declare const PiParallelogram: IconType;
export declare const PiPark: IconType;
export declare const PiPassword: IconType;
export declare const PiPath: IconType;
export declare const PiPatreonLogo: IconType;
export declare const PiPauseCircle: IconType;
export declare const PiPause: IconType;
export declare const PiPawPrint: IconType;
export declare const PiPaypalLogo: IconType;
export declare const PiPeace: IconType;
export declare const PiPenNibStraight: IconType;
export declare const PiPenNib: IconType;
export declare const PiPen: IconType;
export declare const PiPencilCircle: IconType;
export declare const PiPencilLine: IconType;
export declare const PiPencilRuler: IconType;
export declare const PiPencilSimpleLine: IconType;
export declare const PiPencilSimpleSlash: IconType;
export declare const PiPencilSimple: IconType;
export declare const PiPencilSlash: IconType;
export declare const PiPencil: IconType;
export declare const PiPentagon: IconType;
export declare const PiPentagram: IconType;
export declare const PiPepper: IconType;
export declare const PiPercent: IconType;
export declare const PiPersonArmsSpread: IconType;
export declare const PiPersonSimpleBike: IconType;
export declare const PiPersonSimpleCircle: IconType;
export declare const PiPersonSimpleHike: IconType;
export declare const PiPersonSimpleRun: IconType;
export declare const PiPersonSimpleSki: IconType;
export declare const PiPersonSimpleSnowboard: IconType;
export declare const PiPersonSimpleSwim: IconType;
export declare const PiPersonSimpleTaiChi: IconType;
export declare const PiPersonSimpleThrow: IconType;
export declare const PiPersonSimpleWalk: IconType;
export declare const PiPersonSimple: IconType;
export declare const PiPerson: IconType;
export declare const PiPerspective: IconType;
export declare const PiPhoneCall: IconType;
export declare const PiPhoneDisconnect: IconType;
export declare const PiPhoneIncoming: IconType;
export declare const PiPhoneList: IconType;
export declare const PiPhoneOutgoing: IconType;
export declare const PiPhonePause: IconType;
export declare const PiPhonePlus: IconType;
export declare const PiPhoneSlash: IconType;
export declare const PiPhoneTransfer: IconType;
export declare const PiPhoneX: IconType;
export declare const PiPhone: IconType;
export declare const PiPhosphorLogo: IconType;
export declare const PiPi: IconType;
export declare const PiPianoKeys: IconType;
export declare const PiPicnicTable: IconType;
export declare const PiPictureInPicture: IconType;
export declare const PiPiggyBank: IconType;
export declare const PiPill: IconType;
export declare const PiPingPong: IconType;
export declare const PiPintGlass: IconType;
export declare const PiPinterestLogo: IconType;
export declare const PiPinwheel: IconType;
export declare const PiPipeWrench: IconType;
export declare const PiPipe: IconType;
export declare const PiPixLogo: IconType;
export declare const PiPizza: IconType;
export declare const PiPlaceholder: IconType;
export declare const PiPlanet: IconType;
export declare const PiPlant: IconType;
export declare const PiPlayCircle: IconType;
export declare const PiPlayPause: IconType;
export declare const PiPlay: IconType;
export declare const PiPlaylist: IconType;
export declare const PiPlugCharging: IconType;
export declare const PiPlug: IconType;
export declare const PiPlugsConnected: IconType;
export declare const PiPlugs: IconType;
export declare const PiPlusCircle: IconType;
export declare const PiPlusMinus: IconType;
export declare const PiPlusSquare: IconType;
export declare const PiPlus: IconType;
export declare const PiPokerChip: IconType;
export declare const PiPoliceCar: IconType;
export declare const PiPolygon: IconType;
export declare const PiPopcorn: IconType;
export declare const PiPopsicle: IconType;
export declare const PiPottedPlant: IconType;
export declare const PiPower: IconType;
export declare const PiPrescription: IconType;
export declare const PiPresentationChart: IconType;
export declare const PiPresentation: IconType;
export declare const PiPrinter: IconType;
export declare const PiProhibitInset: IconType;
export declare const PiProhibit: IconType;
export declare const PiProjectorScreenChart: IconType;
export declare const PiProjectorScreen: IconType;
export declare const PiPulse: IconType;
export declare const PiPushPinSimpleSlash: IconType;
export declare const PiPushPinSimple: IconType;
export declare const PiPushPinSlash: IconType;
export declare const PiPushPin: IconType;
export declare const PiPuzzlePiece: IconType;
export declare const PiQrCode: IconType;
export declare const PiQuestionMark: IconType;
export declare const PiQuestion: IconType;
export declare const PiQueue: IconType;
export declare const PiQuotes: IconType;
export declare const PiRabbit: IconType;
export declare const PiRacquet: IconType;
export declare const PiRadical: IconType;
export declare const PiRadioButton: IconType;
export declare const PiRadio: IconType;
export declare const PiRadioactive: IconType;
export declare const PiRainbowCloud: IconType;
export declare const PiRainbow: IconType;
export declare const PiRanking: IconType;
export declare const PiReadCvLogo: IconType;
export declare const PiReceiptX: IconType;
export declare const PiReceipt: IconType;
export declare const PiRecord: IconType;
export declare const PiRectangleDashed: IconType;
export declare const PiRectangle: IconType;
export declare const PiRecycle: IconType;
export declare const PiRedditLogo: IconType;
export declare const PiRepeatOnce: IconType;
export declare const PiRepeat: IconType;
export declare const PiReplitLogo: IconType;
export declare const PiResize: IconType;
export declare const PiRewindCircle: IconType;
export declare const PiRewind: IconType;
export declare const PiRoadHorizon: IconType;
export declare const PiRobot: IconType;
export declare const PiRocketLaunch: IconType;
export declare const PiRocket: IconType;
export declare const PiRowsPlusBottom: IconType;
export declare const PiRowsPlusTop: IconType;
export declare const PiRows: IconType;
export declare const PiRssSimple: IconType;
export declare const PiRss: IconType;
export declare const PiRug: IconType;
export declare const PiRuler: IconType;
export declare const PiSailboat: IconType;
export declare const PiScales: IconType;
export declare const PiScanSmiley: IconType;
export declare const PiScan: IconType;
export declare const PiScissors: IconType;
export declare const PiScooter: IconType;
export declare const PiScreencast: IconType;
export declare const PiScrewdriver: IconType;
export declare const PiScribbleLoop: IconType;
export declare const PiScribble: IconType;
export declare const PiScroll: IconType;
export declare const PiSealCheck: IconType;
export declare const PiSealPercent: IconType;
export declare const PiSealQuestion: IconType;
export declare const PiSealWarning: IconType;
export declare const PiSeal: IconType;
export declare const PiSeat: IconType;
export declare const PiSeatbelt: IconType;
export declare const PiSecurityCamera: IconType;
export declare const PiSelectionAll: IconType;
export declare const PiSelectionBackground: IconType;
export declare const PiSelectionForeground: IconType;
export declare const PiSelectionInverse: IconType;
export declare const PiSelectionPlus: IconType;
export declare const PiSelectionSlash: IconType;
export declare const PiSelection: IconType;
export declare const PiShapes: IconType;
export declare const PiShareFat: IconType;
export declare const PiShareNetwork: IconType;
export declare const PiShare: IconType;
export declare const PiShieldCheck: IconType;
export declare const PiShieldCheckered: IconType;
export declare const PiShieldChevron: IconType;
export declare const PiShieldPlus: IconType;
export declare const PiShieldSlash: IconType;
export declare const PiShieldStar: IconType;
export declare const PiShieldWarning: IconType;
export declare const PiShield: IconType;
export declare const PiShippingContainer: IconType;
export declare const PiShirtFolded: IconType;
export declare const PiShootingStar: IconType;
export declare const PiShoppingBagOpen: IconType;
export declare const PiShoppingBag: IconType;
export declare const PiShoppingCartSimple: IconType;
export declare const PiShoppingCart: IconType;
export declare const PiShovel: IconType;
export declare const PiShower: IconType;
export declare const PiShrimp: IconType;
export declare const PiShuffleAngular: IconType;
export declare const PiShuffleSimple: IconType;
export declare const PiShuffle: IconType;
export declare const PiSidebarSimple: IconType;
export declare const PiSidebar: IconType;
export declare const PiSigma: IconType;
export declare const PiSignIn: IconType;
export declare const PiSignOut: IconType;
export declare const PiSignature: IconType;
export declare const PiSignpost: IconType;
export declare const PiSimCard: IconType;
export declare const PiSiren: IconType;
export declare const PiSketchLogo: IconType;
export declare const PiSkipBackCircle: IconType;
export declare const PiSkipBack: IconType;
export declare const PiSkipForwardCircle: IconType;
export declare const PiSkipForward: IconType;
export declare const PiSkull: IconType;
export declare const PiSkypeLogo: IconType;
export declare const PiSlackLogo: IconType;
export declare const PiSlidersHorizontal: IconType;
export declare const PiSliders: IconType;
export declare const PiSlideshow: IconType;
export declare const PiSmileyAngry: IconType;
export declare const PiSmileyBlank: IconType;
export declare const PiSmileyMeh: IconType;
export declare const PiSmileyMelting: IconType;
export declare const PiSmileyNervous: IconType;
export declare const PiSmileySad: IconType;
export declare const PiSmileySticker: IconType;
export declare const PiSmileyWink: IconType;
export declare const PiSmileyXEyes: IconType;
export declare const PiSmiley: IconType;
export declare const PiSnapchatLogo: IconType;
export declare const PiSneakerMove: IconType;
export declare const PiSneaker: IconType;
export declare const PiSnowflake: IconType;
export declare const PiSoccerBall: IconType;
export declare const PiSock: IconType;
export declare const PiSolarPanel: IconType;
export declare const PiSolarRoof: IconType;
export declare const PiSortAscending: IconType;
export declare const PiSortDescending: IconType;
export declare const PiSoundcloudLogo: IconType;
export declare const PiSpade: IconType;
export declare const PiSparkle: IconType;
export declare const PiSpeakerHifi: IconType;
export declare const PiSpeakerHigh: IconType;
export declare const PiSpeakerLow: IconType;
export declare const PiSpeakerNone: IconType;
export declare const PiSpeakerSimpleHigh: IconType;
export declare const PiSpeakerSimpleLow: IconType;
export declare const PiSpeakerSimpleNone: IconType;
export declare const PiSpeakerSimpleSlash: IconType;
export declare const PiSpeakerSimpleX: IconType;
export declare const PiSpeakerSlash: IconType;
export declare const PiSpeakerX: IconType;
export declare const PiSpeedometer: IconType;
export declare const PiSphere: IconType;
export declare const PiSpinnerBall: IconType;
export declare const PiSpinnerGap: IconType;
export declare const PiSpinner: IconType;
export declare const PiSpiral: IconType;
export declare const PiSplitHorizontal: IconType;
export declare const PiSplitVertical: IconType;
export declare const PiSpotifyLogo: IconType;
export declare const PiSprayBottle: IconType;
export declare const PiSquareHalfBottom: IconType;
export declare const PiSquareHalf: IconType;
export declare const PiSquareLogo: IconType;
export declare const PiSquareSplitHorizontal: IconType;
export declare const PiSquareSplitVertical: IconType;
export declare const PiSquare: IconType;
export declare const PiSquaresFour: IconType;
export declare const PiStackMinus: IconType;
export declare const PiStackOverflowLogo: IconType;
export declare const PiStackPlus: IconType;
export declare const PiStackSimple: IconType;
export declare const PiStack: IconType;
export declare const PiStairs: IconType;
export declare const PiStamp: IconType;
export declare const PiStandardDefinition: IconType;
export declare const PiStarAndCrescent: IconType;
export declare const PiStarFour: IconType;
export declare const PiStarHalf: IconType;
export declare const PiStarOfDavid: IconType;
export declare const PiStar: IconType;
export declare const PiSteamLogo: IconType;
export declare const PiSteeringWheel: IconType;
export declare const PiSteps: IconType;
export declare const PiStethoscope: IconType;
export declare const PiSticker: IconType;
export declare const PiStool: IconType;
export declare const PiStopCircle: IconType;
export declare const PiStop: IconType;
export declare const PiStorefront: IconType;
export declare const PiStrategy: IconType;
export declare const PiStripeLogo: IconType;
export declare const PiStudent: IconType;
export declare const PiSubsetOf: IconType;
export declare const PiSubsetProperOf: IconType;
export declare const PiSubtitlesSlash: IconType;
export declare const PiSubtitles: IconType;
export declare const PiSubtractSquare: IconType;
export declare const PiSubtract: IconType;
export declare const PiSubway: IconType;
export declare const PiSuitcaseRolling: IconType;
export declare const PiSuitcaseSimple: IconType;
export declare const PiSuitcase: IconType;
export declare const PiSunDim: IconType;
export declare const PiSunHorizon: IconType;
export declare const PiSun: IconType;
export declare const PiSunglasses: IconType;
export declare const PiSupersetOf: IconType;
export declare const PiSupersetProperOf: IconType;
export declare const PiSwap: IconType;
export declare const PiSwatches: IconType;
export declare const PiSwimmingPool: IconType;
export declare const PiSword: IconType;
export declare const PiSynagogue: IconType;
export declare const PiSyringe: IconType;
export declare const PiTShirt: IconType;
export declare const PiTable: IconType;
export declare const PiTabs: IconType;
export declare const PiTagChevron: IconType;
export declare const PiTagSimple: IconType;
export declare const PiTag: IconType;
export declare const PiTarget: IconType;
export declare const PiTaxi: IconType;
export declare const PiTeaBag: IconType;
export declare const PiTelegramLogo: IconType;
export declare const PiTelevisionSimple: IconType;
export declare const PiTelevision: IconType;
export declare const PiTennisBall: IconType;
export declare const PiTent: IconType;
export declare const PiTerminalWindow: IconType;
export declare const PiTerminal: IconType;
export declare const PiTestTube: IconType;
export declare const PiTextAUnderline: IconType;
export declare const PiTextAa: IconType;
export declare const PiTextAlignCenter: IconType;
export declare const PiTextAlignJustify: IconType;
export declare const PiTextAlignLeft: IconType;
export declare const PiTextAlignRight: IconType;
export declare const PiTextB: IconType;
export declare const PiTextColumns: IconType;
export declare const PiTextHFive: IconType;
export declare const PiTextHFour: IconType;
export declare const PiTextHOne: IconType;
export declare const PiTextHSix: IconType;
export declare const PiTextHThree: IconType;
export declare const PiTextHTwo: IconType;
export declare const PiTextH: IconType;
export declare const PiTextIndent: IconType;
export declare const PiTextItalic: IconType;
export declare const PiTextOutdent: IconType;
export declare const PiTextStrikethrough: IconType;
export declare const PiTextSubscript: IconType;
export declare const PiTextSuperscript: IconType;
export declare const PiTextTSlash: IconType;
export declare const PiTextT: IconType;
export declare const PiTextUnderline: IconType;
export declare const PiTextbox: IconType;
export declare const PiThermometerCold: IconType;
export declare const PiThermometerHot: IconType;
export declare const PiThermometerSimple: IconType;
export declare const PiThermometer: IconType;
export declare const PiThreadsLogo: IconType;
export declare const PiThreeD: IconType;
export declare const PiThumbsDown: IconType;
export declare const PiThumbsUp: IconType;
export declare const PiTicket: IconType;
export declare const PiTidalLogo: IconType;
export declare const PiTiktokLogo: IconType;
export declare const PiTilde: IconType;
export declare const PiTimer: IconType;
export declare const PiTipJar: IconType;
export declare const PiTipi: IconType;
export declare const PiTire: IconType;
export declare const PiToggleLeft: IconType;
export declare const PiToggleRight: IconType;
export declare const PiToiletPaper: IconType;
export declare const PiToilet: IconType;
export declare const PiToolbox: IconType;
export declare const PiTooth: IconType;
export declare const PiTornado: IconType;
export declare const PiToteSimple: IconType;
export declare const PiTote: IconType;
export declare const PiTowel: IconType;
export declare const PiTractor: IconType;
export declare const PiTrademarkRegistered: IconType;
export declare const PiTrademark: IconType;
export declare const PiTrafficCone: IconType;
export declare const PiTrafficSign: IconType;
export declare const PiTrafficSignal: IconType;
export declare const PiTrainRegional: IconType;
export declare const PiTrainSimple: IconType;
export declare const PiTrain: IconType;
export declare const PiTram: IconType;
export declare const PiTranslate: IconType;
export declare const PiTrashSimple: IconType;
export declare const PiTrash: IconType;
export declare const PiTrayArrowDown: IconType;
export declare const PiTrayArrowUp: IconType;
export declare const PiTray: IconType;
export declare const PiTreasureChest: IconType;
export declare const PiTreeEvergreen: IconType;
export declare const PiTreePalm: IconType;
export declare const PiTreeStructure: IconType;
export declare const PiTreeView: IconType;
export declare const PiTree: IconType;
export declare const PiTrendDown: IconType;
export declare const PiTrendUp: IconType;
export declare const PiTriangleDashed: IconType;
export declare const PiTriangle: IconType;
export declare const PiTrolleySuitcase: IconType;
export declare const PiTrolley: IconType;
export declare const PiTrophy: IconType;
export declare const PiTruckTrailer: IconType;
export declare const PiTruck: IconType;
export declare const PiTumblrLogo: IconType;
export declare const PiTwitchLogo: IconType;
export declare const PiTwitterLogo: IconType;
export declare const PiUmbrellaSimple: IconType;
export declare const PiUmbrella: IconType;
export declare const PiUnion: IconType;
export declare const PiUniteSquare: IconType;
export declare const PiUnite: IconType;
export declare const PiUploadSimple: IconType;
export declare const PiUpload: IconType;
export declare const PiUsb: IconType;
export declare const PiUserCheck: IconType;
export declare const PiUserCircleCheck: IconType;
export declare const PiUserCircleDashed: IconType;
export declare const PiUserCircleGear: IconType;
export declare const PiUserCircleMinus: IconType;
export declare const PiUserCirclePlus: IconType;
export declare const PiUserCircle: IconType;
export declare const PiUserFocus: IconType;
export declare const PiUserGear: IconType;
export declare const PiUserList: IconType;
export declare const PiUserMinus: IconType;
export declare const PiUserPlus: IconType;
export declare const PiUserRectangle: IconType;
export declare const PiUserSound: IconType;
export declare const PiUserSquare: IconType;
export declare const PiUserSwitch: IconType;
export declare const PiUser: IconType;
export declare const PiUsersFour: IconType;
export declare const PiUsersThree: IconType;
export declare const PiUsers: IconType;
export declare const PiVan: IconType;
export declare const PiVault: IconType;
export declare const PiVectorThree: IconType;
export declare const PiVectorTwo: IconType;
export declare const PiVibrate: IconType;
export declare const PiVideoCameraSlash: IconType;
export declare const PiVideoCamera: IconType;
export declare const PiVideoConference: IconType;
export declare const PiVideo: IconType;
export declare const PiVignette: IconType;
export declare const PiVinylRecord: IconType;
export declare const PiVirtualReality: IconType;
export declare const PiVirus: IconType;
export declare const PiVisor: IconType;
export declare const PiVoicemail: IconType;
export declare const PiVolleyball: IconType;
export declare const PiWall: IconType;
export declare const PiWallet: IconType;
export declare const PiWarehouse: IconType;
export declare const PiWarningCircle: IconType;
export declare const PiWarningDiamond: IconType;
export declare const PiWarningOctagon: IconType;
export declare const PiWarning: IconType;
export declare const PiWashingMachine: IconType;
export declare const PiWatch: IconType;
export declare const PiWaveSawtooth: IconType;
export declare const PiWaveSine: IconType;
export declare const PiWaveSquare: IconType;
export declare const PiWaveTriangle: IconType;
export declare const PiWaveformSlash: IconType;
export declare const PiWaveform: IconType;
export declare const PiWaves: IconType;
export declare const PiWebcamSlash: IconType;
export declare const PiWebcam: IconType;
export declare const PiWebhooksLogo: IconType;
export declare const PiWechatLogo: IconType;
export declare const PiWhatsappLogo: IconType;
export declare const PiWheelchairMotion: IconType;
export declare const PiWheelchair: IconType;
export declare const PiWifiHigh: IconType;
export declare const PiWifiLow: IconType;
export declare const PiWifiMedium: IconType;
export declare const PiWifiNone: IconType;
export declare const PiWifiSlash: IconType;
export declare const PiWifiX: IconType;
export declare const PiWind: IconType;
export declare const PiWindmill: IconType;
export declare const PiWindowsLogo: IconType;
export declare const PiWine: IconType;
export declare const PiWrench: IconType;
export declare const PiXCircle: IconType;
export declare const PiXLogo: IconType;
export declare const PiXSquare: IconType;
export declare const PiX: IconType;
export declare const PiYarn: IconType;
export declare const PiYinYang: IconType;
export declare const PiYoutubeLogo: IconType;
export declare const PiAcornThin: IconType;
export declare const PiAddressBookTabsThin: IconType;
export declare const PiAddressBookThin: IconType;
export declare const PiAirTrafficControlThin: IconType;
export declare const PiAirplaneInFlightThin: IconType;
export declare const PiAirplaneLandingThin: IconType;
export declare const PiAirplaneTakeoffThin: IconType;
export declare const PiAirplaneTaxiingThin: IconType;
export declare const PiAirplaneThin: IconType;
export declare const PiAirplaneTiltThin: IconType;
export declare const PiAirplayThin: IconType;
export declare const PiAlarmThin: IconType;
export declare const PiAlienThin: IconType;
export declare const PiAlignBottomSimpleThin: IconType;
export declare const PiAlignBottomThin: IconType;
export declare const PiAlignCenterHorizontalSimpleThin: IconType;
export declare const PiAlignCenterHorizontalThin: IconType;
export declare const PiAlignCenterVerticalSimpleThin: IconType;
export declare const PiAlignCenterVerticalThin: IconType;
export declare const PiAlignLeftSimpleThin: IconType;
export declare const PiAlignLeftThin: IconType;
export declare const PiAlignRightSimpleThin: IconType;
export declare const PiAlignRightThin: IconType;
export declare const PiAlignTopSimpleThin: IconType;
export declare const PiAlignTopThin: IconType;
export declare const PiAmazonLogoThin: IconType;
export declare const PiAmbulanceThin: IconType;
export declare const PiAnchorSimpleThin: IconType;
export declare const PiAnchorThin: IconType;
export declare const PiAndroidLogoThin: IconType;
export declare const PiAngleThin: IconType;
export declare const PiAngularLogoThin: IconType;
export declare const PiApertureThin: IconType;
export declare const PiAppStoreLogoThin: IconType;
export declare const PiAppWindowThin: IconType;
export declare const PiAppleLogoThin: IconType;
export declare const PiApplePodcastsLogoThin: IconType;
export declare const PiApproximateEqualsThin: IconType;
export declare const PiArchiveThin: IconType;
export declare const PiArmchairThin: IconType;
export declare const PiArrowArcLeftThin: IconType;
export declare const PiArrowArcRightThin: IconType;
export declare const PiArrowBendDoubleUpLeftThin: IconType;
export declare const PiArrowBendDoubleUpRightThin: IconType;
export declare const PiArrowBendDownLeftThin: IconType;
export declare const PiArrowBendDownRightThin: IconType;
export declare const PiArrowBendLeftDownThin: IconType;
export declare const PiArrowBendLeftUpThin: IconType;
export declare const PiArrowBendRightDownThin: IconType;
export declare const PiArrowBendRightUpThin: IconType;
export declare const PiArrowBendUpLeftThin: IconType;
export declare const PiArrowBendUpRightThin: IconType;
export declare const PiArrowCircleDownLeftThin: IconType;
export declare const PiArrowCircleDownRightThin: IconType;
export declare const PiArrowCircleDownThin: IconType;
export declare const PiArrowCircleLeftThin: IconType;
export declare const PiArrowCircleRightThin: IconType;
export declare const PiArrowCircleUpLeftThin: IconType;
export declare const PiArrowCircleUpRightThin: IconType;
export declare const PiArrowCircleUpThin: IconType;
export declare const PiArrowClockwiseThin: IconType;
export declare const PiArrowCounterClockwiseThin: IconType;
export declare const PiArrowDownLeftThin: IconType;
export declare const PiArrowDownRightThin: IconType;
export declare const PiArrowDownThin: IconType;
export declare const PiArrowElbowDownLeftThin: IconType;
export declare const PiArrowElbowDownRightThin: IconType;
export declare const PiArrowElbowLeftDownThin: IconType;
export declare const PiArrowElbowLeftThin: IconType;
export declare const PiArrowElbowLeftUpThin: IconType;
export declare const PiArrowElbowRightDownThin: IconType;
export declare const PiArrowElbowRightThin: IconType;
export declare const PiArrowElbowRightUpThin: IconType;
export declare const PiArrowElbowUpLeftThin: IconType;
export declare const PiArrowElbowUpRightThin: IconType;
export declare const PiArrowFatDownThin: IconType;
export declare const PiArrowFatLeftThin: IconType;
export declare const PiArrowFatLineDownThin: IconType;
export declare const PiArrowFatLineLeftThin: IconType;
export declare const PiArrowFatLineRightThin: IconType;
export declare const PiArrowFatLineUpThin: IconType;
export declare const PiArrowFatLinesDownThin: IconType;
export declare const PiArrowFatLinesLeftThin: IconType;
export declare const PiArrowFatLinesRightThin: IconType;
export declare const PiArrowFatLinesUpThin: IconType;
export declare const PiArrowFatRightThin: IconType;
export declare const PiArrowFatUpThin: IconType;
export declare const PiArrowLeftThin: IconType;
export declare const PiArrowLineDownLeftThin: IconType;
export declare const PiArrowLineDownRightThin: IconType;
export declare const PiArrowLineDownThin: IconType;
export declare const PiArrowLineLeftThin: IconType;
export declare const PiArrowLineRightThin: IconType;
export declare const PiArrowLineUpLeftThin: IconType;
export declare const PiArrowLineUpRightThin: IconType;
export declare const PiArrowLineUpThin: IconType;
export declare const PiArrowRightThin: IconType;
export declare const PiArrowSquareDownLeftThin: IconType;
export declare const PiArrowSquareDownRightThin: IconType;
export declare const PiArrowSquareDownThin: IconType;
export declare const PiArrowSquareInThin: IconType;
export declare const PiArrowSquareLeftThin: IconType;
export declare const PiArrowSquareOutThin: IconType;
export declare const PiArrowSquareRightThin: IconType;
export declare const PiArrowSquareUpLeftThin: IconType;
export declare const PiArrowSquareUpRightThin: IconType;
export declare const PiArrowSquareUpThin: IconType;
export declare const PiArrowUDownLeftThin: IconType;
export declare const PiArrowUDownRightThin: IconType;
export declare const PiArrowULeftDownThin: IconType;
export declare const PiArrowULeftUpThin: IconType;
export declare const PiArrowURightDownThin: IconType;
export declare const PiArrowURightUpThin: IconType;
export declare const PiArrowUUpLeftThin: IconType;
export declare const PiArrowUUpRightThin: IconType;
export declare const PiArrowUpLeftThin: IconType;
export declare const PiArrowUpRightThin: IconType;
export declare const PiArrowUpThin: IconType;
export declare const PiArrowsClockwiseThin: IconType;
export declare const PiArrowsCounterClockwiseThin: IconType;
export declare const PiArrowsDownUpThin: IconType;
export declare const PiArrowsHorizontalThin: IconType;
export declare const PiArrowsInCardinalThin: IconType;
export declare const PiArrowsInLineHorizontalThin: IconType;
export declare const PiArrowsInLineVerticalThin: IconType;
export declare const PiArrowsInSimpleThin: IconType;
export declare const PiArrowsInThin: IconType;
export declare const PiArrowsLeftRightThin: IconType;
export declare const PiArrowsMergeThin: IconType;
export declare const PiArrowsOutCardinalThin: IconType;
export declare const PiArrowsOutLineHorizontalThin: IconType;
export declare const PiArrowsOutLineVerticalThin: IconType;
export declare const PiArrowsOutSimpleThin: IconType;
export declare const PiArrowsOutThin: IconType;
export declare const PiArrowsSplitThin: IconType;
export declare const PiArrowsVerticalThin: IconType;
export declare const PiArticleMediumThin: IconType;
export declare const PiArticleNyTimesThin: IconType;
export declare const PiArticleThin: IconType;
export declare const PiAsclepiusThin: IconType;
export declare const PiAsteriskSimpleThin: IconType;
export declare const PiAsteriskThin: IconType;
export declare const PiAtThin: IconType;
export declare const PiAtomThin: IconType;
export declare const PiAvocadoThin: IconType;
export declare const PiAxeThin: IconType;
export declare const PiBabyCarriageThin: IconType;
export declare const PiBabyThin: IconType;
export declare const PiBackpackThin: IconType;
export declare const PiBackspaceThin: IconType;
export declare const PiBagSimpleThin: IconType;
export declare const PiBagThin: IconType;
export declare const PiBalloonThin: IconType;
export declare const PiBandaidsThin: IconType;
export declare const PiBankThin: IconType;
export declare const PiBarbellThin: IconType;
export declare const PiBarcodeThin: IconType;
export declare const PiBarnThin: IconType;
export declare const PiBarricadeThin: IconType;
export declare const PiBaseballCapThin: IconType;
export declare const PiBaseballHelmetThin: IconType;
export declare const PiBaseballThin: IconType;
export declare const PiBasketThin: IconType;
export declare const PiBasketballThin: IconType;
export declare const PiBathtubThin: IconType;
export declare const PiBatteryChargingThin: IconType;
export declare const PiBatteryChargingVerticalThin: IconType;
export declare const PiBatteryEmptyThin: IconType;
export declare const PiBatteryFullThin: IconType;
export declare const PiBatteryHighThin: IconType;
export declare const PiBatteryLowThin: IconType;
export declare const PiBatteryMediumThin: IconType;
export declare const PiBatteryPlusThin: IconType;
export declare const PiBatteryPlusVerticalThin: IconType;
export declare const PiBatteryVerticalEmptyThin: IconType;
export declare const PiBatteryVerticalFullThin: IconType;
export declare const PiBatteryVerticalHighThin: IconType;
export declare const PiBatteryVerticalLowThin: IconType;
export declare const PiBatteryVerticalMediumThin: IconType;
export declare const PiBatteryWarningThin: IconType;
export declare const PiBatteryWarningVerticalThin: IconType;
export declare const PiBeachBallThin: IconType;
export declare const PiBeanieThin: IconType;
export declare const PiBedThin: IconType;
export declare const PiBeerBottleThin: IconType;
export declare const PiBeerSteinThin: IconType;
export declare const PiBehanceLogoThin: IconType;
export declare const PiBellRingingThin: IconType;
export declare const PiBellSimpleRingingThin: IconType;
export declare const PiBellSimpleSlashThin: IconType;
export declare const PiBellSimpleThin: IconType;
export declare const PiBellSimpleZThin: IconType;
export declare const PiBellSlashThin: IconType;
export declare const PiBellThin: IconType;
export declare const PiBellZThin: IconType;
export declare const PiBeltThin: IconType;
export declare const PiBezierCurveThin: IconType;
export declare const PiBicycleThin: IconType;
export declare const PiBinaryThin: IconType;
export declare const PiBinocularsThin: IconType;
export declare const PiBiohazardThin: IconType;
export declare const PiBirdThin: IconType;
export declare const PiBlueprintThin: IconType;
export declare const PiBluetoothConnectedThin: IconType;
export declare const PiBluetoothSlashThin: IconType;
export declare const PiBluetoothThin: IconType;
export declare const PiBluetoothXThin: IconType;
export declare const PiBoatThin: IconType;
export declare const PiBombThin: IconType;
export declare const PiBoneThin: IconType;
export declare const PiBookBookmarkThin: IconType;
export declare const PiBookOpenTextThin: IconType;
export declare const PiBookOpenThin: IconType;
export declare const PiBookOpenUserThin: IconType;
export declare const PiBookThin: IconType;
export declare const PiBookmarkSimpleThin: IconType;
export declare const PiBookmarkThin: IconType;
export declare const PiBookmarksSimpleThin: IconType;
export declare const PiBookmarksThin: IconType;
export declare const PiBooksThin: IconType;
export declare const PiBootThin: IconType;
export declare const PiBoulesThin: IconType;
export declare const PiBoundingBoxThin: IconType;
export declare const PiBowlFoodThin: IconType;
export declare const PiBowlSteamThin: IconType;
export declare const PiBowlingBallThin: IconType;
export declare const PiBoxArrowDownThin: IconType;
export declare const PiBoxArrowUpThin: IconType;
export declare const PiBoxingGloveThin: IconType;
export declare const PiBracketsAngleThin: IconType;
export declare const PiBracketsCurlyThin: IconType;
export declare const PiBracketsRoundThin: IconType;
export declare const PiBracketsSquareThin: IconType;
export declare const PiBrainThin: IconType;
export declare const PiBrandyThin: IconType;
export declare const PiBreadThin: IconType;
export declare const PiBridgeThin: IconType;
export declare const PiBriefcaseMetalThin: IconType;
export declare const PiBriefcaseThin: IconType;
export declare const PiBroadcastThin: IconType;
export declare const PiBroomThin: IconType;
export declare const PiBrowserThin: IconType;
export declare const PiBrowsersThin: IconType;
export declare const PiBugBeetleThin: IconType;
export declare const PiBugDroidThin: IconType;
export declare const PiBugThin: IconType;
export declare const PiBuildingApartmentThin: IconType;
export declare const PiBuildingOfficeThin: IconType;
export declare const PiBuildingThin: IconType;
export declare const PiBuildingsThin: IconType;
export declare const PiBulldozerThin: IconType;
export declare const PiBusThin: IconType;
export declare const PiButterflyThin: IconType;
export declare const PiCableCarThin: IconType;
export declare const PiCactusThin: IconType;
export declare const PiCakeThin: IconType;
export declare const PiCalculatorThin: IconType;
export declare const PiCalendarBlankThin: IconType;
export declare const PiCalendarCheckThin: IconType;
export declare const PiCalendarDotThin: IconType;
export declare const PiCalendarDotsThin: IconType;
export declare const PiCalendarHeartThin: IconType;
export declare const PiCalendarMinusThin: IconType;
export declare const PiCalendarPlusThin: IconType;
export declare const PiCalendarSlashThin: IconType;
export declare const PiCalendarStarThin: IconType;
export declare const PiCalendarThin: IconType;
export declare const PiCalendarXThin: IconType;
export declare const PiCallBellThin: IconType;
export declare const PiCameraPlusThin: IconType;
export declare const PiCameraRotateThin: IconType;
export declare const PiCameraSlashThin: IconType;
export declare const PiCameraThin: IconType;
export declare const PiCampfireThin: IconType;
export declare const PiCarBatteryThin: IconType;
export declare const PiCarProfileThin: IconType;
export declare const PiCarSimpleThin: IconType;
export declare const PiCarThin: IconType;
export declare const PiCardholderThin: IconType;
export declare const PiCardsThin: IconType;
export declare const PiCardsThreeThin: IconType;
export declare const PiCaretCircleDoubleDownThin: IconType;
export declare const PiCaretCircleDoubleLeftThin: IconType;
export declare const PiCaretCircleDoubleRightThin: IconType;
export declare const PiCaretCircleDoubleUpThin: IconType;
export declare const PiCaretCircleDownThin: IconType;
export declare const PiCaretCircleLeftThin: IconType;
export declare const PiCaretCircleRightThin: IconType;
export declare const PiCaretCircleUpDownThin: IconType;
export declare const PiCaretCircleUpThin: IconType;
export declare const PiCaretDoubleDownThin: IconType;
export declare const PiCaretDoubleLeftThin: IconType;
export declare const PiCaretDoubleRightThin: IconType;
export declare const PiCaretDoubleUpThin: IconType;
export declare const PiCaretDownThin: IconType;
export declare const PiCaretLeftThin: IconType;
export declare const PiCaretLineDownThin: IconType;
export declare const PiCaretLineLeftThin: IconType;
export declare const PiCaretLineRightThin: IconType;
export declare const PiCaretLineUpThin: IconType;
export declare const PiCaretRightThin: IconType;
export declare const PiCaretUpDownThin: IconType;
export declare const PiCaretUpThin: IconType;
export declare const PiCarrotThin: IconType;
export declare const PiCashRegisterThin: IconType;
export declare const PiCassetteTapeThin: IconType;
export declare const PiCastleTurretThin: IconType;
export declare const PiCatThin: IconType;
export declare const PiCellSignalFullThin: IconType;
export declare const PiCellSignalHighThin: IconType;
export declare const PiCellSignalLowThin: IconType;
export declare const PiCellSignalMediumThin: IconType;
export declare const PiCellSignalNoneThin: IconType;
export declare const PiCellSignalSlashThin: IconType;
export declare const PiCellSignalXThin: IconType;
export declare const PiCellTowerThin: IconType;
export declare const PiCertificateThin: IconType;
export declare const PiChairThin: IconType;
export declare const PiChalkboardSimpleThin: IconType;
export declare const PiChalkboardTeacherThin: IconType;
export declare const PiChalkboardThin: IconType;
export declare const PiChampagneThin: IconType;
export declare const PiChargingStationThin: IconType;
export declare const PiChartBarHorizontalThin: IconType;
export declare const PiChartBarThin: IconType;
export declare const PiChartDonutThin: IconType;
export declare const PiChartLineDownThin: IconType;
export declare const PiChartLineThin: IconType;
export declare const PiChartLineUpThin: IconType;
export declare const PiChartPieSliceThin: IconType;
export declare const PiChartPieThin: IconType;
export declare const PiChartPolarThin: IconType;
export declare const PiChartScatterThin: IconType;
export declare const PiChatCenteredDotsThin: IconType;
export declare const PiChatCenteredSlashThin: IconType;
export declare const PiChatCenteredTextThin: IconType;
export declare const PiChatCenteredThin: IconType;
export declare const PiChatCircleDotsThin: IconType;
export declare const PiChatCircleSlashThin: IconType;
export declare const PiChatCircleTextThin: IconType;
export declare const PiChatCircleThin: IconType;
export declare const PiChatDotsThin: IconType;
export declare const PiChatSlashThin: IconType;
export declare const PiChatTeardropDotsThin: IconType;
export declare const PiChatTeardropSlashThin: IconType;
export declare const PiChatTeardropTextThin: IconType;
export declare const PiChatTeardropThin: IconType;
export declare const PiChatTextThin: IconType;
export declare const PiChatThin: IconType;
export declare const PiChatsCircleThin: IconType;
export declare const PiChatsTeardropThin: IconType;
export declare const PiChatsThin: IconType;
export declare const PiCheckCircleThin: IconType;
export declare const PiCheckFatThin: IconType;
export declare const PiCheckSquareOffsetThin: IconType;
export declare const PiCheckSquareThin: IconType;
export declare const PiCheckThin: IconType;
export declare const PiCheckerboardThin: IconType;
export declare const PiChecksThin: IconType;
export declare const PiCheersThin: IconType;
export declare const PiCheeseThin: IconType;
export declare const PiChefHatThin: IconType;
export declare const PiCherriesThin: IconType;
export declare const PiChurchThin: IconType;
export declare const PiCigaretteSlashThin: IconType;
export declare const PiCigaretteThin: IconType;
export declare const PiCircleDashedThin: IconType;
export declare const PiCircleHalfThin: IconType;
export declare const PiCircleHalfTiltThin: IconType;
export declare const PiCircleNotchThin: IconType;
export declare const PiCircleThin: IconType;
export declare const PiCirclesFourThin: IconType;
export declare const PiCirclesThreePlusThin: IconType;
export declare const PiCirclesThreeThin: IconType;
export declare const PiCircuitryThin: IconType;
export declare const PiCityThin: IconType;
export declare const PiClipboardTextThin: IconType;
export declare const PiClipboardThin: IconType;
export declare const PiClockAfternoonThin: IconType;
export declare const PiClockClockwiseThin: IconType;
export declare const PiClockCountdownThin: IconType;
export declare const PiClockCounterClockwiseThin: IconType;
export declare const PiClockThin: IconType;
export declare const PiClockUserThin: IconType;
export declare const PiClosedCaptioningThin: IconType;
export declare const PiCloudArrowDownThin: IconType;
export declare const PiCloudArrowUpThin: IconType;
export declare const PiCloudCheckThin: IconType;
export declare const PiCloudFogThin: IconType;
export declare const PiCloudLightningThin: IconType;
export declare const PiCloudMoonThin: IconType;
export declare const PiCloudRainThin: IconType;
export declare const PiCloudSlashThin: IconType;
export declare const PiCloudSnowThin: IconType;
export declare const PiCloudSunThin: IconType;
export declare const PiCloudThin: IconType;
export declare const PiCloudWarningThin: IconType;
export declare const PiCloudXThin: IconType;
export declare const PiCloverThin: IconType;
export declare const PiClubThin: IconType;
export declare const PiCoatHangerThin: IconType;
export declare const PiCodaLogoThin: IconType;
export declare const PiCodeBlockThin: IconType;
export declare const PiCodeSimpleThin: IconType;
export declare const PiCodeThin: IconType;
export declare const PiCodepenLogoThin: IconType;
export declare const PiCodesandboxLogoThin: IconType;
export declare const PiCoffeeBeanThin: IconType;
export declare const PiCoffeeThin: IconType;
export declare const PiCoinThin: IconType;
export declare const PiCoinVerticalThin: IconType;
export declare const PiCoinsThin: IconType;
export declare const PiColumnsPlusLeftThin: IconType;
export declare const PiColumnsPlusRightThin: IconType;
export declare const PiColumnsThin: IconType;
export declare const PiCommandThin: IconType;
export declare const PiCompassRoseThin: IconType;
export declare const PiCompassThin: IconType;
export declare const PiCompassToolThin: IconType;
export declare const PiComputerTowerThin: IconType;
export declare const PiConfettiThin: IconType;
export declare const PiContactlessPaymentThin: IconType;
export declare const PiControlThin: IconType;
export declare const PiCookieThin: IconType;
export declare const PiCookingPotThin: IconType;
export declare const PiCopySimpleThin: IconType;
export declare const PiCopyThin: IconType;
export declare const PiCopyleftThin: IconType;
export declare const PiCopyrightThin: IconType;
export declare const PiCornersInThin: IconType;
export declare const PiCornersOutThin: IconType;
export declare const PiCouchThin: IconType;
export declare const PiCourtBasketballThin: IconType;
export declare const PiCowThin: IconType;
export declare const PiCowboyHatThin: IconType;
export declare const PiCpuThin: IconType;
export declare const PiCraneThin: IconType;
export declare const PiCraneTowerThin: IconType;
export declare const PiCreditCardThin: IconType;
export declare const PiCricketThin: IconType;
export declare const PiCropThin: IconType;
export declare const PiCrossThin: IconType;
export declare const PiCrosshairSimpleThin: IconType;
export declare const PiCrosshairThin: IconType;
export declare const PiCrownCrossThin: IconType;
export declare const PiCrownSimpleThin: IconType;
export declare const PiCrownThin: IconType;
export declare const PiCubeFocusThin: IconType;
export declare const PiCubeThin: IconType;
export declare const PiCubeTransparentThin: IconType;
export declare const PiCurrencyBtcThin: IconType;
export declare const PiCurrencyCircleDollarThin: IconType;
export declare const PiCurrencyCnyThin: IconType;
export declare const PiCurrencyDollarSimpleThin: IconType;
export declare const PiCurrencyDollarThin: IconType;
export declare const PiCurrencyEthThin: IconType;
export declare const PiCurrencyEurThin: IconType;
export declare const PiCurrencyGbpThin: IconType;
export declare const PiCurrencyInrThin: IconType;
export declare const PiCurrencyJpyThin: IconType;
export declare const PiCurrencyKrwThin: IconType;
export declare const PiCurrencyKztThin: IconType;
export declare const PiCurrencyNgnThin: IconType;
export declare const PiCurrencyRubThin: IconType;
export declare const PiCursorClickThin: IconType;
export declare const PiCursorTextThin: IconType;
export declare const PiCursorThin: IconType;
export declare const PiCylinderThin: IconType;
export declare const PiDatabaseThin: IconType;
export declare const PiDeskThin: IconType;
export declare const PiDesktopThin: IconType;
export declare const PiDesktopTowerThin: IconType;
export declare const PiDetectiveThin: IconType;
export declare const PiDevToLogoThin: IconType;
export declare const PiDeviceMobileCameraThin: IconType;
export declare const PiDeviceMobileSlashThin: IconType;
export declare const PiDeviceMobileSpeakerThin: IconType;
export declare const PiDeviceMobileThin: IconType;
export declare const PiDeviceRotateThin: IconType;
export declare const PiDeviceTabletCameraThin: IconType;
export declare const PiDeviceTabletSpeakerThin: IconType;
export declare const PiDeviceTabletThin: IconType;
export declare const PiDevicesThin: IconType;
export declare const PiDiamondThin: IconType;
export declare const PiDiamondsFourThin: IconType;
export declare const PiDiceFiveThin: IconType;
export declare const PiDiceFourThin: IconType;
export declare const PiDiceOneThin: IconType;
export declare const PiDiceSixThin: IconType;
export declare const PiDiceThreeThin: IconType;
export declare const PiDiceTwoThin: IconType;
export declare const PiDiscThin: IconType;
export declare const PiDiscoBallThin: IconType;
export declare const PiDiscordLogoThin: IconType;
export declare const PiDivideThin: IconType;
export declare const PiDnaThin: IconType;
export declare const PiDogThin: IconType;
export declare const PiDoorOpenThin: IconType;
export declare const PiDoorThin: IconType;
export declare const PiDotOutlineThin: IconType;
export declare const PiDotThin: IconType;
export declare const PiDotsNineThin: IconType;
export declare const PiDotsSixThin: IconType;
export declare const PiDotsSixVerticalThin: IconType;
export declare const PiDotsThreeCircleThin: IconType;
export declare const PiDotsThreeCircleVerticalThin: IconType;
export declare const PiDotsThreeOutlineThin: IconType;
export declare const PiDotsThreeOutlineVerticalThin: IconType;
export declare const PiDotsThreeThin: IconType;
export declare const PiDotsThreeVerticalThin: IconType;
export declare const PiDownloadSimpleThin: IconType;
export declare const PiDownloadThin: IconType;
export declare const PiDressThin: IconType;
export declare const PiDresserThin: IconType;
export declare const PiDribbbleLogoThin: IconType;
export declare const PiDroneThin: IconType;
export declare const PiDropHalfBottomThin: IconType;
export declare const PiDropHalfThin: IconType;
export declare const PiDropSimpleThin: IconType;
export declare const PiDropSlashThin: IconType;
export declare const PiDropThin: IconType;
export declare const PiDropboxLogoThin: IconType;
export declare const PiEarSlashThin: IconType;
export declare const PiEarThin: IconType;
export declare const PiEggCrackThin: IconType;
export declare const PiEggThin: IconType;
export declare const PiEjectSimpleThin: IconType;
export declare const PiEjectThin: IconType;
export declare const PiElevatorThin: IconType;
export declare const PiEmptyThin: IconType;
export declare const PiEngineThin: IconType;
export declare const PiEnvelopeOpenThin: IconType;
export declare const PiEnvelopeSimpleOpenThin: IconType;
export declare const PiEnvelopeSimpleThin: IconType;
export declare const PiEnvelopeThin: IconType;
export declare const PiEqualizerThin: IconType;
export declare const PiEqualsThin: IconType;
export declare const PiEraserThin: IconType;
export declare const PiEscalatorDownThin: IconType;
export declare const PiEscalatorUpThin: IconType;
export declare const PiExamThin: IconType;
export declare const PiExclamationMarkThin: IconType;
export declare const PiExcludeSquareThin: IconType;
export declare const PiExcludeThin: IconType;
export declare const PiExportThin: IconType;
export declare const PiEyeClosedThin: IconType;
export declare const PiEyeSlashThin: IconType;
export declare const PiEyeThin: IconType;
export declare const PiEyedropperSampleThin: IconType;
export declare const PiEyedropperThin: IconType;
export declare const PiEyeglassesThin: IconType;
export declare const PiEyesThin: IconType;
export declare const PiFaceMaskThin: IconType;
export declare const PiFacebookLogoThin: IconType;
export declare const PiFactoryThin: IconType;
export declare const PiFadersHorizontalThin: IconType;
export declare const PiFadersThin: IconType;
export declare const PiFalloutShelterThin: IconType;
export declare const PiFanThin: IconType;
export declare const PiFarmThin: IconType;
export declare const PiFastForwardCircleThin: IconType;
export declare const PiFastForwardThin: IconType;
export declare const PiFeatherThin: IconType;
export declare const PiFediverseLogoThin: IconType;
export declare const PiFigmaLogoThin: IconType;
export declare const PiFileArchiveThin: IconType;
export declare const PiFileArrowDownThin: IconType;
export declare const PiFileArrowUpThin: IconType;
export declare const PiFileAudioThin: IconType;
export declare const PiFileCSharpThin: IconType;
export declare const PiFileCThin: IconType;
export declare const PiFileCloudThin: IconType;
export declare const PiFileCodeThin: IconType;
export declare const PiFileCppThin: IconType;
export declare const PiFileCssThin: IconType;
export declare const PiFileCsvThin: IconType;
export declare const PiFileDashedThin: IconType;
export declare const PiFileDocThin: IconType;
export declare const PiFileHtmlThin: IconType;
export declare const PiFileImageThin: IconType;
export declare const PiFileIniThin: IconType;
export declare const PiFileJpgThin: IconType;
export declare const PiFileJsThin: IconType;
export declare const PiFileJsxThin: IconType;
export declare const PiFileLockThin: IconType;
export declare const PiFileMagnifyingGlassThin: IconType;
export declare const PiFileMdThin: IconType;
export declare const PiFileMinusThin: IconType;
export declare const PiFilePdfThin: IconType;
export declare const PiFilePlusThin: IconType;
export declare const PiFilePngThin: IconType;
export declare const PiFilePptThin: IconType;
export declare const PiFilePyThin: IconType;
export declare const PiFileRsThin: IconType;
export declare const PiFileSqlThin: IconType;
export declare const PiFileSvgThin: IconType;
export declare const PiFileTextThin: IconType;
export declare const PiFileThin: IconType;
export declare const PiFileTsThin: IconType;
export declare const PiFileTsxThin: IconType;
export declare const PiFileTxtThin: IconType;
export declare const PiFileVideoThin: IconType;
export declare const PiFileVueThin: IconType;
export declare const PiFileXThin: IconType;
export declare const PiFileXlsThin: IconType;
export declare const PiFileZipThin: IconType;
export declare const PiFilesThin: IconType;
export declare const PiFilmReelThin: IconType;
export declare const PiFilmScriptThin: IconType;
export declare const PiFilmSlateThin: IconType;
export declare const PiFilmStripThin: IconType;
export declare const PiFingerprintSimpleThin: IconType;
export declare const PiFingerprintThin: IconType;
export declare const PiFinnTheHumanThin: IconType;
export declare const PiFireExtinguisherThin: IconType;
export declare const PiFireSimpleThin: IconType;
export declare const PiFireThin: IconType;
export declare const PiFireTruckThin: IconType;
export declare const PiFirstAidKitThin: IconType;
export declare const PiFirstAidThin: IconType;
export declare const PiFishSimpleThin: IconType;
export declare const PiFishThin: IconType;
export declare const PiFlagBannerFoldThin: IconType;
export declare const PiFlagBannerThin: IconType;
export declare const PiFlagCheckeredThin: IconType;
export declare const PiFlagPennantThin: IconType;
export declare const PiFlagThin: IconType;
export declare const PiFlameThin: IconType;
export declare const PiFlashlightThin: IconType;
export declare const PiFlaskThin: IconType;
export declare const PiFlipHorizontalThin: IconType;
export declare const PiFlipVerticalThin: IconType;
export declare const PiFloppyDiskBackThin: IconType;
export declare const PiFloppyDiskThin: IconType;
export declare const PiFlowArrowThin: IconType;
export declare const PiFlowerLotusThin: IconType;
export declare const PiFlowerThin: IconType;
export declare const PiFlowerTulipThin: IconType;
export declare const PiFlyingSaucerThin: IconType;
export declare const PiFolderDashedThin: IconType;
export declare const PiFolderLockThin: IconType;
export declare const PiFolderMinusThin: IconType;
export declare const PiFolderOpenThin: IconType;
export declare const PiFolderPlusThin: IconType;
export declare const PiFolderSimpleDashedThin: IconType;
export declare const PiFolderSimpleLockThin: IconType;
export declare const PiFolderSimpleMinusThin: IconType;
export declare const PiFolderSimplePlusThin: IconType;
export declare const PiFolderSimpleStarThin: IconType;
export declare const PiFolderSimpleThin: IconType;
export declare const PiFolderSimpleUserThin: IconType;
export declare const PiFolderStarThin: IconType;
export declare const PiFolderThin: IconType;
export declare const PiFolderUserThin: IconType;
export declare const PiFoldersThin: IconType;
export declare const PiFootballHelmetThin: IconType;
export declare const PiFootballThin: IconType;
export declare const PiFootprintsThin: IconType;
export declare const PiForkKnifeThin: IconType;
export declare const PiFourKThin: IconType;
export declare const PiFrameCornersThin: IconType;
export declare const PiFramerLogoThin: IconType;
export declare const PiFunctionThin: IconType;
export declare const PiFunnelSimpleThin: IconType;
export declare const PiFunnelSimpleXThin: IconType;
export declare const PiFunnelThin: IconType;
export declare const PiFunnelXThin: IconType;
export declare const PiGameControllerThin: IconType;
export declare const PiGarageThin: IconType;
export declare const PiGasCanThin: IconType;
export declare const PiGasPumpThin: IconType;
export declare const PiGaugeThin: IconType;
export declare const PiGavelThin: IconType;
export declare const PiGearFineThin: IconType;
export declare const PiGearSixThin: IconType;
export declare const PiGearThin: IconType;
export declare const PiGenderFemaleThin: IconType;
export declare const PiGenderIntersexThin: IconType;
export declare const PiGenderMaleThin: IconType;
export declare const PiGenderNeuterThin: IconType;
export declare const PiGenderNonbinaryThin: IconType;
export declare const PiGenderTransgenderThin: IconType;
export declare const PiGhostThin: IconType;
export declare const PiGifThin: IconType;
export declare const PiGiftThin: IconType;
export declare const PiGitBranchThin: IconType;
export declare const PiGitCommitThin: IconType;
export declare const PiGitDiffThin: IconType;
export declare const PiGitForkThin: IconType;
export declare const PiGitMergeThin: IconType;
export declare const PiGitPullRequestThin: IconType;
export declare const PiGithubLogoThin: IconType;
export declare const PiGitlabLogoSimpleThin: IconType;
export declare const PiGitlabLogoThin: IconType;
export declare const PiGlobeHemisphereEastThin: IconType;
export declare const PiGlobeHemisphereWestThin: IconType;
export declare const PiGlobeSimpleThin: IconType;
export declare const PiGlobeSimpleXThin: IconType;
export declare const PiGlobeStandThin: IconType;
export declare const PiGlobeThin: IconType;
export declare const PiGlobeXThin: IconType;
export declare const PiGogglesThin: IconType;
export declare const PiGolfThin: IconType;
export declare const PiGoodreadsLogoThin: IconType;
export declare const PiGoogleCardboardLogoThin: IconType;
export declare const PiGoogleChromeLogoThin: IconType;
export declare const PiGoogleDriveLogoThin: IconType;
export declare const PiGoogleLogoThin: IconType;
export declare const PiGooglePhotosLogoThin: IconType;
export declare const PiGooglePlayLogoThin: IconType;
export declare const PiGooglePodcastsLogoThin: IconType;
export declare const PiGpsFixThin: IconType;
export declare const PiGpsSlashThin: IconType;
export declare const PiGpsThin: IconType;
export declare const PiGradientThin: IconType;
export declare const PiGraduationCapThin: IconType;
export declare const PiGrainsSlashThin: IconType;
export declare const PiGrainsThin: IconType;
export declare const PiGraphThin: IconType;
export declare const PiGraphicsCardThin: IconType;
export declare const PiGreaterThanOrEqualThin: IconType;
export declare const PiGreaterThanThin: IconType;
export declare const PiGridFourThin: IconType;
export declare const PiGridNineThin: IconType;
export declare const PiGuitarThin: IconType;
export declare const PiHairDryerThin: IconType;
export declare const PiHamburgerThin: IconType;
export declare const PiHammerThin: IconType;
export declare const PiHandArrowDownThin: IconType;
export declare const PiHandArrowUpThin: IconType;
export declare const PiHandCoinsThin: IconType;
export declare const PiHandDepositThin: IconType;
export declare const PiHandEyeThin: IconType;
export declare const PiHandFistThin: IconType;
export declare const PiHandGrabbingThin: IconType;
export declare const PiHandHeartThin: IconType;
export declare const PiHandPalmThin: IconType;
export declare const PiHandPeaceThin: IconType;
export declare const PiHandPointingThin: IconType;
export declare const PiHandSoapThin: IconType;
export declare const PiHandSwipeLeftThin: IconType;
export declare const PiHandSwipeRightThin: IconType;
export declare const PiHandTapThin: IconType;
export declare const PiHandThin: IconType;
export declare const PiHandWavingThin: IconType;
export declare const PiHandWithdrawThin: IconType;
export declare const PiHandbagSimpleThin: IconType;
export declare const PiHandbagThin: IconType;
export declare const PiHandsClappingThin: IconType;
export declare const PiHandsPrayingThin: IconType;
export declare const PiHandshakeThin: IconType;
export declare const PiHardDriveThin: IconType;
export declare const PiHardDrivesThin: IconType;
export declare const PiHardHatThin: IconType;
export declare const PiHashStraightThin: IconType;
export declare const PiHashThin: IconType;
export declare const PiHeadCircuitThin: IconType;
export declare const PiHeadlightsThin: IconType;
export declare const PiHeadphonesThin: IconType;
export declare const PiHeadsetThin: IconType;
export declare const PiHeartBreakThin: IconType;
export declare const PiHeartHalfThin: IconType;
export declare const PiHeartStraightBreakThin: IconType;
export declare const PiHeartStraightThin: IconType;
export declare const PiHeartThin: IconType;
export declare const PiHeartbeatThin: IconType;
export declare const PiHexagonThin: IconType;
export declare const PiHighDefinitionThin: IconType;
export declare const PiHighHeelThin: IconType;
export declare const PiHighlighterCircleThin: IconType;
export declare const PiHighlighterThin: IconType;
export declare const PiHockeyThin: IconType;
export declare const PiHoodieThin: IconType;
export declare const PiHorseThin: IconType;
export declare const PiHospitalThin: IconType;
export declare const PiHourglassHighThin: IconType;
export declare const PiHourglassLowThin: IconType;
export declare const PiHourglassMediumThin: IconType;
export declare const PiHourglassSimpleHighThin: IconType;
export declare const PiHourglassSimpleLowThin: IconType;
export declare const PiHourglassSimpleMediumThin: IconType;
export declare const PiHourglassSimpleThin: IconType;
export declare const PiHourglassThin: IconType;
export declare const PiHouseLineThin: IconType;
export declare const PiHouseSimpleThin: IconType;
export declare const PiHouseThin: IconType;
export declare const PiHurricaneThin: IconType;
export declare const PiIceCreamThin: IconType;
export declare const PiIdentificationBadgeThin: IconType;
export declare const PiIdentificationCardThin: IconType;
export declare const PiImageBrokenThin: IconType;
export declare const PiImageSquareThin: IconType;
export declare const PiImageThin: IconType;
export declare const PiImagesSquareThin: IconType;
export declare const PiImagesThin: IconType;
export declare const PiInfinityThin: IconType;
export declare const PiInfoThin: IconType;
export declare const PiInstagramLogoThin: IconType;
export declare const PiIntersectSquareThin: IconType;
export declare const PiIntersectThin: IconType;
export declare const PiIntersectThreeThin: IconType;
export declare const PiIntersectionThin: IconType;
export declare const PiInvoiceThin: IconType;
export declare const PiIslandThin: IconType;
export declare const PiJarLabelThin: IconType;
export declare const PiJarThin: IconType;
export declare const PiJeepThin: IconType;
export declare const PiJoystickThin: IconType;
export declare const PiKanbanThin: IconType;
export declare const PiKeyReturnThin: IconType;
export declare const PiKeyThin: IconType;
export declare const PiKeyboardThin: IconType;
export declare const PiKeyholeThin: IconType;
export declare const PiKnifeThin: IconType;
export declare const PiLadderSimpleThin: IconType;
export declare const PiLadderThin: IconType;
export declare const PiLampPendantThin: IconType;
export declare const PiLampThin: IconType;
export declare const PiLaptopThin: IconType;
export declare const PiLassoThin: IconType;
export declare const PiLastfmLogoThin: IconType;
export declare const PiLayoutThin: IconType;
export declare const PiLeafThin: IconType;
export declare const PiLecternThin: IconType;
export declare const PiLegoSmileyThin: IconType;
export declare const PiLegoThin: IconType;
export declare const PiLessThanOrEqualThin: IconType;
export declare const PiLessThanThin: IconType;
export declare const PiLetterCircleHThin: IconType;
export declare const PiLetterCirclePThin: IconType;
export declare const PiLetterCircleVThin: IconType;
export declare const PiLifebuoyThin: IconType;
export declare const PiLightbulbFilamentThin: IconType;
export declare const PiLightbulbThin: IconType;
export declare const PiLighthouseThin: IconType;
export declare const PiLightningAThin: IconType;
export declare const PiLightningSlashThin: IconType;
export declare const PiLightningThin: IconType;
export declare const PiLineSegmentThin: IconType;
export declare const PiLineSegmentsThin: IconType;
export declare const PiLineVerticalThin: IconType;
export declare const PiLinkBreakThin: IconType;
export declare const PiLinkSimpleBreakThin: IconType;
export declare const PiLinkSimpleHorizontalBreakThin: IconType;
export declare const PiLinkSimpleHorizontalThin: IconType;
export declare const PiLinkSimpleThin: IconType;
export declare const PiLinkThin: IconType;
export declare const PiLinkedinLogoThin: IconType;
export declare const PiLinktreeLogoThin: IconType;
export declare const PiLinuxLogoThin: IconType;
export declare const PiListBulletsThin: IconType;
export declare const PiListChecksThin: IconType;
export declare const PiListDashesThin: IconType;
export declare const PiListHeartThin: IconType;
export declare const PiListMagnifyingGlassThin: IconType;
export declare const PiListNumbersThin: IconType;
export declare const PiListPlusThin: IconType;
export declare const PiListStarThin: IconType;
export declare const PiListThin: IconType;
export declare const PiLockKeyOpenThin: IconType;
export declare const PiLockKeyThin: IconType;
export declare const PiLockLaminatedOpenThin: IconType;
export declare const PiLockLaminatedThin: IconType;
export declare const PiLockOpenThin: IconType;
export declare const PiLockSimpleOpenThin: IconType;
export declare const PiLockSimpleThin: IconType;
export declare const PiLockThin: IconType;
export declare const PiLockersThin: IconType;
export declare const PiLogThin: IconType;
export declare const PiMagicWandThin: IconType;
export declare const PiMagnetStraightThin: IconType;
export declare const PiMagnetThin: IconType;
export declare const PiMagnifyingGlassMinusThin: IconType;
export declare const PiMagnifyingGlassPlusThin: IconType;
export declare const PiMagnifyingGlassThin: IconType;
export declare const PiMailboxThin: IconType;
export declare const PiMapPinAreaThin: IconType;
export declare const PiMapPinLineThin: IconType;
export declare const PiMapPinPlusThin: IconType;
export declare const PiMapPinSimpleAreaThin: IconType;
export declare const PiMapPinSimpleLineThin: IconType;
export declare const PiMapPinSimpleThin: IconType;
export declare const PiMapPinThin: IconType;
export declare const PiMapTrifoldThin: IconType;
export declare const PiMarkdownLogoThin: IconType;
export declare const PiMarkerCircleThin: IconType;
export declare const PiMartiniThin: IconType;
export declare const PiMaskHappyThin: IconType;
export declare const PiMaskSadThin: IconType;
export declare const PiMastodonLogoThin: IconType;
export declare const PiMathOperationsThin: IconType;
export declare const PiMatrixLogoThin: IconType;
export declare const PiMedalMilitaryThin: IconType;
export declare const PiMedalThin: IconType;
export declare const PiMediumLogoThin: IconType;
export declare const PiMegaphoneSimpleThin: IconType;
export declare const PiMegaphoneThin: IconType;
export declare const PiMemberOfThin: IconType;
export declare const PiMemoryThin: IconType;
export declare const PiMessengerLogoThin: IconType;
export declare const PiMetaLogoThin: IconType;
export declare const PiMeteorThin: IconType;
export declare const PiMetronomeThin: IconType;
export declare const PiMicrophoneSlashThin: IconType;
export declare const PiMicrophoneStageThin: IconType;
export declare const PiMicrophoneThin: IconType;
export declare const PiMicroscopeThin: IconType;
export declare const PiMicrosoftExcelLogoThin: IconType;
export declare const PiMicrosoftOutlookLogoThin: IconType;
export declare const PiMicrosoftPowerpointLogoThin: IconType;
export declare const PiMicrosoftTeamsLogoThin: IconType;
export declare const PiMicrosoftWordLogoThin: IconType;
export declare const PiMinusCircleThin: IconType;
export declare const PiMinusSquareThin: IconType;
export declare const PiMinusThin: IconType;
export declare const PiMoneyThin: IconType;
export declare const PiMoneyWavyThin: IconType;
export declare const PiMonitorArrowUpThin: IconType;
export declare const PiMonitorPlayThin: IconType;
export declare const PiMonitorThin: IconType;
export declare const PiMoonStarsThin: IconType;
export declare const PiMoonThin: IconType;
export declare const PiMopedFrontThin: IconType;
export declare const PiMopedThin: IconType;
export declare const PiMosqueThin: IconType;
export declare const PiMotorcycleThin: IconType;
export declare const PiMountainsThin: IconType;
export declare const PiMouseLeftClickThin: IconType;
export declare const PiMouseMiddleClickThin: IconType;
export declare const PiMouseRightClickThin: IconType;
export declare const PiMouseScrollThin: IconType;
export declare const PiMouseSimpleThin: IconType;
export declare const PiMouseThin: IconType;
export declare const PiMusicNoteSimpleThin: IconType;
export declare const PiMusicNoteThin: IconType;
export declare const PiMusicNotesMinusThin: IconType;
export declare const PiMusicNotesPlusThin: IconType;
export declare const PiMusicNotesSimpleThin: IconType;
export declare const PiMusicNotesThin: IconType;
export declare const PiNavigationArrowThin: IconType;
export declare const PiNeedleThin: IconType;
export declare const PiNetworkSlashThin: IconType;
export declare const PiNetworkThin: IconType;
export declare const PiNetworkXThin: IconType;
export declare const PiNewspaperClippingThin: IconType;
export declare const PiNewspaperThin: IconType;
export declare const PiNotEqualsThin: IconType;
export declare const PiNotMemberOfThin: IconType;
export declare const PiNotSubsetOfThin: IconType;
export declare const PiNotSupersetOfThin: IconType;
export declare const PiNotchesThin: IconType;
export declare const PiNoteBlankThin: IconType;
export declare const PiNotePencilThin: IconType;
export declare const PiNoteThin: IconType;
export declare const PiNotebookThin: IconType;
export declare const PiNotepadThin: IconType;
export declare const PiNotificationThin: IconType;
export declare const PiNotionLogoThin: IconType;
export declare const PiNuclearPlantThin: IconType;
export declare const PiNumberCircleEightThin: IconType;
export declare const PiNumberCircleFiveThin: IconType;
export declare const PiNumberCircleFourThin: IconType;
export declare const PiNumberCircleNineThin: IconType;
export declare const PiNumberCircleOneThin: IconType;
export declare const PiNumberCircleSevenThin: IconType;
export declare const PiNumberCircleSixThin: IconType;
export declare const PiNumberCircleThreeThin: IconType;
export declare const PiNumberCircleTwoThin: IconType;
export declare const PiNumberCircleZeroThin: IconType;
export declare const PiNumberEightThin: IconType;
export declare const PiNumberFiveThin: IconType;
export declare const PiNumberFourThin: IconType;
export declare const PiNumberNineThin: IconType;
export declare const PiNumberOneThin: IconType;
export declare const PiNumberSevenThin: IconType;
export declare const PiNumberSixThin: IconType;
export declare const PiNumberSquareEightThin: IconType;
export declare const PiNumberSquareFiveThin: IconType;
export declare const PiNumberSquareFourThin: IconType;
export declare const PiNumberSquareNineThin: IconType;
export declare const PiNumberSquareOneThin: IconType;
export declare const PiNumberSquareSevenThin: IconType;
export declare const PiNumberSquareSixThin: IconType;
export declare const PiNumberSquareThreeThin: IconType;
export declare const PiNumberSquareTwoThin: IconType;
export declare const PiNumberSquareZeroThin: IconType;
export declare const PiNumberThreeThin: IconType;
export declare const PiNumberTwoThin: IconType;
export declare const PiNumberZeroThin: IconType;
export declare const PiNumpadThin: IconType;
export declare const PiNutThin: IconType;
export declare const PiNyTimesLogoThin: IconType;
export declare const PiOctagonThin: IconType;
export declare const PiOfficeChairThin: IconType;
export declare const PiOnigiriThin: IconType;
export declare const PiOpenAiLogoThin: IconType;
export declare const PiOptionThin: IconType;
export declare const PiOrangeSliceThin: IconType;
export declare const PiOrangeThin: IconType;
export declare const PiOvenThin: IconType;
export declare const PiPackageThin: IconType;
export declare const PiPaintBrushBroadThin: IconType;
export declare const PiPaintBrushHouseholdThin: IconType;
export declare const PiPaintBrushThin: IconType;
export declare const PiPaintBucketThin: IconType;
export declare const PiPaintRollerThin: IconType;
export declare const PiPaletteThin: IconType;
export declare const PiPanoramaThin: IconType;
export declare const PiPantsThin: IconType;
export declare const PiPaperPlaneRightThin: IconType;
export declare const PiPaperPlaneThin: IconType;
export declare const PiPaperPlaneTiltThin: IconType;
export declare const PiPaperclipHorizontalThin: IconType;
export declare const PiPaperclipThin: IconType;
export declare const PiParachuteThin: IconType;
export declare const PiParagraphThin: IconType;
export declare const PiParallelogramThin: IconType;
export declare const PiParkThin: IconType;
export declare const PiPasswordThin: IconType;
export declare const PiPathThin: IconType;
export declare const PiPatreonLogoThin: IconType;
export declare const PiPauseCircleThin: IconType;
export declare const PiPauseThin: IconType;
export declare const PiPawPrintThin: IconType;
export declare const PiPaypalLogoThin: IconType;
export declare const PiPeaceThin: IconType;
export declare const PiPenNibStraightThin: IconType;
export declare const PiPenNibThin: IconType;
export declare const PiPenThin: IconType;
export declare const PiPencilCircleThin: IconType;
export declare const PiPencilLineThin: IconType;
export declare const PiPencilRulerThin: IconType;
export declare const PiPencilSimpleLineThin: IconType;
export declare const PiPencilSimpleSlashThin: IconType;
export declare const PiPencilSimpleThin: IconType;
export declare const PiPencilSlashThin: IconType;
export declare const PiPencilThin: IconType;
export declare const PiPentagonThin: IconType;
export declare const PiPentagramThin: IconType;
export declare const PiPepperThin: IconType;
export declare const PiPercentThin: IconType;
export declare const PiPersonArmsSpreadThin: IconType;
export declare const PiPersonSimpleBikeThin: IconType;
export declare const PiPersonSimpleCircleThin: IconType;
export declare const PiPersonSimpleHikeThin: IconType;
export declare const PiPersonSimpleRunThin: IconType;
export declare const PiPersonSimpleSkiThin: IconType;
export declare const PiPersonSimpleSnowboardThin: IconType;
export declare const PiPersonSimpleSwimThin: IconType;
export declare const PiPersonSimpleTaiChiThin: IconType;
export declare const PiPersonSimpleThin: IconType;
export declare const PiPersonSimpleThrowThin: IconType;
export declare const PiPersonSimpleWalkThin: IconType;
export declare const PiPersonThin: IconType;
export declare const PiPerspectiveThin: IconType;
export declare const PiPhoneCallThin: IconType;
export declare const PiPhoneDisconnectThin: IconType;
export declare const PiPhoneIncomingThin: IconType;
export declare const PiPhoneListThin: IconType;
export declare const PiPhoneOutgoingThin: IconType;
export declare const PiPhonePauseThin: IconType;
export declare const PiPhonePlusThin: IconType;
export declare const PiPhoneSlashThin: IconType;
export declare const PiPhoneThin: IconType;
export declare const PiPhoneTransferThin: IconType;
export declare const PiPhoneXThin: IconType;
export declare const PiPhosphorLogoThin: IconType;
export declare const PiPiThin: IconType;
export declare const PiPianoKeysThin: IconType;
export declare const PiPicnicTableThin: IconType;
export declare const PiPictureInPictureThin: IconType;
export declare const PiPiggyBankThin: IconType;
export declare const PiPillThin: IconType;
export declare const PiPingPongThin: IconType;
export declare const PiPintGlassThin: IconType;
export declare const PiPinterestLogoThin: IconType;
export declare const PiPinwheelThin: IconType;
export declare const PiPipeThin: IconType;
export declare const PiPipeWrenchThin: IconType;
export declare const PiPixLogoThin: IconType;
export declare const PiPizzaThin: IconType;
export declare const PiPlaceholderThin: IconType;
export declare const PiPlanetThin: IconType;
export declare const PiPlantThin: IconType;
export declare const PiPlayCircleThin: IconType;
export declare const PiPlayPauseThin: IconType;
export declare const PiPlayThin: IconType;
export declare const PiPlaylistThin: IconType;
export declare const PiPlugChargingThin: IconType;
export declare const PiPlugThin: IconType;
export declare const PiPlugsConnectedThin: IconType;
export declare const PiPlugsThin: IconType;
export declare const PiPlusCircleThin: IconType;
export declare const PiPlusMinusThin: IconType;
export declare const PiPlusSquareThin: IconType;
export declare const PiPlusThin: IconType;
export declare const PiPokerChipThin: IconType;
export declare const PiPoliceCarThin: IconType;
export declare const PiPolygonThin: IconType;
export declare const PiPopcornThin: IconType;
export declare const PiPopsicleThin: IconType;
export declare const PiPottedPlantThin: IconType;
export declare const PiPowerThin: IconType;
export declare const PiPrescriptionThin: IconType;
export declare const PiPresentationChartThin: IconType;
export declare const PiPresentationThin: IconType;
export declare const PiPrinterThin: IconType;
export declare const PiProhibitInsetThin: IconType;
export declare const PiProhibitThin: IconType;
export declare const PiProjectorScreenChartThin: IconType;
export declare const PiProjectorScreenThin: IconType;
export declare const PiPulseThin: IconType;
export declare const PiPushPinSimpleSlashThin: IconType;
export declare const PiPushPinSimpleThin: IconType;
export declare const PiPushPinSlashThin: IconType;
export declare const PiPushPinThin: IconType;
export declare const PiPuzzlePieceThin: IconType;
export declare const PiQrCodeThin: IconType;
export declare const PiQuestionMarkThin: IconType;
export declare const PiQuestionThin: IconType;
export declare const PiQueueThin: IconType;
export declare const PiQuotesThin: IconType;
export declare const PiRabbitThin: IconType;
export declare const PiRacquetThin: IconType;
export declare const PiRadicalThin: IconType;
export declare const PiRadioButtonThin: IconType;
export declare const PiRadioThin: IconType;
export declare const PiRadioactiveThin: IconType;
export declare const PiRainbowCloudThin: IconType;
export declare const PiRainbowThin: IconType;
export declare const PiRankingThin: IconType;
export declare const PiReadCvLogoThin: IconType;
export declare const PiReceiptThin: IconType;
export declare const PiReceiptXThin: IconType;
export declare const PiRecordThin: IconType;
export declare const PiRectangleDashedThin: IconType;
export declare const PiRectangleThin: IconType;
export declare const PiRecycleThin: IconType;
export declare const PiRedditLogoThin: IconType;
export declare const PiRepeatOnceThin: IconType;
export declare const PiRepeatThin: IconType;
export declare const PiReplitLogoThin: IconType;
export declare const PiResizeThin: IconType;
export declare const PiRewindCircleThin: IconType;
export declare const PiRewindThin: IconType;
export declare const PiRoadHorizonThin: IconType;
export declare const PiRobotThin: IconType;
export declare const PiRocketLaunchThin: IconType;
export declare const PiRocketThin: IconType;
export declare const PiRowsPlusBottomThin: IconType;
export declare const PiRowsPlusTopThin: IconType;
export declare const PiRowsThin: IconType;
export declare const PiRssSimpleThin: IconType;
export declare const PiRssThin: IconType;
export declare const PiRugThin: IconType;
export declare const PiRulerThin: IconType;
export declare const PiSailboatThin: IconType;
export declare const PiScalesThin: IconType;
export declare const PiScanSmileyThin: IconType;
export declare const PiScanThin: IconType;
export declare const PiScissorsThin: IconType;
export declare const PiScooterThin: IconType;
export declare const PiScreencastThin: IconType;
export declare const PiScrewdriverThin: IconType;
export declare const PiScribbleLoopThin: IconType;
export declare const PiScribbleThin: IconType;
export declare const PiScrollThin: IconType;
export declare const PiSealCheckThin: IconType;
export declare const PiSealPercentThin: IconType;
export declare const PiSealQuestionThin: IconType;
export declare const PiSealThin: IconType;
export declare const PiSealWarningThin: IconType;
export declare const PiSeatThin: IconType;
export declare const PiSeatbeltThin: IconType;
export declare const PiSecurityCameraThin: IconType;
export declare const PiSelectionAllThin: IconType;
export declare const PiSelectionBackgroundThin: IconType;
export declare const PiSelectionForegroundThin: IconType;
export declare const PiSelectionInverseThin: IconType;
export declare const PiSelectionPlusThin: IconType;
export declare const PiSelectionSlashThin: IconType;
export declare const PiSelectionThin: IconType;
export declare const PiShapesThin: IconType;
export declare const PiShareFatThin: IconType;
export declare const PiShareNetworkThin: IconType;
export declare const PiShareThin: IconType;
export declare const PiShieldCheckThin: IconType;
export declare const PiShieldCheckeredThin: IconType;
export declare const PiShieldChevronThin: IconType;
export declare const PiShieldPlusThin: IconType;
export declare const PiShieldSlashThin: IconType;
export declare const PiShieldStarThin: IconType;
export declare const PiShieldThin: IconType;
export declare const PiShieldWarningThin: IconType;
export declare const PiShippingContainerThin: IconType;
export declare const PiShirtFoldedThin: IconType;
export declare const PiShootingStarThin: IconType;
export declare const PiShoppingBagOpenThin: IconType;
export declare const PiShoppingBagThin: IconType;
export declare const PiShoppingCartSimpleThin: IconType;
export declare const PiShoppingCartThin: IconType;
export declare const PiShovelThin: IconType;
export declare const PiShowerThin: IconType;
export declare const PiShrimpThin: IconType;
export declare const PiShuffleAngularThin: IconType;
export declare const PiShuffleSimpleThin: IconType;
export declare const PiShuffleThin: IconType;
export declare const PiSidebarSimpleThin: IconType;
export declare const PiSidebarThin: IconType;
export declare const PiSigmaThin: IconType;
export declare const PiSignInThin: IconType;
export declare const PiSignOutThin: IconType;
export declare const PiSignatureThin: IconType;
export declare const PiSignpostThin: IconType;
export declare const PiSimCardThin: IconType;
export declare const PiSirenThin: IconType;
export declare const PiSketchLogoThin: IconType;
export declare const PiSkipBackCircleThin: IconType;
export declare const PiSkipBackThin: IconType;
export declare const PiSkipForwardCircleThin: IconType;
export declare const PiSkipForwardThin: IconType;
export declare const PiSkullThin: IconType;
export declare const PiSkypeLogoThin: IconType;
export declare const PiSlackLogoThin: IconType;
export declare const PiSlidersHorizontalThin: IconType;
export declare const PiSlidersThin: IconType;
export declare const PiSlideshowThin: IconType;
export declare const PiSmileyAngryThin: IconType;
export declare const PiSmileyBlankThin: IconType;
export declare const PiSmileyMehThin: IconType;
export declare const PiSmileyMeltingThin: IconType;
export declare const PiSmileyNervousThin: IconType;
export declare const PiSmileySadThin: IconType;
export declare const PiSmileyStickerThin: IconType;
export declare const PiSmileyThin: IconType;
export declare const PiSmileyWinkThin: IconType;
export declare const PiSmileyXEyesThin: IconType;
export declare const PiSnapchatLogoThin: IconType;
export declare const PiSneakerMoveThin: IconType;
export declare const PiSneakerThin: IconType;
export declare const PiSnowflakeThin: IconType;
export declare const PiSoccerBallThin: IconType;
export declare const PiSockThin: IconType;
export declare const PiSolarPanelThin: IconType;
export declare const PiSolarRoofThin: IconType;
export declare const PiSortAscendingThin: IconType;
export declare const PiSortDescendingThin: IconType;
export declare const PiSoundcloudLogoThin: IconType;
export declare const PiSpadeThin: IconType;
export declare const PiSparkleThin: IconType;
export declare const PiSpeakerHifiThin: IconType;
export declare const PiSpeakerHighThin: IconType;
export declare const PiSpeakerLowThin: IconType;
export declare const PiSpeakerNoneThin: IconType;
export declare const PiSpeakerSimpleHighThin: IconType;
export declare const PiSpeakerSimpleLowThin: IconType;
export declare const PiSpeakerSimpleNoneThin: IconType;
export declare const PiSpeakerSimpleSlashThin: IconType;
export declare const PiSpeakerSimpleXThin: IconType;
export declare const PiSpeakerSlashThin: IconType;
export declare const PiSpeakerXThin: IconType;
export declare const PiSpeedometerThin: IconType;
export declare const PiSphereThin: IconType;
export declare const PiSpinnerBallThin: IconType;
export declare const PiSpinnerGapThin: IconType;
export declare const PiSpinnerThin: IconType;
export declare const PiSpiralThin: IconType;
export declare const PiSplitHorizontalThin: IconType;
export declare const PiSplitVerticalThin: IconType;
export declare const PiSpotifyLogoThin: IconType;
export declare const PiSprayBottleThin: IconType;
export declare const PiSquareHalfBottomThin: IconType;
export declare const PiSquareHalfThin: IconType;
export declare const PiSquareLogoThin: IconType;
export declare const PiSquareSplitHorizontalThin: IconType;
export declare const PiSquareSplitVerticalThin: IconType;
export declare const PiSquareThin: IconType;
export declare const PiSquaresFourThin: IconType;
export declare const PiStackMinusThin: IconType;
export declare const PiStackOverflowLogoThin: IconType;
export declare const PiStackPlusThin: IconType;
export declare const PiStackSimpleThin: IconType;
export declare const PiStackThin: IconType;
export declare const PiStairsThin: IconType;
export declare const PiStampThin: IconType;
export declare const PiStandardDefinitionThin: IconType;
export declare const PiStarAndCrescentThin: IconType;
export declare const PiStarFourThin: IconType;
export declare const PiStarHalfThin: IconType;
export declare const PiStarOfDavidThin: IconType;
export declare const PiStarThin: IconType;
export declare const PiSteamLogoThin: IconType;
export declare const PiSteeringWheelThin: IconType;
export declare const PiStepsThin: IconType;
export declare const PiStethoscopeThin: IconType;
export declare const PiStickerThin: IconType;
export declare const PiStoolThin: IconType;
export declare const PiStopCircleThin: IconType;
export declare const PiStopThin: IconType;
export declare const PiStorefrontThin: IconType;
export declare const PiStrategyThin: IconType;
export declare const PiStripeLogoThin: IconType;
export declare const PiStudentThin: IconType;
export declare const PiSubsetOfThin: IconType;
export declare const PiSubsetProperOfThin: IconType;
export declare const PiSubtitlesSlashThin: IconType;
export declare const PiSubtitlesThin: IconType;
export declare const PiSubtractSquareThin: IconType;
export declare const PiSubtractThin: IconType;
export declare const PiSubwayThin: IconType;
export declare const PiSuitcaseRollingThin: IconType;
export declare const PiSuitcaseSimpleThin: IconType;
export declare const PiSuitcaseThin: IconType;
export declare const PiSunDimThin: IconType;
export declare const PiSunHorizonThin: IconType;
export declare const PiSunThin: IconType;
export declare const PiSunglassesThin: IconType;
export declare const PiSupersetOfThin: IconType;
export declare const PiSupersetProperOfThin: IconType;
export declare const PiSwapThin: IconType;
export declare const PiSwatchesThin: IconType;
export declare const PiSwimmingPoolThin: IconType;
export declare const PiSwordThin: IconType;
export declare const PiSynagogueThin: IconType;
export declare const PiSyringeThin: IconType;
export declare const PiTShirtThin: IconType;
export declare const PiTableThin: IconType;
export declare const PiTabsThin: IconType;
export declare const PiTagChevronThin: IconType;
export declare const PiTagSimpleThin: IconType;
export declare const PiTagThin: IconType;
export declare const PiTargetThin: IconType;
export declare const PiTaxiThin: IconType;
export declare const PiTeaBagThin: IconType;
export declare const PiTelegramLogoThin: IconType;
export declare const PiTelevisionSimpleThin: IconType;
export declare const PiTelevisionThin: IconType;
export declare const PiTennisBallThin: IconType;
export declare const PiTentThin: IconType;
export declare const PiTerminalThin: IconType;
export declare const PiTerminalWindowThin: IconType;
export declare const PiTestTubeThin: IconType;
export declare const PiTextAUnderlineThin: IconType;
export declare const PiTextAaThin: IconType;
export declare const PiTextAlignCenterThin: IconType;
export declare const PiTextAlignJustifyThin: IconType;
export declare const PiTextAlignLeftThin: IconType;
export declare const PiTextAlignRightThin: IconType;
export declare const PiTextBThin: IconType;
export declare const PiTextColumnsThin: IconType;
export declare const PiTextHFiveThin: IconType;
export declare const PiTextHFourThin: IconType;
export declare const PiTextHOneThin: IconType;
export declare const PiTextHSixThin: IconType;
export declare const PiTextHThin: IconType;
export declare const PiTextHThreeThin: IconType;
export declare const PiTextHTwoThin: IconType;
export declare const PiTextIndentThin: IconType;
export declare const PiTextItalicThin: IconType;
export declare const PiTextOutdentThin: IconType;
export declare const PiTextStrikethroughThin: IconType;
export declare const PiTextSubscriptThin: IconType;
export declare const PiTextSuperscriptThin: IconType;
export declare const PiTextTSlashThin: IconType;
export declare const PiTextTThin: IconType;
export declare const PiTextUnderlineThin: IconType;
export declare const PiTextboxThin: IconType;
export declare const PiThermometerColdThin: IconType;
export declare const PiThermometerHotThin: IconType;
export declare const PiThermometerSimpleThin: IconType;
export declare const PiThermometerThin: IconType;
export declare const PiThreadsLogoThin: IconType;
export declare const PiThreeDThin: IconType;
export declare const PiThumbsDownThin: IconType;
export declare const PiThumbsUpThin: IconType;
export declare const PiTicketThin: IconType;
export declare const PiTidalLogoThin: IconType;
export declare const PiTiktokLogoThin: IconType;
export declare const PiTildeThin: IconType;
export declare const PiTimerThin: IconType;
export declare const PiTipJarThin: IconType;
export declare const PiTipiThin: IconType;
export declare const PiTireThin: IconType;
export declare const PiToggleLeftThin: IconType;
export declare const PiToggleRightThin: IconType;
export declare const PiToiletPaperThin: IconType;
export declare const PiToiletThin: IconType;
export declare const PiToolboxThin: IconType;
export declare const PiToothThin: IconType;
export declare const PiTornadoThin: IconType;
export declare const PiToteSimpleThin: IconType;
export declare const PiToteThin: IconType;
export declare const PiTowelThin: IconType;
export declare const PiTractorThin: IconType;
export declare const PiTrademarkRegisteredThin: IconType;
export declare const PiTrademarkThin: IconType;
export declare const PiTrafficConeThin: IconType;
export declare const PiTrafficSignThin: IconType;
export declare const PiTrafficSignalThin: IconType;
export declare const PiTrainRegionalThin: IconType;
export declare const PiTrainSimpleThin: IconType;
export declare const PiTrainThin: IconType;
export declare const PiTramThin: IconType;
export declare const PiTranslateThin: IconType;
export declare const PiTrashSimpleThin: IconType;
export declare const PiTrashThin: IconType;
export declare const PiTrayArrowDownThin: IconType;
export declare const PiTrayArrowUpThin: IconType;
export declare const PiTrayThin: IconType;
export declare const PiTreasureChestThin: IconType;
export declare const PiTreeEvergreenThin: IconType;
export declare const PiTreePalmThin: IconType;
export declare const PiTreeStructureThin: IconType;
export declare const PiTreeThin: IconType;
export declare const PiTreeViewThin: IconType;
export declare const PiTrendDownThin: IconType;
export declare const PiTrendUpThin: IconType;
export declare const PiTriangleDashedThin: IconType;
export declare const PiTriangleThin: IconType;
export declare const PiTrolleySuitcaseThin: IconType;
export declare const PiTrolleyThin: IconType;
export declare const PiTrophyThin: IconType;
export declare const PiTruckThin: IconType;
export declare const PiTruckTrailerThin: IconType;
export declare const PiTumblrLogoThin: IconType;
export declare const PiTwitchLogoThin: IconType;
export declare const PiTwitterLogoThin: IconType;
export declare const PiUmbrellaSimpleThin: IconType;
export declare const PiUmbrellaThin: IconType;
export declare const PiUnionThin: IconType;
export declare const PiUniteSquareThin: IconType;
export declare const PiUniteThin: IconType;
export declare const PiUploadSimpleThin: IconType;
export declare const PiUploadThin: IconType;
export declare const PiUsbThin: IconType;
export declare const PiUserCheckThin: IconType;
export declare const PiUserCircleCheckThin: IconType;
export declare const PiUserCircleDashedThin: IconType;
export declare const PiUserCircleGearThin: IconType;
export declare const PiUserCircleMinusThin: IconType;
export declare const PiUserCirclePlusThin: IconType;
export declare const PiUserCircleThin: IconType;
export declare const PiUserFocusThin: IconType;
export declare const PiUserGearThin: IconType;
export declare const PiUserListThin: IconType;
export declare const PiUserMinusThin: IconType;
export declare const PiUserPlusThin: IconType;
export declare const PiUserRectangleThin: IconType;
export declare const PiUserSoundThin: IconType;
export declare const PiUserSquareThin: IconType;
export declare const PiUserSwitchThin: IconType;
export declare const PiUserThin: IconType;
export declare const PiUsersFourThin: IconType;
export declare const PiUsersThin: IconType;
export declare const PiUsersThreeThin: IconType;
export declare const PiVanThin: IconType;
export declare const PiVaultThin: IconType;
export declare const PiVectorThreeThin: IconType;
export declare const PiVectorTwoThin: IconType;
export declare const PiVibrateThin: IconType;
export declare const PiVideoCameraSlashThin: IconType;
export declare const PiVideoCameraThin: IconType;
export declare const PiVideoConferenceThin: IconType;
export declare const PiVideoThin: IconType;
export declare const PiVignetteThin: IconType;
export declare const PiVinylRecordThin: IconType;
export declare const PiVirtualRealityThin: IconType;
export declare const PiVirusThin: IconType;
export declare const PiVisorThin: IconType;
export declare const PiVoicemailThin: IconType;
export declare const PiVolleyballThin: IconType;
export declare const PiWallThin: IconType;
export declare const PiWalletThin: IconType;
export declare const PiWarehouseThin: IconType;
export declare const PiWarningCircleThin: IconType;
export declare const PiWarningDiamondThin: IconType;
export declare const PiWarningOctagonThin: IconType;
export declare const PiWarningThin: IconType;
export declare const PiWashingMachineThin: IconType;
export declare const PiWatchThin: IconType;
export declare const PiWaveSawtoothThin: IconType;
export declare const PiWaveSineThin: IconType;
export declare const PiWaveSquareThin: IconType;
export declare const PiWaveTriangleThin: IconType;
export declare const PiWaveformSlashThin: IconType;
export declare const PiWaveformThin: IconType;
export declare const PiWavesThin: IconType;
export declare const PiWebcamSlashThin: IconType;
export declare const PiWebcamThin: IconType;
export declare const PiWebhooksLogoThin: IconType;
export declare const PiWechatLogoThin: IconType;
export declare const PiWhatsappLogoThin: IconType;
export declare const PiWheelchairMotionThin: IconType;
export declare const PiWheelchairThin: IconType;
export declare const PiWifiHighThin: IconType;
export declare const PiWifiLowThin: IconType;
export declare const PiWifiMediumThin: IconType;
export declare const PiWifiNoneThin: IconType;
export declare const PiWifiSlashThin: IconType;
export declare const PiWifiXThin: IconType;
export declare const PiWindThin: IconType;
export declare const PiWindmillThin: IconType;
export declare const PiWindowsLogoThin: IconType;
export declare const PiWineThin: IconType;
export declare const PiWrenchThin: IconType;
export declare const PiXCircleThin: IconType;
export declare const PiXLogoThin: IconType;
export declare const PiXSquareThin: IconType;
export declare const PiXThin: IconType;
export declare const PiYarnThin: IconType;
export declare const PiYinYangThin: IconType;
export declare const PiYoutubeLogoThin: IconType;
